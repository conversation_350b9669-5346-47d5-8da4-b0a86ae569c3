using System;
using UnityEngine;

namespace MoxieRobot.TTS.Core
{
    /// <summary>
    /// Configuración para síntesis de voz con ElevenLabs
    /// </summary>
    [Serializable]
    public class TTSSettings
    {
        [Header("Voice Settings")]
        [Range(0f, 1f)]
        [Tooltip("Controla la consistencia de la voz. Valores más altos = más estable")]
        public float stability = 0.5f;
        
        [Range(0f, 1f)]
        [Toolt<PERSON>("Controla qué tan similar es la voz al original")]
        public float similarityBoost = 0.5f;
        
        [Range(0f, 1f)]
        [Tooltip("Controla el estilo y expresividad de la voz")]
        public float style = 0.0f;
        
        [Tooltip("Mejora la claridad y calidad de la voz")]
        public bool useSpeakerBoost = true;
        
        [Header("Output Settings")]
        public OutputFormat outputFormat = OutputFormat.MP3_44100_128;
        
        [Range(0, 4)]
        [Tooltip("Optimización para streaming (0=calidad, 4=latencia)")]
        public int optimizeStreamingLatency = 0;
        
        [Header("Advanced")]
        [Range(0.1f, 2f)]
        [<PERSON>lt<PERSON>("Velocidad de reproducción")]
        public float speed = 1f;
        
        [Range(0.5f, 2f)]
        [Tooltip("Tono de la voz")]
        public float pitch = 1f;
        
        public TTSSettings()
        {
            // Valores por defecto optimizados para MoxieRobot
            stability = 0.6f;
            similarityBoost = 0.7f;
            style = 0.2f;
            useSpeakerBoost = true;
            outputFormat = OutputFormat.MP3_44100_128;
            optimizeStreamingLatency = 1;
            speed = 1f;
            pitch = 1f;
        }
        
        /// <summary>
        /// Crea configuración optimizada para conversación
        /// </summary>
        public static TTSSettings ForConversation()
        {
            return new TTSSettings
            {
                stability = 0.7f,
                similarityBoost = 0.8f,
                style = 0.3f,
                useSpeakerBoost = true,
                optimizeStreamingLatency = 2
            };
        }
        
        /// <summary>
        /// Crea configuración optimizada para narración
        /// </summary>
        public static TTSSettings ForNarration()
        {
            return new TTSSettings
            {
                stability = 0.8f,
                similarityBoost = 0.6f,
                style = 0.1f,
                useSpeakerBoost = true,
                optimizeStreamingLatency = 0
            };
        }
        
        /// <summary>
        /// Crea configuración optimizada para expresividad
        /// </summary>
        public static TTSSettings ForExpressive()
        {
            return new TTSSettings
            {
                stability = 0.4f,
                similarityBoost = 0.9f,
                style = 0.8f,
                useSpeakerBoost = true,
                optimizeStreamingLatency = 1
            };
        }
    }
    
    /// <summary>
    /// Formatos de salida soportados por ElevenLabs
    /// </summary>
    public enum OutputFormat
    {
        MP3_22050_32,
        MP3_44100_32,
        MP3_44100_64,
        MP3_44100_96,
        MP3_44100_128,
        MP3_44100_192,
        PCM_16000,
        PCM_22050,
        PCM_24000,
        PCM_44100,
        ULAW_8000
    }
    
    /// <summary>
    /// Datos de streaming TTS
    /// </summary>
    [Serializable]
    public class TTSStreamData
    {
        public byte[] audioData;
        public float timestamp;
        public bool isComplete;
        public string error;
        
        public TTSStreamData(byte[] data, float time, bool complete = false, string errorMsg = null)
        {
            audioData = data;
            timestamp = time;
            isComplete = complete;
            error = errorMsg;
        }
    }
    
    /// <summary>
    /// Información de una voz
    /// </summary>
    [Serializable]
    public class VoiceInfo
    {
        public string voiceId;
        public string name;
        public string category;
        public string description;
        public string[] labels;
        public string previewUrl;
        public VoiceSettings settings;
        
        public VoiceInfo(string id, string voiceName)
        {
            voiceId = id;
            name = voiceName;
            settings = new VoiceSettings();
        }
    }
    
    /// <summary>
    /// Configuración específica de una voz
    /// </summary>
    [Serializable]
    public class VoiceSettings
    {
        public float stability = 0.5f;
        public float similarityBoost = 0.5f;
        public float style = 0.0f;
        public bool useSpeakerBoost = true;
    }
}
