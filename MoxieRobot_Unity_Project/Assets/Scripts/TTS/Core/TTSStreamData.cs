using System;
using UnityEngine;
using MoxieRobot.TTS.Visemes;

namespace MoxieRobot.TTS.Core
{
    /// <summary>
    /// Datos de streaming de TTS para compatibilidad con sistema anterior
    /// </summary>
    [Serializable]
    public class TTSStreamData
    {
        [Header("Audio Data")]
        public AudioClip audioClip;
        public byte[] audioBytes;
        public float duration;
        
        [Header("Text Information")]
        public string originalText;
        public string processedText;
        public string voiceId;
        public string language;
        
        [Header("Timing")]
        public float timestamp;
        public float startTime;
        public float endTime;
        
        [Header("Viseme Data")]
        public VisemeData[] visemes;
        public int currentVisemeIndex;
        
        [Header("Metadata")]
        public bool isComplete;
        public bool hasError;
        public string errorMessage;
        public float progress; // 0.0 to 1.0
        
        public TTSStreamData()
        {
            audioClip = null;
            audioBytes = null;
            duration = 0f;
            originalText = "";
            processedText = "";
            voiceId = "";
            language = "en";
            timestamp = 0f;
            startTime = 0f;
            endTime = 0f;
            visemes = new VisemeData[0];
            currentVisemeIndex = 0;
            isComplete = false;
            hasError = false;
            errorMessage = "";
            progress = 0f;
        }
        
        public TTSStreamData(string text, string voice = "")
        {
            originalText = text;
            processedText = text;
            voiceId = voice;
            language = "en";
            timestamp = Time.time;
            startTime = 0f;
            endTime = 0f;
            visemes = new VisemeData[0];
            currentVisemeIndex = 0;
            isComplete = false;
            hasError = false;
            errorMessage = "";
            progress = 0f;
            audioClip = null;
            audioBytes = null;
            duration = 0f;
        }
        
        /// <summary>
        /// Marca el stream como completado
        /// </summary>
        public void MarkComplete()
        {
            isComplete = true;
            progress = 1f;
            if (audioClip != null)
            {
                duration = audioClip.length;
                endTime = startTime + duration;
            }
        }
        
        /// <summary>
        /// Marca el stream con error
        /// </summary>
        public void MarkError(string error)
        {
            hasError = true;
            errorMessage = error;
            isComplete = true;
        }
        
        /// <summary>
        /// Actualiza el progreso del stream
        /// </summary>
        public void UpdateProgress(float newProgress)
        {
            progress = Mathf.Clamp01(newProgress);
        }
        
        /// <summary>
        /// Obtiene el visema actual basado en el tiempo
        /// </summary>
        public VisemeData GetCurrentViseme(float currentTime)
        {
            if (visemes == null || visemes.Length == 0)
                return null;
                
            float relativeTime = currentTime - startTime;
            
            for (int i = 0; i < visemes.Length; i++)
            {
                if (relativeTime >= visemes[i].timestamp && 
                    relativeTime <= visemes[i].timestamp + visemes[i].duration)
                {
                    currentVisemeIndex = i;
                    return visemes[i];
                }
            }
            
            return null;
        }
        
        /// <summary>
        /// Verifica si el stream está activo
        /// </summary>
        public bool IsActive(float currentTime)
        {
            return currentTime >= startTime && currentTime <= endTime && !hasError;
        }
        
        public override string ToString()
        {
            return $"TTSStreamData: '{originalText}' ({progress:P0}) - Voice: {voiceId}";
        }
    }
}
