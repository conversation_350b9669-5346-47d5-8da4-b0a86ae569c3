using System;
using System.Collections;
using UnityEngine;
using MoxieRobot.TTS.ElevenLabs;
using MoxieRobot.TTS.Visemes;

namespace MoxieRobot.TTS.Core
{
    /// <summary>
    /// Adaptador de compatibilidad para mantener la interfaz del sistema TTS anterior
    /// Permite migración gradual sin romper código existente
    /// </summary>
    public class TTSCompatibilityAdapter : MonoBehaviour
    {
        [Header("Components")]
        [SerializeField] private ElevenLabsTTSManager elevenLabsManager;
        
        [Header("Legacy Compatibility")]
        [SerializeField] private bool enableLegacyEvents = true;
        [SerializeField] private bool logLegacyCalls = false;
        
        // Eventos legacy (mantener compatibilidad)
        public static event Action<AudioClip> OnTTSCompleteEvent;
        public static event Action<TTSStreamData> OnTTSStreamEvent;
        public static event Action<string> OnTTSError;
        
        // Referencias estáticas para compatibilidad
        private static TTSCompatibilityAdapter instance;
        
        // Estados legacy
        private bool isInitialized = false;
        private Coroutine currentTTSCoroutine;
        
        private void Awake()
        {
            // Singleton para compatibilidad
            if (instance == null)
            {
                instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else
            {
                Destroy(gameObject);
                return;
            }
            
            InitializeComponents();
        }
        
        private void Start()
        {
            Initialize();
        }
        
        /// <summary>
        /// Inicializa el adaptador de compatibilidad
        /// </summary>
        public void Initialize()
        {
            if (isInitialized) return;
            
            if (elevenLabsManager != null)
            {
                // Suscribirse a eventos del nuevo sistema
                elevenLabsManager.OnTTSCompleteEvent += HandleTTSComplete;
                elevenLabsManager.OnTTSStreamEvent += HandleTTSStream;
                elevenLabsManager.OnTTSError += HandleTTSError;
                elevenLabsManager.OnVisemeEvent += HandleVisemeEvent;
                
                isInitialized = true;
                Debug.Log("TTS Compatibility Adapter initialized");
            }
            else
            {
                Debug.LogError("ElevenLabsTTSManager not found! Please assign it in the inspector.");
            }
        }
        
        #region Legacy Static Methods (Compatibilidad con código anterior)
        
        /// <summary>
        /// Método legacy: TTSRequestCloudHandler.SpeakText()
        /// </summary>
        public static void SpeakText(string text)
        {
            if (instance != null && instance.elevenLabsManager != null)
            {
                if (instance.logLegacyCalls)
                    Debug.Log($"Legacy TTS call: SpeakText(\"{text}\")");
                    
                instance.elevenLabsManager.SpeakText(text);
            }
            else
            {
                Debug.LogError("TTS system not initialized");
            }
        }
        
        /// <summary>
        /// Método legacy: TTSRequestCloudHandler.SpeakTextWithVoice()
        /// </summary>
        public static void SpeakTextWithVoice(string text, string voiceId)
        {
            if (instance != null && instance.elevenLabsManager != null)
            {
                if (instance.logLegacyCalls)
                    Debug.Log($"Legacy TTS call: SpeakTextWithVoice(\"{text}\", \"{voiceId}\")");
                    
                instance.elevenLabsManager.SpeakText(text, voiceId);
            }
            else
            {
                Debug.LogError("TTS system not initialized");
            }
        }
        
        /// <summary>
        /// Método legacy: TTSRequestCloudHandler.StopTTS()
        /// </summary>
        public static void StopTTS()
        {
            if (instance != null && instance.elevenLabsManager != null)
            {
                if (instance.logLegacyCalls)
                    Debug.Log("Legacy TTS call: StopTTS()");
                    
                instance.elevenLabsManager.StopSpeaking();
            }
        }
        
        /// <summary>
        /// Método legacy: TTSRequestCloudHandler.IsSpeaking
        /// </summary>
        public static bool IsSpeaking
        {
            get
            {
                if (instance != null && instance.elevenLabsManager != null)
                {
                    return instance.elevenLabsManager.IsSpeaking;
                }
                return false;
            }
        }
        
        /// <summary>
        /// Método legacy: TTSRequestCloudHandler.SetLanguage()
        /// </summary>
        public static void SetLanguage(string languageCode)
        {
            if (instance != null && instance.elevenLabsManager != null)
            {
                if (instance.logLegacyCalls)
                    Debug.Log($"Legacy TTS call: SetLanguage(\"{languageCode}\")");
                
                // Convertir código de idioma legacy a enum
                SupportedLanguage language = ConvertLanguageCode(languageCode);
                instance.elevenLabsManager.SetLanguage(language);
            }
        }
        
        /// <summary>
        /// Método legacy: TTSRequestCloudHandler.SetVoice()
        /// </summary>
        public static void SetVoice(string voiceId)
        {
            if (instance != null && instance.elevenLabsManager != null)
            {
                if (instance.logLegacyCalls)
                    Debug.Log($"Legacy TTS call: SetVoice(\"{voiceId}\")");
                    
                instance.elevenLabsManager.SetVoice(voiceId);
            }
        }
        
        #endregion
        
        #region Legacy Event Handlers
        
        private void HandleTTSComplete(AudioClip audioClip)
        {
            if (enableLegacyEvents)
            {
                OnTTSCompleteEvent?.Invoke(audioClip);
            }
        }
        
        private void HandleTTSStream(TTSStreamData streamData)
        {
            if (enableLegacyEvents)
            {
                OnTTSStreamEvent?.Invoke(streamData);
            }
        }
        
        private void HandleTTSError(string error)
        {
            if (enableLegacyEvents)
            {
                OnTTSError?.Invoke(error);
            }
        }
        
        private void HandleVisemeEvent(VisemeData viseme)
        {
            // Convertir visema a formato legacy si es necesario
            // En el sistema anterior, los visemas se manejaban diferente
            
            if (enableLegacyEvents)
            {
                // Emitir evento legacy de visema si existe
                // LegacyVisemeEvent?.Invoke(ConvertToLegacyViseme(viseme));
            }
        }
        
        #endregion
        
        #region Utility Methods
        
        /// <summary>
        /// Convierte códigos de idioma legacy al nuevo enum
        /// </summary>
        private static SupportedLanguage ConvertLanguageCode(string languageCode)
        {
            switch (languageCode.ToLower())
            {
                case "en":
                case "en-us":
                case "english":
                    return SupportedLanguage.English;
                case "es":
                case "es-es":
                case "spanish":
                    return SupportedLanguage.Spanish;
                case "fr":
                case "fr-fr":
                case "french":
                    return SupportedLanguage.French;
                case "de":
                case "de-de":
                case "german":
                    return SupportedLanguage.German;
                case "it":
                case "it-it":
                case "italian":
                    return SupportedLanguage.Italian;
                case "pt":
                case "pt-pt":
                case "portuguese":
                    return SupportedLanguage.Portuguese;
                case "ja":
                case "ja-jp":
                case "japanese":
                    return SupportedLanguage.Japanese;
                case "ko":
                case "ko-kr":
                case "korean":
                    return SupportedLanguage.Korean;
                case "zh":
                case "zh-cn":
                case "chinese":
                    return SupportedLanguage.Chinese;
                case "ru":
                case "ru-ru":
                case "russian":
                    return SupportedLanguage.Russian;
                case "ar":
                case "ar-sa":
                case "arabic":
                    return SupportedLanguage.Arabic;
                case "hi":
                case "hi-in":
                case "hindi":
                    return SupportedLanguage.Hindi;
                default:
                    Debug.LogWarning($"Unknown language code: {languageCode}, defaulting to English");
                    return SupportedLanguage.English;
            }
        }
        
        /// <summary>
        /// Obtiene la instancia del adaptador
        /// </summary>
        public static TTSCompatibilityAdapter GetInstance()
        {
            return instance;
        }
        
        /// <summary>
        /// Verifica si el sistema TTS está disponible
        /// </summary>
        public static bool IsAvailable()
        {
            return instance != null && instance.isInitialized && instance.elevenLabsManager != null;
        }
        
        #endregion
        
        #region Legacy Class Emulation
        
        /// <summary>
        /// Clase legacy emulada: TTSRequestCloudHandler
        /// Mantiene compatibilidad con código que instancia esta clase
        /// </summary>
        public class TTSRequestCloudHandler
        {
            public static void SpeakText(string text)
            {
                TTSCompatibilityAdapter.SpeakText(text);
            }
            
            public static void StopTTS()
            {
                TTSCompatibilityAdapter.StopTTS();
            }
            
            public static bool IsSpeaking => TTSCompatibilityAdapter.IsSpeaking;
            
            // Eventos legacy
            public static event Action<AudioClip> OnTTSCompleteEvent
            {
                add { TTSCompatibilityAdapter.OnTTSCompleteEvent += value; }
                remove { TTSCompatibilityAdapter.OnTTSCompleteEvent -= value; }
            }
            
            public static event Action<TTSStreamData> OnTTSStreamEvent
            {
                add { TTSCompatibilityAdapter.OnTTSStreamEvent += value; }
                remove { TTSCompatibilityAdapter.OnTTSStreamEvent -= value; }
            }
        }
        
        /// <summary>
        /// Clase legacy emulada: TTSSpeechMarkPlayback
        /// </summary>
        public class TTSSpeechMarkPlayback
        {
            public static void PlayWithVisemes(AudioClip clip, VisemeData[] visemes)
            {
                if (instance != null)
                {
                    instance.StartCoroutine(instance.PlayWithVisemesCoroutine(clip, visemes));
                }
            }
        }
        
        #endregion
        
        private IEnumerator PlayWithVisemesCoroutine(AudioClip clip, VisemeData[] visemes)
        {
            // Implementación legacy de reproducción con visemas
            if (clip != null && visemes != null)
            {
                var audioSource = GetComponent<AudioSource>();
                if (audioSource == null)
                    audioSource = gameObject.AddComponent<AudioSource>();
                
                audioSource.clip = clip;
                audioSource.Play();
                
                // Reproducir visemas sincronizados
                float startTime = Time.time;
                int currentVisemeIndex = 0;
                
                while (audioSource.isPlaying && currentVisemeIndex < visemes.Length)
                {
                    float elapsed = Time.time - startTime;
                    
                    if (currentVisemeIndex < visemes.Length && elapsed >= visemes[currentVisemeIndex].timestamp)
                    {
                        HandleVisemeEvent(visemes[currentVisemeIndex]);
                        currentVisemeIndex++;
                    }
                    
                    yield return null;
                }
            }
        }
        
        private void InitializeComponents()
        {
            if (elevenLabsManager == null)
            {
                elevenLabsManager = GetComponent<ElevenLabsTTSManager>();
                
                if (elevenLabsManager == null)
                {
                    elevenLabsManager = FindObjectOfType<ElevenLabsTTSManager>();
                }
            }
        }
        
        private void OnDestroy()
        {
            if (elevenLabsManager != null)
            {
                elevenLabsManager.OnTTSCompleteEvent -= HandleTTSComplete;
                elevenLabsManager.OnTTSStreamEvent -= HandleTTSStream;
                elevenLabsManager.OnTTSError -= HandleTTSError;
                elevenLabsManager.OnVisemeEvent -= HandleVisemeEvent;
            }
            
            if (instance == this)
            {
                instance = null;
            }
        }
    }
}
