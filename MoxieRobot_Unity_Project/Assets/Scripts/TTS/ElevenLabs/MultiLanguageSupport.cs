using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using MoxieRobot.TTS.Core;

namespace MoxieRobot.TTS.ElevenLabs
{
    /// <summary>
    /// Idiomas soportados por el sistema TTS
    /// </summary>
    public enum SupportedLanguage
    {
        English,
        Spanish,
        French,
        German,
        Italian,
        Portuguese,
        Japanese,
        Korean,
        Chinese,
        Russian,
        Arabic,
        Hindi,
        Dutch,
        Polish,
        Turkish
    }
    
    /// <summary>
    /// Perfil de voz con información de idioma
    /// </summary>
    [Serializable]
    public class VoiceProfile
    {
        public string voiceId;
        public string name;
        public VoiceGender gender;
        public VoiceAge age;
        public SupportedLanguage[] supportedLanguages;
        public VoiceStyle[] supportedStyles;
        public TTSSettings defaultSettings;
        public bool isCustomVoice;
        public string previewUrl;
        public string description;
        
        public VoiceProfile(string id, string voiceName)
        {
            voiceId = id;
            name = voiceName;
            gender = VoiceGender.Any;
            age = VoiceAge.Any;
            supportedLanguages = new[] { SupportedLanguage.English };
            supportedStyles = new[] { VoiceStyle.Conversational };
            defaultSettings = new TTSSettings();
            isCustomVoice = false;
        }
        
        public bool SupportsLanguage(SupportedLanguage language)
        {
            return supportedLanguages.Contains(language);
        }
        
        public bool SupportsStyle(VoiceStyle style)
        {
            return supportedStyles.Contains(style);
        }
    }
    
    /// <summary>
    /// Características de voz
    /// </summary>
    public enum VoiceGender { Male, Female, Any }
    public enum VoiceAge { Child, Young, Adult, Senior, Any }
    public enum VoiceStyle { Conversational, Narration, Expressive, Calm, Energetic, Friendly, Professional }
    
    /// <summary>
    /// Gestión de soporte multiidioma
    /// </summary>
    public class MultiLanguageSupport : MonoBehaviour
    {
        [Header("Language Configuration")]
        [SerializeField] private SupportedLanguage currentLanguage = SupportedLanguage.English;
        [SerializeField] private bool autoDetectLanguage = true;
        
        [Header("Voice Mapping")]
        [SerializeField] private LanguageVoiceMapping[] languageVoiceMappings;
        
        // Cache de voces por idioma
        private Dictionary<SupportedLanguage, List<VoiceProfile>> languageVoices;
        private Dictionary<string, SupportedLanguage> languageDetectionCache;
        
        // Eventos
        public event Action<SupportedLanguage> OnLanguageChanged;
        
        // Propiedades
        public SupportedLanguage CurrentLanguage => currentLanguage;
        public bool AutoDetectLanguage => autoDetectLanguage;
        
        private void Awake()
        {
            InitializeLanguageSupport();
        }
        
        private void InitializeLanguageSupport()
        {
            languageVoices = new Dictionary<SupportedLanguage, List<VoiceProfile>>();
            languageDetectionCache = new Dictionary<string, SupportedLanguage>();
            
            // Inicializar mapeos por defecto
            InitializeDefaultVoiceMappings();
        }
        
        /// <summary>
        /// Establece el idioma actual
        /// </summary>
        public void SetLanguage(SupportedLanguage language)
        {
            if (currentLanguage != language)
            {
                currentLanguage = language;
                OnLanguageChanged?.Invoke(language);
                Debug.Log($"Language changed to: {language}");
            }
        }
        
        /// <summary>
        /// Detecta el idioma de un texto
        /// </summary>
        public SupportedLanguage DetectLanguage(string text)
        {
            if (string.IsNullOrEmpty(text))
                return currentLanguage;
            
            // Verificar cache
            if (languageDetectionCache.ContainsKey(text))
                return languageDetectionCache[text];
            
            // Detección simple basada en patrones
            SupportedLanguage detectedLanguage = DetectLanguageByPatterns(text);
            
            // Cachear resultado
            languageDetectionCache[text] = detectedLanguage;
            
            return detectedLanguage;
        }
        
        private SupportedLanguage DetectLanguageByPatterns(string text)
        {
            text = text.ToLower();
            
            // Patrones simples para detección de idioma
            var patterns = new Dictionary<SupportedLanguage, string[]>
            {
                { SupportedLanguage.Spanish, new[] { "el ", "la ", "de ", "que ", "y ", "es ", "en ", "un ", "ser ", "se ", "no ", "te ", "lo ", "le ", "da ", "su ", "por ", "son ", "con ", "para ", "al ", "del ", "los ", "las ", "una ", "está ", "todo ", "también ", "fue ", "había ", "muy ", "sin ", "sobre ", "mi ", "sí ", "qué ", "yo ", "cuando ", "él ", "más ", "si ", "ya ", "pero ", "puede ", "hasta ", "donde ", "quien ", "desde ", "todos ", "durante ", "tanto ", "menos ", "según ", "entre " } },
                { SupportedLanguage.French, new[] { "le ", "de ", "et ", "à ", "un ", "il ", "être ", "et ", "en ", "avoir ", "que ", "pour ", "dans ", "ce ", "son ", "une ", "sur ", "avec ", "ne ", "se ", "pas ", "tout ", "plus ", "par ", "grand ", "en ", "une ", "être ", "et ", "à ", "il ", "avoir ", "ne ", "je ", "son ", "que ", "se ", "qui ", "ce ", "dans ", "en ", "du ", "elle ", "au ", "de ", "le ", "un ", "à " } },
                { SupportedLanguage.German, new[] { "der ", "die ", "und ", "in ", "den ", "von ", "zu ", "das ", "mit ", "sich ", "des ", "auf ", "für ", "ist ", "im ", "dem ", "nicht ", "ein ", "eine ", "als ", "auch ", "es ", "an ", "werden ", "aus ", "er ", "hat ", "dass ", "sie ", "nach ", "wird ", "bei ", "einer ", "um ", "am ", "sind ", "noch ", "wie ", "einem ", "über ", "einen ", "so ", "zum ", "war ", "haben ", "nur ", "oder ", "aber ", "vor ", "zur ", "bis ", "mehr ", "durch ", "man ", "sein ", "wurde ", "sei " } },
                { SupportedLanguage.Italian, new[] { "il ", "di ", "che ", "e ", "la ", "per ", "un ", "in ", "con ", "del ", "da ", "a ", "al ", "le ", "si ", "dei ", "come ", "io ", "questo ", "ho ", "lo ", "tutto ", "lei ", "molto ", "me ", "contro ", "tutti ", "nel ", "sopra ", "quella ", "mi ", "dopo ", "anche ", "due ", "quanto ", "sopra ", "loro ", "dove ", "chi ", "tempo ", "più ", "ora ", "mai ", "casa ", "sotto ", "cosa " } },
                { SupportedLanguage.Portuguese, new[] { "o ", "de ", "a ", "e ", "do ", "da ", "em ", "um ", "para ", "é ", "com ", "não ", "uma ", "os ", "no ", "se ", "na ", "por ", "mais ", "as ", "dos ", "como ", "mas ", "foi ", "ao ", "ele ", "das ", "tem ", "à ", "seu ", "sua ", "ou ", "ser ", "quando ", "muito ", "há ", "nos ", "já ", "está ", "eu ", "também ", "só ", "pelo ", "pela ", "até ", "isso ", "ela ", "entre ", "era ", "depois ", "sem ", "mesmo ", "aos ", "ter ", "seus ", "suas " } },
                { SupportedLanguage.Japanese, new[] { "の", "に", "は", "を", "た", "が", "で", "て", "と", "し", "れ", "さ", "ある", "いる", "も", "する", "から", "な", "こと", "として", "い", "や", "れる", "など", "なっ", "ない", "この", "ため", "その", "あっ", "よう", "また", "もの", "という", "あり", "まで", "られ", "なる", "へ", "か", "だ", "これ", "によって", "により", "おり", "より", "による", "ず", "なり", "られる", "において", "ば", "なかっ", "なく", "しかし", "について", "せ", "だっ", "その後", "できる", "それ" } },
                { SupportedLanguage.Korean, new[] { "이", "의", "가", "을", "들", "에", "는", "와", "한", "하다", "있다", "되다", "하는", "과", "도", "를", "으로", "자", "에게", "보다", "때", "때문에", "같은", "통해", "그", "수", "있는", "등", "들을", "대한", "그런", "이런", "그것", "것", "수도", "아니다", "그리고", "또한", "하지만", "그러나", "따라서", "또는", "및", "그래서", "왜냐하면", "만약", "비록", "아직", "이미", "곧", "항상", "가끔", "자주", "보통", "아마", "정말", "매우", "너무", "조금", "많이", "모두", "각", "어떤", "이런", "저런", "새로운", "오래된", "좋은", "나쁜", "큰", "작은", "높은", "낮은", "빠른", "느린" } },
                { SupportedLanguage.Chinese, new[] { "的", "一", "是", "在", "不", "了", "有", "和", "人", "这", "中", "大", "为", "上", "个", "国", "我", "以", "要", "他", "时", "来", "用", "们", "生", "到", "作", "地", "于", "出", "就", "分", "对", "成", "会", "可", "主", "发", "年", "动", "同", "工", "也", "能", "下", "过", "子", "说", "产", "种", "面", "而", "方", "后", "多", "定", "行", "学", "法", "所", "民", "得", "经", "十", "三", "之", "进", "着", "等", "部", "度", "家", "电", "力", "里", "如", "水", "化", "高", "自", "二", "理", "起", "小", "物", "现", "实", "加", "量", "都", "两", "体", "制", "机", "当", "使", "点", "从", "业", "本", "去", "把", "性", "好", "应", "开", "它", "合", "还", "因", "由", "其", "些", "然", "前", "外", "天", "政", "四", "日", "那", "社", "义", "事", "平", "形", "相", "全", "表", "间", "样", "与", "关", "各", "重", "新", "线", "内", "数", "正", "心", "反", "你", "明", "看", "原", "又", "么", "利", "比", "或", "但", "质", "气", "第", "向", "道", "命", "此", "变", "条", "只", "没", "结", "解", "问", "意", "建", "月", "公", "无", "系", "军", "很", "情", "者", "最", "立", "代", "想", "已", "通", "并", "提", "直", "题", "党", "程", "展", "五", "果", "料", "象", "员", "革", "位", "入", "常", "文", "总", "次", "品", "式", "活", "设", "及", "管", "特", "件", "长", "求", "老", "头", "基", "资", "边", "流", "路", "级", "少", "图", "山", "统", "接", "知", "较", "将", "组", "见", "计", "别", "她", "手", "角", "期", "根", "论", "运", "农", "指", "几", "九", "区", "强", "放", "决", "西", "被", "干", "做", "必", "战", "先", "回", "则", "任", "取", "据", "处", "队", "南", "给", "色", "光", "门", "即", "保", "治", "北", "造", "百", "规", "热", "领", "七", "海", "口", "东", "导", "器", "压", "志", "世", "金", "增", "争", "济", "阶", "油", "思", "术", "极", "交", "受", "联", "什", "认", "六", "共", "权", "收", "证", "改", "清", "美", "再", "采", "转", "更", "单", "风", "切", "打", "白", "教", "速", "花", "带", "安", "场", "身", "车", "例", "真", "务", "具", "万", "每", "目", "至", "达", "走", "积", "示", "议", "声", "报", "斗", "完", "类", "八", "离", "华", "名", "确", "才", "科", "张", "信", "马", "节", "话", "米", "整", "空", "元", "况", "今", "集", "温", "传", "土", "许", "步", "群", "广", "石", "记", "需", "段", "研", "界", "拉", "林", "律", "叫", "且", "究", "观", "越", "织", "装", "影", "算", "低", "持", "音", "众", "书", "布", "复", "容", "儿", "须", "际", "商", "非", "验", "连", "断", "深", "难", "近", "矿", "千", "周", "委", "素", "技", "备", "半", "办", "青", "省", "列", "习", "响", "约", "支", "般", "史", "感", "劳", "便", "团", "往", "酸", "历", "市", "克", "何", "除", "消", "构", "府", "称", "太", "准", "精", "值", "号", "率", "族", "维", "划", "选", "标", "写", "存", "候", "毛", "亲", "快", "效", "斯", "院", "查", "江", "型", "眼", "王", "按", "格", "养", "易", "置", "派", "层", "片", "始", "却", "专", "状", "育", "厂", "京", "识", "适", "属", "圆", "包", "火", "住", "调", "满", "县", "局", "照", "参", "红", "细", "引", "听", "该", "铁", "价", "严", "首", "底", "液", "官", "德", "随", "病", "苏", "失", "尔", "死", "讲", "配", "女", "黄", "推", "显", "谈", "罪", "神", "艺", "呢", "席", "含", "企", "望", "密", "批", "营", "项", "防", "举", "球", "英", "氧", "势", "告", "李", "台", "落", "木", "帮", "轮", "破", "亚", "师", "围", "注", "远", "字", "材", "排", "供", "河", "态", "封", "另", "施", "减", "树", "溶", "怎", "止", "案", "言", "士", "均", "武", "固", "叶", "鱼", "波", "视", "仅", "费", "紧", "爱", "左", "章", "早", "朝", "害", "续", "轻", "服", "试", "食", "充", "兵", "源", "判", "护", "司", "足", "某", "练", "差", "致", "板", "田", "降", "黑", "犯", "负", "击", "范", "继", "兴", "似", "余", "坚", "曲", "输", "修", "故", "城", "夫", "够", "送", "笔", "船", "占", "右", "财", "吃", "富", "春", "职", "觉", "汉", "画", "功", "巴", "跟", "虽", "杂", "飞", "检", "吸", "助", "升", "阳", "互", "初", "创", "抗", "考", "投", "坏", "策", "古", "径", "换", "未", "跑", "留", "钢", "曾", "端", "责", "站", "简", "述", "钱", "副", "尽", "帝", "射", "草", "冲", "承", "独", "令", "限", "阿", "宣", "环", "双", "请", "超", "微", "让", "控", "州", "良", "轴", "找", "否", "纪", "益", "依", "优", "顶", "础", "载", "倒", "房", "突", "坐", "粉", "敌", "略", "客", "袁", "冷", "胜", "绝", "析", "块", "剂", "测", "丝", "协", "诉", "念", "陈", "仍", "罗", "盐", "友", "洋", "错", "苦", "夜", "刑", "移", "频", "逐", "靠", "混", "母", "短", "皮", "终", "聚", "汽", "村", "云", "哪", "既", "距", "卫", "停", "烈", "央", "察", "烧", "迅", "境", "若", "印", "洲", "刻", "括", "激", "孔", "搞", "甚", "室", "待", "核", "校", "散", "侵", "吧", "甲", "游", "久", "菜", "味", "旧", "模", "湖", "货", "损", "预", "阻", "毫", "普", "稳", "乙", "妈", "植", "息", "扩", "银", "语", "挥", "酒", "守", "拿", "序", "纸", "医", "缺", "雨", "吗", "针", "刘", "啊", "急", "唱", "误", "训", "愿", "审", "附", "获", "茶", "鲜", "粮", "斤", "孩", "脱", "硫", "肥", "善", "龙", "演", "父", "渐", "血", "欢", "械", "掌", "歌", "沙", "刚", "攻", "谓", "盾", "讨", "晚", "粒", "乱", "燃", "矛", "乎", "杀", "药", "宁", "鲁", "贵", "钟", "煤", "读", "班", "伯", "香", "介", "迫", "句", "丰", "培", "握", "兰", "担", "弦", "蛋", "沉", "假", "穿", "执", "答", "乐", "谁", "顺", "烟", "缩", "征", "脸", "喜", "松", "脚", "困", "异", "免", "背", "星", "福", "买", "染", "井", "概", "慢", "怕", "磁", "倍", "祖", "皇", "促", "静", "补", "评", "翻", "肉", "践", "尼", "衣", "宽", "扬", "棉", "希", "伤", "操", "垂", "秋", "宜", "氢", "套", "督", "振", "架", "亮", "末", "宪", "庆", "编", "牛", "触", "映", "雷", "销", "诗", "座", "居", "抓", "裂", "胞", "呼", "娘", "景", "威", "绿", "晶", "厚", "盟", "衡", "鸡", "孙", "延", "危", "胶", "屋", "乡", "临", "陆", "顾", "掉", "呀", "灯", "岁", "措", "束", "耐", "剧", "玉", "赵", "跳", "哥", "季", "课", "凯", "胡", "额", "款", "绍", "卷", "齐", "伟", "蒸", "殖", "永", "宗", "苗", "川", "炉", "岩", "弱", "零", "杨", "奏", "沿", "露", "杆", "探", "滑", "镇", "饭", "浓", "航", "怀", "赶", "库", "夺", "伊", "灵", "税", "途", "灭", "赛", "归", "召", "鼓", "播", "盘", "裁", "险", "康", "唯", "录", "菌", "纯", "借", "糖", "盖", "横", "符", "私", "努", "堂", "域", "枪", "润", "幅", "哈", "竟", "熟", "虫", "泽", "脑", "壤", "碳", "欧", "遍", "侧", "寨", "敢", "彻", "虑", "斜", "薄", "庭", "纳", "弹", "饲", "伸", "折", "麦", "湿", "暗", "荷", "瓦", "塞", "床", "筑", "恶", "户", "访", "塔", "奇", "透", "梁", "刀", "旋", "迹", "卡", "氯", "遇", "份", "毒", "泥", "退", "洗", "摆", "灰", "彩", "卖", "耗", "夏", "择", "忙", "铜", "献", "硬", "予", "繁", "圈", "雪", "函", "亦", "抽", "篇", "阵", "阴", "丁", "尺", "追", "堆", "雄", "迎", "泛", "爸", "楼", "避", "谋", "吨", "野", "猪", "旗", "累", "偏", "典", "馆", "索", "秦", "脂", "潮", "爷", "豆", "忽", "托", "惊", "塑", "遗", "愈", "朱", "替", "纤", "粗", "倾", "尚", "痛", "楚", "谢", "奋", "购", "磨", "君", "池", "旁", "碎", "骨", "监", "捕", "弟", "暴", "割", "贯", "殊", "释", "词", "亡", "壁", "顿", "宝", "午", "尘", "闻", "揭", "炮", "残", "冬", "桥", "妇", "警", "综", "招", "吴", "付", "浮", "遭", "徐", "您", "摇", "谷", "赞", "箱", "隔", "订", "男", "吹", "园", "纷", "唐", "败", "宋", "玻", "巨", "耕", "坦", "荣", "闭", "湾", "键", "凡", "驻", "锅", "救", "恩", "剥", "凝", "碱", "齿", "截", "炼", "麻", "纺", "禁", "废", "盛", "版", "缓", "净", "睛", "昌", "婚", "涉", "筒", "嘴", "插", "岸", "朗", "庄", "街", "藏", "姑", "贸", "腐", "奴", "啦", "惯", "乘", "伙", "恢", "匀", "纱", "扎", "辩", "耳", "彪", "臣", "亿", "璃", "抵", "脉", "秀", "萨", "俄", "网", "舞", "店", "喷", "纵", "寸", "汗", "挂", "洪", "贺", "闪", "柬", "爆", "烯", "津", "稻", "墙", "软", "勇", "像", "滚", "厘", "蒙", "芳", "肯", "坡", "柱", "荡", "腿", "仪", "旅", "尾", "轧", "冰", "贡", "登", "黎", "削", "钻", "勒", "逃", "障", "氨", "郭", "峰", "币", "港", "伏", "轨", "亩", "毕", "擦", "莫", "刺", "浪", "秘", "援", "株", "健", "售", "股", "岛", "甘", "泡", "睡", "童", "铸", "汤", "阀", "休", "汇", "舍", "牧", "绕", "炸", "哲", "磷", "绩", "朋", "淡", "尖", "启", "陷", "柴", "呈", "徒", "颜", "泪", "稍", "忘", "泵", "蓝", "拖", "洞", "授", "镜", "辛", "壮", "锋", "贫", "虚", "弯", "摩", "泰", "幼", "廷", "尊", "窗", "纲", "弄", "隶", "疑", "氏", "宫", "姐", "震", "瑞", "怪", "尤", "琴", "循", "描", "膜", "违", "夹", "腰", "缘", "珠", "穷", "森", "枝", "竹", "沟", "催", "绳", "忆", "邦", "剩", "幸", "浆", "栏", "拥", "牙", "贮", "礼", "滤", "钠", "纹", "罢", "拍", "咱", "喊", "袖", "埃", "勤", "罚", "焦", "潜", "伍", "墨", "欲", "缝", "姓", "刊", "饱", "仿", "奖", "铝", "鬼", "丽", "跨", "默", "挖", "链", "扫", "喝", "袋", "炭", "污", "幕", "诸", "弧", "励", "梅", "奶", "洁", "灾", "舟", "鉴", "苯", "讼", "抱", "毁", "懂", "寒", "智", "埔", "寄", "届", "跃", "渡", "挑", "丹", "艰", "贝", "碰", "拔", "爹", "戴", "码", "梦", "芽", "熔", "赤", "渔", "哭", "敬", "颗", "奔", "铅", "仲", "虎", "稀", "妹", "乏", "珍", "申", "桌", "遵", "允", "隆", "螺", "仓", "魏", "锐", "晓", "氮", "兼", "隐", "碍", "赫", "拨", "忠", "肃", "缸", "牵", "抢", "博", "巧", "壳", "兄", "杜", "讯", "诚", "碧", "祥", "柯", "页", "巡", "矩", "悲", "灌", "龄", "伦", "票", "寻", "桂", "铺", "圣", "恐", "恰", "郑", "趣", "抬", "荒", "腾", "贴", "柔", "滴", "猛", "阔", "辆", "妻", "填", "撤", "储", "签", "闹", "扰", "紫", "砂", "递", "戏", "吊", "陶", "伐", "喂", "疗", "瓶", "婆", "抚", "臂", "摸", "忍", "虾", "蜡", "邻", "胸", "巩", "挤", "偶", "弃", "槽", "劲", "乳", "邓", "吉", "仁", "烂", "砖", "租", "乌", "舰", "伴", "瓜", "浅", "丙", "暂", "燥", "橡", "柳", "迷", "暖", "牌", "秧", "胆", "详", "簧", "踏", "瓷", "谱", "呆", "宾", "糊", "洛", "辉", "愤", "竞", "隙", "怒", "粘", "乃", "绪", "肩", "籍", "敏", "涂", "熙", "皆", "侦", "悬", "掘", "享", "纠", "醒", "狂", "锁", "淀", "恨", "牲", "霸", "爬", "赏", "逆", "玩", "陵", "祝", "秒", "浙", "貌", "役", "彼", "悉", "鸭", "趋", "凤", "晨", "畜", "辈", "秩", "卵", "署", "梯", "炎", "滩", "棋", "驱", "筛", "峡", "冒", "啥", "寿", "译", "浸", "泉", "帽", "迟", "硅", "疆", "贷", "漏", "稿", "冠", "嫩", "胁", "芯", "牢", "叛", "蚀", "奥", "鸣", "岭", "羊", "凭", "串", "塘", "绘", "酵", "融", "盆", "锡", "庙", "筹", "冻", "辅", "摄", "袭", "筋", "拒", "僚", "旱", "钾", "鸟", "漆", "沈", "眉", "疏", "添", "棒", "穗", "硝", "韩", "逼", "扭", "侨", "凉", "挺", "碗", "栽", "炒", "杯", "患", "馏", "劝", "豪", "辽", "勃", "鸿", "旦", "吏", "拜", "狗", "埋", "辊", "掩", "饮", "搬", "骂", "辞", "勾", "扣", "估", "蒋", "绒", "雾", "丈", "朵", "姆", "拟", "宇", "辑", "陕", "雕", "偿", "蓄", "崇", "剪", "倡", "厅", "咬", "驶", "薯", "刷", "斥", "番", "赋", "奉", "佛", "浇", "漫", "曼", "扇", "钙", "桃", "扶", "仔", "返", "俗", "亏", "腔", "鞋", "棱", "覆", "框", "悄", "叔", "撞", "骗", "勘", "旺", "沸", "孤", "吐", "孟", "渠", "屈", "疾", "妙", "惜", "仰", "狠", "胀", "谐", "抛", "霉", "桑", "岗", "嘛", "衰", "盗", "渗", "脏", "赖", "涌", "甜", "曹", "阅", "肌", "哩", "厉", "烃", "纬", "毅", "昨", "伪", "症", "煮", "叹", "钉", "搭", "茎", "笼", "酷", "偷", "弓", "锥", "恒", "杰", "坑", "鼻", "翼", "纶", "叙", "狱", "逮", "罐", "络", "棚", "抑", "膨", "蔬", "寺", "骤", "穆", "冶", "枯", "册", "尸", "凸", "绅", "坯", "牺", "焰", "轰", "欣", "晋", "瘦", "御", "锭", "锦", "丧", "旬", "锻", "垄", "搜", "扑", "邀", "亭", "酯", "迈", "舒", "脆", "酶", "闲", "忧", "酚", "顽", "羽", "涨", "卸", "仗", "陪", "辟", "惩", "杭", "姿", "肆", "鸡", "饿", "恶", "祸", "丘", "玄", "溜", "曰", "逻", "彭", "尝", "卿", "妨", "艇", "吞", "韦", "怨", "矮", "歇" } },
                { SupportedLanguage.Russian, new[] { "в", "и", "не", "на", "я", "быть", "он", "с", "а", "как", "что", "это", "вы", "мы", "к", "из", "у", "который", "по", "для", "от", "его", "но", "да", "ты", "за", "же", "если", "при", "так", "все", "без", "может", "она", "до", "мой", "о", "очень", "то", "только", "или", "уже", "там", "чтобы", "нет", "ни", "когда", "даже", "под", "будет", "где", "еще", "также", "их", "чем", "была", "сам", "чего", "были", "тем", "кто", "этот", "того", "потому", "этой", "после", "над", "более", "здесь", "надо", "ней" } },
                { SupportedLanguage.Arabic, new[] { "في", "من", "إلى", "على", "هذا", "هذه", "التي", "التي", "كان", "لم", "إن", "أن", "كل", "قد", "لا", "ما", "أو", "كما", "بعد", "عند", "أي", "أم", "أين", "كيف", "لكن", "غير", "سوف", "حتى", "منذ", "بين", "تحت", "فوق", "أمام", "خلف", "يمين", "يسار", "داخل", "خارج", "حول", "ضد", "مع", "بدون", "خلال", "أثناء", "قبل", "بعد", "أمس", "اليوم", "غدا", "الآن", "هنا", "هناك", "أين", "متى", "كيف", "لماذا", "ماذا", "من", "أي", "كم", "أين", "متى", "كيف", "لماذا", "ماذا", "من", "أي", "كم" } },
                { SupportedLanguage.Hindi, new[] { "के", "में", "की", "और", "को", "से", "पर", "है", "का", "एक", "यह", "होता", "था", "हैं", "थे", "करने", "किया", "गया", "हो", "तो", "ही", "भी", "कि", "जो", "कर", "दिया", "गई", "थी", "कुछ", "लिए", "साथ", "बाद", "फिर", "वह", "जा", "रहा", "आप", "हम", "उन", "इस", "अब", "सब", "कहा", "वे", "क्या", "नहीं", "उसे", "जब", "तक", "या", "अपने", "कोई", "सभी", "मैं", "हुआ", "उस", "कहते", "यदि", "हुई", "जैसे", "अपनी", "इसे", "वाले", "बहुत", "करते", "होने", "वाला", "इसी", "उसी", "जाता", "होगा", "किसी", "आदि", "इससे", "होती", "करना", "चाहिए", "दूसरे", "किन्तु", "वहाँ", "जहाँ", "अभी", "सकते", "होते", "करके", "किये", "रखा", "जाने", "एवं", "तुम", "हमारे", "इनके", "दोनों", "एकदम", "हमें", "आपको", "इनका", "हमारा", "आपका", "इनकी", "हमारी", "आपकी", "इनसे", "हमसे", "आपसे", "इनमें", "हममें", "आपमें", "इनपर", "हमपर", "आपपर", "इनके लिए", "हमारे लिए", "आपके लिए", "इनके साथ", "हमारे साथ", "आपके साथ", "इनके बाद", "हमारे बाद", "आपके बाद", "इनसे पहले", "हमसे पहले", "आपसे पहले" } }
            };
            
            foreach (var languagePattern in patterns)
            {
                int matches = 0;
                foreach (var pattern in languagePattern.Value)
                {
                    if (text.Contains(pattern))
                        matches++;
                }
                
                // Si encuentra suficientes coincidencias, considera que es ese idioma
                if (matches >= 3)
                    return languagePattern.Key;
            }
            
            // Si no detecta nada específico, mantener idioma actual
            return currentLanguage;
        }
        
        /// <summary>
        /// Obtiene las mejores voces para un idioma
        /// </summary>
        public VoiceProfile[] GetBestVoicesForLanguage(SupportedLanguage language, int maxResults = 5)
        {
            if (languageVoices.ContainsKey(language))
            {
                var voices = languageVoices[language];
                return voices.Take(maxResults).ToArray();
            }
            
            return new VoiceProfile[0];
        }
        
        /// <summary>
        /// Actualiza las voces disponibles
        /// </summary>
        public void UpdateAvailableVoices(VoiceInfo[] voices)
        {
            languageVoices.Clear();
            
            foreach (var voice in voices)
            {
                var profile = ConvertToVoiceProfile(voice);
                
                foreach (var language in profile.supportedLanguages)
                {
                    if (!languageVoices.ContainsKey(language))
                        languageVoices[language] = new List<VoiceProfile>();
                        
                    languageVoices[language].Add(profile);
                }
            }
        }
        
        private VoiceProfile ConvertToVoiceProfile(VoiceInfo voiceInfo)
        {
            var profile = new VoiceProfile(voiceInfo.voiceId, voiceInfo.name);
            
            // Mapear características basándose en el nombre y descripción
            profile.gender = DetermineGender(voiceInfo.name, voiceInfo.description);
            profile.age = DetermineAge(voiceInfo.name, voiceInfo.description);
            profile.supportedLanguages = DetermineSupportedLanguages(voiceInfo);
            profile.supportedStyles = new[] { VoiceStyle.Conversational }; // Por defecto
            profile.description = voiceInfo.description;
            profile.previewUrl = voiceInfo.previewUrl;
            
            return profile;
        }
        
        private VoiceGender DetermineGender(string name, string description)
        {
            var maleNames = new[] { "adam", "antoni", "arnold", "bill", "callum", "charlie", "clyde", "daniel", "dave", "drew", "ethan", "fin", "george", "giovanni", "harry", "james", "jeremy", "josh", "liam", "matilda", "michael", "paul", "ryan", "sam", "thomas", "will" };
            var femaleNames = new[] { "alice", "bella", "charlotte", "dorothy", "elli", "emily", "freya", "gigi", "grace", "jessica", "lily", "mimi", "nicole", "rachel", "sarah", "serena", "sophie", "stella", "victoria", "zoe" };
            
            string lowerName = name.ToLower();
            
            if (maleNames.Any(n => lowerName.Contains(n)))
                return VoiceGender.Male;
            if (femaleNames.Any(n => lowerName.Contains(n)))
                return VoiceGender.Female;
                
            return VoiceGender.Any;
        }
        
        private VoiceAge DetermineAge(string name, string description)
        {
            string combined = (name + " " + description).ToLower();
            
            if (combined.Contains("young") || combined.Contains("child"))
                return VoiceAge.Young;
            if (combined.Contains("old") || combined.Contains("senior"))
                return VoiceAge.Senior;
                
            return VoiceAge.Adult;
        }
        
        private SupportedLanguage[] DetermineSupportedLanguages(VoiceInfo voiceInfo)
        {
            // Por defecto, todas las voces de ElevenLabs soportan múltiples idiomas
            // En una implementación real, esto vendría de la API
            return new[] { SupportedLanguage.English, SupportedLanguage.Spanish, SupportedLanguage.French, SupportedLanguage.German };
        }
        
        private void InitializeDefaultVoiceMappings()
        {
            // Configurar mapeos por defecto si no están definidos
            if (languageVoiceMappings == null || languageVoiceMappings.Length == 0)
            {
                languageVoiceMappings = new LanguageVoiceMapping[]
                {
                    new LanguageVoiceMapping { language = SupportedLanguage.English, defaultVoiceId = "21m00Tcm4TlvDq8ikWAM" }, // Rachel
                    new LanguageVoiceMapping { language = SupportedLanguage.Spanish, defaultVoiceId = "VR6AewLTigWG4xSOukaG" }, // Arnold
                    new LanguageVoiceMapping { language = SupportedLanguage.French, defaultVoiceId = "ErXwobaYiN019PkySvjV" }, // Antoni
                    new LanguageVoiceMapping { language = SupportedLanguage.German, defaultVoiceId = "pNInz6obpgDQGcFmaJgB" }, // Adam
                };
            }
        }
        
        [Serializable]
        public class LanguageVoiceMapping
        {
            public SupportedLanguage language;
            public string defaultVoiceId;
        }
    }
}
