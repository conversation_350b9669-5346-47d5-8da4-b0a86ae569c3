using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Text;
using UnityEngine;
using UnityEngine.Networking;
using Newtonsoft.Json;
using MoxieRobot.TTS.Core;

namespace MoxieRobot.TTS.ElevenLabs
{
    /// <summary>
    /// Cliente para la API de ElevenLabs
    /// </summary>
    public class ElevenLabsAPIClient : MonoBehaviour
    {
        [Header("API Configuration")]
        [SerializeField] private string apiKey;
        [SerializeField] private string baseUrl = "https://api.elevenlabs.io/v1";
        
        [Header("Default Settings")]
        [SerializeField] private TTSSettings defaultSettings;
        
        // Cache de voces disponibles
        private Dictionary<string, VoiceInfo> voiceCache = new Dictionary<string, VoiceInfo>();
        private bool voicesCached = false;
        
        // Eventos
        public event Action<VoiceInfo[]> OnVoicesLoaded;
        public event Action<string> OnError;
        
        private void Awake()
        {
            if (defaultSettings == null)
                defaultSettings = new TTSSettings();
        }
        
        /// <summary>
        /// Configura la API key
        /// </summary>
        public void SetApiKey(string key)
        {
            apiKey = key;
        }
        
        /// <summary>
        /// Obtiene las voces disponibles
        /// </summary>
        public void GetAvailableVoices(Action<VoiceInfo[]> onComplete = null)
        {
            if (voicesCached)
            {
                onComplete?.Invoke(GetCachedVoices());
                return;
            }
            
            StartCoroutine(GetVoicesCoroutine(onComplete));
        }
        
        private IEnumerator GetVoicesCoroutine(Action<VoiceInfo[]> onComplete)
        {
            string url = $"{baseUrl}/voices";
            
            using (UnityWebRequest request = UnityWebRequest.Get(url))
            {
                request.SetRequestHeader("xi-api-key", apiKey);
                request.SetRequestHeader("Content-Type", "application/json");
                
                yield return request.SendWebRequest();
                
                if (request.result == UnityWebRequest.Result.Success)
                {
                    try
                    {
                        var response = JsonConvert.DeserializeObject<VoicesResponse>(request.downloadHandler.text);
                        
                        voiceCache.Clear();
                        foreach (var voice in response.voices)
                        {
                            voiceCache[voice.voiceId] = voice;
                        }
                        
                        voicesCached = true;
                        var voices = GetCachedVoices();
                        
                        OnVoicesLoaded?.Invoke(voices);
                        onComplete?.Invoke(voices);
                    }
                    catch (Exception e)
                    {
                        string error = $"Error parsing voices response: {e.Message}";
                        Debug.LogError(error);
                        OnError?.Invoke(error);
                        onComplete?.Invoke(new VoiceInfo[0]);
                    }
                }
                else
                {
                    string error = $"Error getting voices: {request.error}";
                    Debug.LogError(error);
                    OnError?.Invoke(error);
                    onComplete?.Invoke(new VoiceInfo[0]);
                }
            }
        }
        
        /// <summary>
        /// Sintetiza texto a audio
        /// </summary>
        public void TextToSpeech(string text, string voiceId, TTSSettings settings = null, Action<AudioClip> onComplete = null)
        {
            if (string.IsNullOrEmpty(text))
            {
                onComplete?.Invoke(null);
                return;
            }
            
            if (settings == null)
                settings = defaultSettings;
                
            StartCoroutine(TextToSpeechCoroutine(text, voiceId, settings, onComplete));
        }
        
        private IEnumerator TextToSpeechCoroutine(string text, string voiceId, TTSSettings settings, Action<AudioClip> onComplete)
        {
            string url = $"{baseUrl}/text-to-speech/{voiceId}";
            
            var requestData = new TTSRequest
            {
                text = text,
                voice_settings = new VoiceSettingsRequest
                {
                    stability = settings.stability,
                    similarity_boost = settings.similarityBoost,
                    style = settings.style,
                    use_speaker_boost = settings.useSpeakerBoost
                },
                model_id = "eleven_multilingual_v2"
            };
            
            string jsonData = JsonConvert.SerializeObject(requestData);
            byte[] bodyRaw = Encoding.UTF8.GetBytes(jsonData);
            
            using (UnityWebRequest request = new UnityWebRequest(url, "POST"))
            {
                request.uploadHandler = new UploadHandlerRaw(bodyRaw);
                request.downloadHandler = new DownloadHandlerBuffer();
                request.SetRequestHeader("xi-api-key", apiKey);
                request.SetRequestHeader("Content-Type", "application/json");
                request.SetRequestHeader("Accept", "audio/mpeg");
                
                yield return request.SendWebRequest();
                
                if (request.result == UnityWebRequest.Result.Success)
                {
                    try
                    {
                        byte[] audioData = request.downloadHandler.data;
                        AudioClip clip = ConvertToAudioClip(audioData, text);
                        onComplete?.Invoke(clip);
                    }
                    catch (Exception e)
                    {
                        string error = $"Error converting audio: {e.Message}";
                        Debug.LogError(error);
                        OnError?.Invoke(error);
                        onComplete?.Invoke(null);
                    }
                }
                else
                {
                    string error = $"TTS Error: {request.error} - {request.downloadHandler.text}";
                    Debug.LogError(error);
                    OnError?.Invoke(error);
                    onComplete?.Invoke(null);
                }
            }
        }
        
        /// <summary>
        /// Convierte datos de audio MP3 a AudioClip
        /// </summary>
        private AudioClip ConvertToAudioClip(byte[] audioData, string clipName)
        {
            // Implementación mejorada para manejar MP3
            try
            {
                // Crear archivo temporal
                string tempPath = System.IO.Path.GetTempFileName() + ".mp3";
                System.IO.File.WriteAllBytes(tempPath, audioData);

                // Usar UnityWebRequestMultimedia para cargar MP3
                StartCoroutine(LoadAudioFromFile(tempPath, clipName, (clip) => {
                    // Limpiar archivo temporal
                    if (System.IO.File.Exists(tempPath))
                        System.IO.File.Delete(tempPath);
                }));

                // Por ahora retornar clip temporal hasta que se cargue el real
                return AudioClip.Create(clipName, 1, 1, 44100, false);
            }
            catch (Exception e)
            {
                Debug.LogError($"Error converting audio: {e.Message}");
                return AudioClip.Create(clipName, 1, 1, 44100, false);
            }
        }

        private IEnumerator LoadAudioFromFile(string filePath, string clipName, Action<AudioClip> onComplete)
        {
            using (UnityWebRequest www = UnityWebRequestMultimedia.GetAudioClip("file://" + filePath, AudioType.MPEG))
            {
                yield return www.SendWebRequest();

                if (www.result == UnityWebRequest.Result.Success)
                {
                    AudioClip clip = DownloadHandlerAudioClip.GetContent(www);
                    clip.name = clipName;
                    onComplete?.Invoke(clip);
                }
                else
                {
                    Debug.LogError($"Failed to load audio: {www.error}");
                    onComplete?.Invoke(null);
                }
            }
        }
        
        private VoiceInfo[] GetCachedVoices()
        {
            var voices = new VoiceInfo[voiceCache.Count];
            voiceCache.Values.CopyTo(voices, 0);
            return voices;
        }
        
        // Clases para serialización JSON
        [Serializable]
        private class VoicesResponse
        {
            public VoiceInfo[] voices;
        }
        
        [Serializable]
        private class TTSRequest
        {
            public string text;
            public VoiceSettingsRequest voice_settings;
            public string model_id;
        }
        
        [Serializable]
        private class VoiceSettingsRequest
        {
            public float stability;
            public float similarity_boost;
            public float style;
            public bool use_speaker_boost;
        }
    }
}
