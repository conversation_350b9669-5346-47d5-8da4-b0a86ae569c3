using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using MoxieRobot.TTS.Core;
using MoxieRobot.TTS.Visemes;

namespace MoxieRobot.TTS.ElevenLabs
{
    /// <summary>
    /// Gestor principal del sistema TTS con ElevenLabs
    /// Reemplaza TTSRequestCloudHandler manteniendo compatibilidad
    /// </summary>
    public class ElevenLabsTTSManager : MonoBehaviour
    {
        [Header("Configuration")]
        [SerializeField] private string apiKey;
        [SerializeField] private string defaultVoiceId = "21m00Tcm4TlvDq8ikWAM"; // <PERSON> voice
        [SerializeField] private SupportedLanguage defaultLanguage = SupportedLanguage.English;
        [SerializeField] private TTSSettings defaultSettings;
        
        [Header("Components")]
        [SerializeField] private ElevenLabsAPIClient apiClient;
        [SerializeField] private MultiLanguageSupport languageSupport;
        [SerializeField] private VoiceSelectionManager voiceManager;
        [SerializeField] private AudioCacheManager cacheManager;
        [SerializeField] private EnhancedVisemeGenerator visemeGenerator;
        
        [Header("Audio")]
        [SerializeField] private AudioSource audioSource;
        
        // Estados
        private bool isInitialized = false;
        private bool isSpeaking = false;
        private Coroutine currentTTSCoroutine;
        
        // Eventos (compatibilidad con sistema anterior)
        public event Action<AudioClip> OnTTSCompleteEvent;
        public event Action<TTSStreamData> OnTTSStreamEvent;
        public event Action<VisemeData> OnVisemeEvent;
        public event Action<string> OnTTSError;
        
        // Propiedades públicas
        public bool IsInitialized => isInitialized;
        public bool IsSpeaking => isSpeaking;
        public SupportedLanguage CurrentLanguage => languageSupport?.CurrentLanguage ?? defaultLanguage;
        public string CurrentVoiceId => voiceManager?.CurrentVoice?.VoiceId ?? defaultVoiceId;
        
        private void Awake()
        {
            InitializeComponents();
        }
        
        private void Start()
        {
            Initialize();
        }
        
        /// <summary>
        /// Inicializa el sistema TTS
        /// </summary>
        public void Initialize()
        {
            if (isInitialized) return;
            
            StartCoroutine(InitializeCoroutine());
        }
        
        private IEnumerator InitializeCoroutine()
        {
            Debug.Log("Initializing ElevenLabs TTS Manager...");
            
            // Configurar API client
            if (apiClient != null)
            {
                apiClient.SetApiKey(apiKey);
                
                // Cargar voces disponibles
                bool voicesLoaded = false;
                apiClient.GetAvailableVoices((voices) => {
                    voicesLoaded = true;
                    if (voiceManager != null)
                        voiceManager.SetAvailableVoices(voices);
                });
                
                // Esperar a que se carguen las voces
                yield return new WaitUntil(() => voicesLoaded);
            }
            
            // Inicializar componentes
            if (languageSupport != null)
                languageSupport.SetLanguage(defaultLanguage);
                
            if (voiceManager != null)
                voiceManager.SelectDefaultVoice(defaultVoiceId);
                
            if (cacheManager != null)
                cacheManager.Initialize();
                
            if (visemeGenerator != null)
                visemeGenerator.Initialize();
            
            isInitialized = true;
            Debug.Log("ElevenLabs TTS Manager initialized successfully");
        }
        
        /// <summary>
        /// Sintetiza y reproduce texto
        /// </summary>
        public void SpeakText(string text, string voiceId = null, SupportedLanguage? language = null, TTSSettings settings = null)
        {
            if (!isInitialized)
            {
                Debug.LogWarning("TTS Manager not initialized");
                return;
            }
            
            if (string.IsNullOrEmpty(text))
            {
                Debug.LogWarning("Empty text provided for TTS");
                return;
            }
            
            // Detener TTS actual si está hablando
            if (isSpeaking)
                StopSpeaking();
            
            // Usar valores por defecto si no se especifican
            voiceId = voiceId ?? CurrentVoiceId;
            language = language ?? CurrentLanguage;
            settings = settings ?? defaultSettings;
            
            currentTTSCoroutine = StartCoroutine(SpeakTextCoroutine(text, voiceId, language.Value, settings));
        }
        
        private IEnumerator SpeakTextCoroutine(string text, string voiceId, SupportedLanguage language, TTSSettings settings)
        {
            isSpeaking = true;
            
            try
            {
                // 1. Verificar cache
                AudioClip cachedClip = null;
                VisemeData[] cachedVisemes = null;
                
                if (cacheManager != null)
                {
                    var cacheResult = cacheManager.GetCachedAudio(text, voiceId, settings);
                    cachedClip = cacheResult.audioClip;
                    cachedVisemes = cacheResult.visemes;
                }
                
                AudioClip audioClip;
                VisemeData[] visemes;
                
                if (cachedClip != null)
                {
                    // Usar audio cacheado
                    audioClip = cachedClip;
                    visemes = cachedVisemes;
                    Debug.Log("Using cached audio for TTS");
                }
                else
                {
                    // Generar nuevo audio
                    bool ttsComplete = false;
                    AudioClip generatedClip = null;
                    
                    apiClient.TextToSpeech(text, voiceId, settings, (clip) => {
                        generatedClip = clip;
                        ttsComplete = true;
                    });
                    
                    // Esperar a que complete la síntesis
                    yield return new WaitUntil(() => ttsComplete);
                    
                    if (generatedClip == null)
                    {
                        OnTTSError?.Invoke("Failed to generate audio");
                        yield break;
                    }
                    
                    audioClip = generatedClip;
                    
                    // Generar visemas
                    if (visemeGenerator != null)
                        visemes = visemeGenerator.GenerateVisemesFromText(text, language);
                    else
                        visemes = new VisemeData[0];
                    
                    // Cachear para uso futuro
                    if (cacheManager != null)
                        cacheManager.CacheAudio(text, voiceId, settings, audioClip, visemes);
                }
                
                // 2. Reproducir audio
                if (audioSource != null)
                {
                    audioSource.clip = audioClip;
                    audioSource.Play();
                    
                    // Notificar inicio
                    OnTTSCompleteEvent?.Invoke(audioClip);
                    
                    // 3. Reproducir visemas sincronizados
                    if (visemes != null && visemes.Length > 0)
                    {
                        StartCoroutine(PlayVisemesCoroutine(visemes, audioClip.length));
                    }
                    
                    // Esperar a que termine la reproducción
                    yield return new WaitForSeconds(audioClip.length);
                }
                
            }
            catch (Exception e)
            {
                Debug.LogError($"Error in TTS: {e.Message}");
                OnTTSError?.Invoke(e.Message);
            }
            finally
            {
                isSpeaking = false;
                currentTTSCoroutine = null;
            }
        }
        
        private IEnumerator PlayVisemesCoroutine(VisemeData[] visemes, float totalDuration)
        {
            float startTime = Time.time;
            int currentVisemeIndex = 0;
            
            while (currentVisemeIndex < visemes.Length && isSpeaking)
            {
                float elapsed = Time.time - startTime;
                var viseme = visemes[currentVisemeIndex];
                
                if (elapsed >= viseme.timestamp)
                {
                    OnVisemeEvent?.Invoke(viseme);
                    currentVisemeIndex++;
                }
                
                yield return null;
            }
        }
        
        /// <summary>
        /// Detiene la reproducción actual
        /// </summary>
        public void StopSpeaking()
        {
            if (currentTTSCoroutine != null)
            {
                StopCoroutine(currentTTSCoroutine);
                currentTTSCoroutine = null;
            }
            
            if (audioSource != null && audioSource.isPlaying)
                audioSource.Stop();
                
            isSpeaking = false;
        }
        
        /// <summary>
        /// Cambia el idioma actual
        /// </summary>
        public void SetLanguage(SupportedLanguage language)
        {
            if (languageSupport != null)
                languageSupport.SetLanguage(language);
        }
        
        /// <summary>
        /// Cambia la voz actual
        /// </summary>
        public void SetVoice(string voiceId)
        {
            if (voiceManager != null)
                voiceManager.SelectVoice(voiceId);
        }
        
        private void InitializeComponents()
        {
            // Buscar componentes si no están asignados
            if (apiClient == null)
                apiClient = GetComponent<ElevenLabsAPIClient>();
                
            if (languageSupport == null)
                languageSupport = GetComponent<MultiLanguageSupport>();
                
            if (voiceManager == null)
                voiceManager = GetComponent<VoiceSelectionManager>();
                
            if (cacheManager == null)
                cacheManager = GetComponent<AudioCacheManager>();
                
            if (visemeGenerator == null)
                visemeGenerator = GetComponent<EnhancedVisemeGenerator>();
                
            if (audioSource == null)
                audioSource = GetComponent<AudioSource>();
                
            // Crear configuración por defecto si no existe
            if (defaultSettings == null)
                defaultSettings = TTSSettings.ForConversation();
        }
        
        private void OnDestroy()
        {
            StopSpeaking();
        }
    }
}
