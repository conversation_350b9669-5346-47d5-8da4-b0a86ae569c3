using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using MoxieRobot.TTS.Core;

namespace MoxieRobot.TTS.ElevenLabs
{
    /// <summary>
    /// Gestor de selección y configuración de voces
    /// </summary>
    public class VoiceSelectionManager : MonoBehaviour
    {
        [Header("Voice Configuration")]
        [SerializeField] private VoiceProfile currentVoice;
        [SerializeField] private VoiceProfile[] favoriteVoices;
        
        [Header("Selection Criteria")]
        [SerializeField] private VoiceGender preferredGender = VoiceGender.Any;
        [SerializeField] private VoiceAge preferredAge = VoiceAge.Any;
        [SerializeField] private VoiceStyle preferredStyle = VoiceStyle.Conversational;
        
        // Cache de voces disponibles
        private VoiceProfile[] availableVoices = new VoiceProfile[0];
        private Dictionary<string, VoiceProfile> voiceCache = new Dictionary<string, VoiceProfile>();
        
        // Eventos
        public event Action<VoiceProfile> OnVoiceChanged;
        public event Action<VoiceProfile[]> OnVoicesUpdated;
        
        // Propiedades
        public VoiceProfile CurrentVoice => currentVoice;
        public VoiceProfile[] AvailableVoices => availableVoices;
        public VoiceProfile[] FavoriteVoices => favoriteVoices ?? new VoiceProfile[0];
        
        private void Awake()
        {
            InitializeDefaultVoices();
        }
        
        /// <summary>
        /// Establece las voces disponibles desde la API
        /// </summary>
        public void SetAvailableVoices(VoiceInfo[] voices)
        {
            var profiles = new List<VoiceProfile>();
            voiceCache.Clear();
            
            foreach (var voice in voices)
            {
                var profile = CreateVoiceProfile(voice);
                profiles.Add(profile);
                voiceCache[voice.voiceId] = profile;
            }
            
            availableVoices = profiles.ToArray();
            OnVoicesUpdated?.Invoke(availableVoices);
            
            Debug.Log($"Updated available voices: {availableVoices.Length} voices loaded");
        }
        
        /// <summary>
        /// Selecciona una voz por ID
        /// </summary>
        public bool SelectVoice(string voiceId)
        {
            if (voiceCache.ContainsKey(voiceId))
            {
                var newVoice = voiceCache[voiceId];
                SetCurrentVoice(newVoice);
                return true;
            }
            
            Debug.LogWarning($"Voice not found: {voiceId}");
            return false;
        }
        
        /// <summary>
        /// Selecciona la voz por defecto
        /// </summary>
        public void SelectDefaultVoice(string defaultVoiceId)
        {
            if (!SelectVoice(defaultVoiceId))
            {
                // Si no encuentra la voz por defecto, usar la primera disponible
                if (availableVoices.Length > 0)
                {
                    SetCurrentVoice(availableVoices[0]);
                }
            }
        }
        
        /// <summary>
        /// Selecciona la mejor voz basada en criterios
        /// </summary>
        public VoiceProfile SelectBestVoice(VoiceGender gender = VoiceGender.Any, 
                                          VoiceAge age = VoiceAge.Any, 
                                          VoiceStyle style = VoiceStyle.Conversational, 
                                          SupportedLanguage language = SupportedLanguage.English)
        {
            var candidates = FilterVoices(new VoiceFilter
            {
                gender = gender,
                age = age,
                style = style,
                language = language
            });
            
            if (candidates.Length > 0)
            {
                var bestVoice = candidates[0]; // Tomar la primera (mejor puntuada)
                SetCurrentVoice(bestVoice);
                return bestVoice;
            }
            
            return currentVoice; // Mantener voz actual si no encuentra mejor opción
        }
        
        /// <summary>
        /// Filtra voces según criterios específicos
        /// </summary>
        public VoiceProfile[] FilterVoices(VoiceFilter filter)
        {
            var filtered = availableVoices.Where(voice => 
            {
                // Filtrar por género
                if (filter.gender != VoiceGender.Any && voice.gender != VoiceGender.Any)
                {
                    if (voice.gender != filter.gender)
                        return false;
                }
                
                // Filtrar por edad
                if (filter.age != VoiceAge.Any && voice.age != VoiceAge.Any)
                {
                    if (voice.age != filter.age)
                        return false;
                }
                
                // Filtrar por idioma
                if (filter.language != SupportedLanguage.English) // English es por defecto
                {
                    if (!voice.SupportsLanguage(filter.language))
                        return false;
                }
                
                // Filtrar por estilo
                if (filter.style != VoiceStyle.Conversational) // Conversational es por defecto
                {
                    if (!voice.SupportsStyle(filter.style))
                        return false;
                }
                
                return true;
            }).ToArray();
            
            // Ordenar por relevancia (favoritas primero, luego por calidad)
            return filtered.OrderByDescending(voice => 
            {
                int score = 0;
                
                // Bonus por ser voz favorita
                if (favoriteVoices != null && favoriteVoices.Contains(voice))
                    score += 100;
                
                // Bonus por coincidencia exacta de género
                if (filter.gender != VoiceGender.Any && voice.gender == filter.gender)
                    score += 50;
                
                // Bonus por coincidencia exacta de edad
                if (filter.age != VoiceAge.Any && voice.age == filter.age)
                    score += 30;
                
                // Bonus por soporte de idioma específico
                if (voice.SupportsLanguage(filter.language))
                    score += 20;
                
                // Bonus por soporte de estilo específico
                if (voice.SupportsStyle(filter.style))
                    score += 10;
                
                return score;
            }).ToArray();
        }
        
        /// <summary>
        /// Añade una voz a favoritas
        /// </summary>
        public void AddToFavorites(VoiceProfile voice)
        {
            if (favoriteVoices == null)
                favoriteVoices = new VoiceProfile[0];
                
            if (!favoriteVoices.Contains(voice))
            {
                var newFavorites = new VoiceProfile[favoriteVoices.Length + 1];
                favoriteVoices.CopyTo(newFavorites, 0);
                newFavorites[favoriteVoices.Length] = voice;
                favoriteVoices = newFavorites;
                
                Debug.Log($"Added voice to favorites: {voice.name}");
            }
        }
        
        /// <summary>
        /// Remueve una voz de favoritas
        /// </summary>
        public void RemoveFromFavorites(VoiceProfile voice)
        {
            if (favoriteVoices != null && favoriteVoices.Contains(voice))
            {
                favoriteVoices = favoriteVoices.Where(v => v != voice).ToArray();
                Debug.Log($"Removed voice from favorites: {voice.name}");
            }
        }
        
        /// <summary>
        /// Obtiene voces recomendadas para un contexto específico
        /// </summary>
        public VoiceProfile[] GetRecommendedVoices(VoiceContext context)
        {
            VoiceFilter filter = new VoiceFilter();
            
            switch (context)
            {
                case VoiceContext.ChildFriendly:
                    filter.age = VoiceAge.Young;
                    filter.style = VoiceStyle.Friendly;
                    break;
                    
                case VoiceContext.Professional:
                    filter.age = VoiceAge.Adult;
                    filter.style = VoiceStyle.Professional;
                    break;
                    
                case VoiceContext.Storytelling:
                    filter.style = VoiceStyle.Narration;
                    break;
                    
                case VoiceContext.Conversational:
                    filter.style = VoiceStyle.Conversational;
                    break;
                    
                case VoiceContext.Expressive:
                    filter.style = VoiceStyle.Expressive;
                    break;
            }
            
            return FilterVoices(filter).Take(5).ToArray();
        }
        
        private void SetCurrentVoice(VoiceProfile voice)
        {
            if (currentVoice != voice)
            {
                currentVoice = voice;
                OnVoiceChanged?.Invoke(voice);
                Debug.Log($"Voice changed to: {voice.name} ({voice.voiceId})");
            }
        }
        
        private VoiceProfile CreateVoiceProfile(VoiceInfo voiceInfo)
        {
            var profile = new VoiceProfile(voiceInfo.voiceId, voiceInfo.name)
            {
                description = voiceInfo.description,
                previewUrl = voiceInfo.previewUrl
            };
            
            // Determinar características basándose en metadatos
            if (voiceInfo.labels != null)
            {
                profile.gender = DetermineGenderFromLabels(voiceInfo.labels);
                profile.age = DetermineAgeFromLabels(voiceInfo.labels);
                profile.supportedStyles = DetermineStylesFromLabels(voiceInfo.labels);
            }
            
            // Configurar idiomas soportados (ElevenLabs soporta múltiples idiomas)
            profile.supportedLanguages = new[] 
            { 
                SupportedLanguage.English, SupportedLanguage.Spanish, 
                SupportedLanguage.French, SupportedLanguage.German,
                SupportedLanguage.Italian, SupportedLanguage.Portuguese
            };
            
            return profile;
        }
        
        private VoiceGender DetermineGenderFromLabels(string[] labels)
        {
            if (labels.Any(l => l.ToLower().Contains("male") && !l.ToLower().Contains("female")))
                return VoiceGender.Male;
            if (labels.Any(l => l.ToLower().Contains("female")))
                return VoiceGender.Female;
            return VoiceGender.Any;
        }
        
        private VoiceAge DetermineAgeFromLabels(string[] labels)
        {
            if (labels.Any(l => l.ToLower().Contains("young") || l.ToLower().Contains("child")))
                return VoiceAge.Young;
            if (labels.Any(l => l.ToLower().Contains("old") || l.ToLower().Contains("senior")))
                return VoiceAge.Senior;
            return VoiceAge.Adult;
        }
        
        private VoiceStyle[] DetermineStylesFromLabels(string[] labels)
        {
            var styles = new List<VoiceStyle> { VoiceStyle.Conversational }; // Por defecto
            
            if (labels.Any(l => l.ToLower().Contains("narrative") || l.ToLower().Contains("storytelling")))
                styles.Add(VoiceStyle.Narration);
            if (labels.Any(l => l.ToLower().Contains("expressive") || l.ToLower().Contains("emotional")))
                styles.Add(VoiceStyle.Expressive);
            if (labels.Any(l => l.ToLower().Contains("calm") || l.ToLower().Contains("soothing")))
                styles.Add(VoiceStyle.Calm);
            if (labels.Any(l => l.ToLower().Contains("energetic") || l.ToLower().Contains("upbeat")))
                styles.Add(VoiceStyle.Energetic);
            if (labels.Any(l => l.ToLower().Contains("friendly") || l.ToLower().Contains("warm")))
                styles.Add(VoiceStyle.Friendly);
            if (labels.Any(l => l.ToLower().Contains("professional") || l.ToLower().Contains("business")))
                styles.Add(VoiceStyle.Professional);
                
            return styles.ToArray();
        }
        
        private void InitializeDefaultVoices()
        {
            // Crear voces por defecto si no hay disponibles
            if (availableVoices.Length == 0)
            {
                availableVoices = new VoiceProfile[]
                {
                    new VoiceProfile("21m00Tcm4TlvDq8ikWAM", "Rachel") 
                    { 
                        gender = VoiceGender.Female, 
                        age = VoiceAge.Adult,
                        supportedLanguages = new[] { SupportedLanguage.English }
                    }
                };
                
                currentVoice = availableVoices[0];
            }
        }
    }
    
    /// <summary>
    /// Filtro para selección de voces
    /// </summary>
    [Serializable]
    public class VoiceFilter
    {
        public VoiceGender gender = VoiceGender.Any;
        public VoiceAge age = VoiceAge.Any;
        public VoiceStyle style = VoiceStyle.Conversational;
        public SupportedLanguage language = SupportedLanguage.English;
    }
    
    /// <summary>
    /// Contextos de uso para recomendaciones de voz
    /// </summary>
    public enum VoiceContext
    {
        ChildFriendly,
        Professional,
        Storytelling,
        Conversational,
        Expressive
    }
}
