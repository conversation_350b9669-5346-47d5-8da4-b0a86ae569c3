using System;
using System.Collections.Generic;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using UnityEngine;
using MoxieRobot.TTS.Core;
using MoxieRobot.TTS.Visemes;

namespace MoxieRobot.TTS.ElevenLabs
{
    /// <summary>
    /// Gestor de cache de audio para optimizar rendimiento y reducir costos de API
    /// </summary>
    public class AudioCacheManager : MonoBehaviour
    {
        [Header("Cache Configuration")]
        [SerializeField] private long maxCacheSizeBytes = 100 * 1024 * 1024; // 100MB por defecto
        [SerializeField] private int maxCacheEntries = 1000;
        [SerializeField] private float cacheExpirationDays = 7f;
        [SerializeField] private bool enablePersistentCache = true;
        
        [Header("Performance")]
        [SerializeField] private bool preloadCommonPhrases = true;
        [SerializeField] private string[] commonPhrases;
        
        // Cache en memoria
        private Dictionary<string, CachedAudioData> memoryCache = new Dictionary<string, CachedAudioData>();
        
        // Rutas de cache
        private string cacheDirectory;
        private string metadataFile;
        
        // Estadísticas
        private CacheStatistics statistics = new CacheStatistics();
        
        // Eventos
        public event Action<CacheStatistics> OnCacheStatsUpdated;
        
        // Propiedades
        public CacheStatistics Statistics => statistics;
        public long CurrentCacheSize => CalculateCurrentCacheSize();
        public bool IsCacheEnabled => enablePersistentCache;
        
        private void Awake()
        {
            InitializeCacheDirectories();
        }
        
        /// <summary>
        /// Inicializa el sistema de cache
        /// </summary>
        public void Initialize()
        {
            LoadCacheMetadata();
            CleanExpiredEntries();
            
            if (preloadCommonPhrases && commonPhrases != null)
            {
                StartCoroutine(PreloadCommonPhrasesCoroutine());
            }
            
            Debug.Log($"Audio Cache initialized. Directory: {cacheDirectory}");
        }
        
        /// <summary>
        /// Obtiene audio del cache o retorna null si no existe
        /// </summary>
        public CacheResult GetCachedAudio(string text, string voiceId, TTSSettings settings)
        {
            string cacheKey = GenerateCacheKey(text, voiceId, settings);
            
            // Verificar cache en memoria primero
            if (memoryCache.ContainsKey(cacheKey))
            {
                var memoryData = memoryCache[cacheKey];
                if (!IsExpired(memoryData))
                {
                    statistics.memoryHits++;
                    UpdateStatistics();
                    return new CacheResult { audioClip = memoryData.audioClip, visemes = memoryData.visemes };
                }
                else
                {
                    memoryCache.Remove(cacheKey);
                }
            }
            
            // Verificar cache persistente
            if (enablePersistentCache)
            {
                var diskData = LoadFromDisk(cacheKey);
                if (diskData != null && !IsExpired(diskData))
                {
                    // Cargar a memoria para acceso rápido
                    memoryCache[cacheKey] = diskData;
                    statistics.diskHits++;
                    UpdateStatistics();
                    return new CacheResult { audioClip = diskData.audioClip, visemes = diskData.visemes };
                }
            }
            
            statistics.misses++;
            UpdateStatistics();
            return new CacheResult { audioClip = null, visemes = null };
        }
        
        /// <summary>
        /// Cachea audio y visemas
        /// </summary>
        public void CacheAudio(string text, string voiceId, TTSSettings settings, AudioClip audioClip, VisemeData[] visemes)
        {
            if (audioClip == null) return;
            
            string cacheKey = GenerateCacheKey(text, voiceId, settings);
            
            var cacheData = new CachedAudioData
            {
                cacheKey = cacheKey,
                text = text,
                voiceId = voiceId,
                settings = settings,
                audioClip = audioClip,
                visemes = visemes,
                timestamp = DateTime.UtcNow,
                accessCount = 1,
                lastAccessed = DateTime.UtcNow
            };
            
            // Cachear en memoria
            memoryCache[cacheKey] = cacheData;
            
            // Cachear en disco si está habilitado
            if (enablePersistentCache)
            {
                SaveToDisk(cacheData);
            }
            
            // Limpiar cache si excede límites
            EnforceCacheLimits();
            
            statistics.entries++;
            UpdateStatistics();
            
            Debug.Log($"Cached audio: {text.Substring(0, Math.Min(50, text.Length))}...");
        }
        
        /// <summary>
        /// Limpia todo el cache
        /// </summary>
        public void ClearCache()
        {
            memoryCache.Clear();
            
            if (enablePersistentCache && Directory.Exists(cacheDirectory))
            {
                try
                {
                    Directory.Delete(cacheDirectory, true);
                    InitializeCacheDirectories();
                }
                catch (Exception e)
                {
                    Debug.LogError($"Error clearing cache: {e.Message}");
                }
            }
            
            statistics = new CacheStatistics();
            UpdateStatistics();
            
            Debug.Log("Audio cache cleared");
        }
        
        /// <summary>
        /// Limpia entradas expiradas
        /// </summary>
        public void CleanExpiredEntries()
        {
            var expiredKeys = new List<string>();
            
            foreach (var kvp in memoryCache)
            {
                if (IsExpired(kvp.Value))
                {
                    expiredKeys.Add(kvp.Key);
                }
            }
            
            foreach (var key in expiredKeys)
            {
                memoryCache.Remove(key);
                DeleteFromDisk(key);
            }
            
            if (expiredKeys.Count > 0)
            {
                Debug.Log($"Cleaned {expiredKeys.Count} expired cache entries");
            }
        }
        
        /// <summary>
        /// Precarga frases comunes
        /// </summary>
        private System.Collections.IEnumerator PreloadCommonPhrasesCoroutine()
        {
            if (commonPhrases == null) yield break;
            
            var ttsManager = FindObjectOfType<ElevenLabsTTSManager>();
            if (ttsManager == null) yield break;
            
            foreach (var phrase in commonPhrases)
            {
                if (!string.IsNullOrEmpty(phrase))
                {
                    // Verificar si ya está en cache
                    var result = GetCachedAudio(phrase, ttsManager.CurrentVoiceId, null);
                    if (result.audioClip == null)
                    {
                        // Generar y cachear
                        ttsManager.SpeakText(phrase);
                        yield return new WaitForSeconds(0.1f); // Pequeña pausa entre requests
                    }
                }
            }
            
            Debug.Log($"Preloaded {commonPhrases.Length} common phrases");
        }
        
        private string GenerateCacheKey(string text, string voiceId, TTSSettings settings)
        {
            var keyData = $"{text}|{voiceId}|{JsonUtility.ToJson(settings)}";
            
            using (var sha256 = SHA256.Create())
            {
                var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(keyData));
                return Convert.ToBase64String(hashBytes).Replace("/", "_").Replace("+", "-");
            }
        }
        
        private void InitializeCacheDirectories()
        {
            cacheDirectory = Path.Combine(Application.persistentDataPath, "AudioCache");
            metadataFile = Path.Combine(cacheDirectory, "cache_metadata.json");
            
            if (!Directory.Exists(cacheDirectory))
            {
                Directory.CreateDirectory(cacheDirectory);
            }
        }
        
        private CachedAudioData LoadFromDisk(string cacheKey)
        {
            try
            {
                string audioPath = Path.Combine(cacheDirectory, cacheKey + ".wav");
                string metaPath = Path.Combine(cacheDirectory, cacheKey + ".meta");
                
                if (File.Exists(audioPath) && File.Exists(metaPath))
                {
                    var metadata = JsonUtility.FromJson<CacheMetadata>(File.ReadAllText(metaPath));
                    
                    // Cargar audio (implementación simplificada)
                    // En producción, usar UnityWebRequest para cargar el archivo WAV
                    var audioClip = LoadAudioClipFromFile(audioPath);
                    
                    return new CachedAudioData
                    {
                        cacheKey = cacheKey,
                        text = metadata.text,
                        voiceId = metadata.voiceId,
                        audioClip = audioClip,
                        visemes = metadata.visemes,
                        timestamp = metadata.timestamp,
                        accessCount = metadata.accessCount + 1,
                        lastAccessed = DateTime.UtcNow
                    };
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"Error loading from disk cache: {e.Message}");
            }
            
            return null;
        }
        
        private void SaveToDisk(CachedAudioData data)
        {
            try
            {
                string audioPath = Path.Combine(cacheDirectory, data.cacheKey + ".wav");
                string metaPath = Path.Combine(cacheDirectory, data.cacheKey + ".meta");
                
                // Guardar audio como WAV (implementación simplificada)
                SaveAudioClipToFile(data.audioClip, audioPath);
                
                // Guardar metadata
                var metadata = new CacheMetadata
                {
                    text = data.text,
                    voiceId = data.voiceId,
                    visemes = data.visemes,
                    timestamp = data.timestamp,
                    accessCount = data.accessCount
                };
                
                File.WriteAllText(metaPath, JsonUtility.ToJson(metadata));
            }
            catch (Exception e)
            {
                Debug.LogError($"Error saving to disk cache: {e.Message}");
            }
        }
        
        private void DeleteFromDisk(string cacheKey)
        {
            try
            {
                string audioPath = Path.Combine(cacheDirectory, cacheKey + ".wav");
                string metaPath = Path.Combine(cacheDirectory, cacheKey + ".meta");
                
                if (File.Exists(audioPath)) File.Delete(audioPath);
                if (File.Exists(metaPath)) File.Delete(metaPath);
            }
            catch (Exception e)
            {
                Debug.LogError($"Error deleting from disk cache: {e.Message}");
            }
        }
        
        private AudioClip LoadAudioClipFromFile(string filePath)
        {
            // Implementación simplificada - en producción usar UnityWebRequest
            return AudioClip.Create("CachedClip", 44100, 1, 44100, false);
        }
        
        private void SaveAudioClipToFile(AudioClip clip, string filePath)
        {
            // Implementación simplificada - en producción convertir AudioClip a WAV
            // y guardar los bytes al archivo
        }
        
        private bool IsExpired(CachedAudioData data)
        {
            return (DateTime.UtcNow - data.timestamp).TotalDays > cacheExpirationDays;
        }
        
        private long CalculateCurrentCacheSize()
        {
            long size = 0;
            
            if (Directory.Exists(cacheDirectory))
            {
                var files = Directory.GetFiles(cacheDirectory);
                foreach (var file in files)
                {
                    size += new FileInfo(file).Length;
                }
            }
            
            return size;
        }
        
        private void EnforceCacheLimits()
        {
            // Limpiar por tamaño
            if (CurrentCacheSize > maxCacheSizeBytes)
            {
                CleanOldestEntries();
            }
            
            // Limpiar por número de entradas
            if (memoryCache.Count > maxCacheEntries)
            {
                CleanLeastUsedEntries();
            }
        }
        
        private void CleanOldestEntries()
        {
            var sortedEntries = new List<KeyValuePair<string, CachedAudioData>>(memoryCache);
            sortedEntries.Sort((a, b) => a.Value.lastAccessed.CompareTo(b.Value.lastAccessed));
            
            int toRemove = sortedEntries.Count / 4; // Remover 25%
            for (int i = 0; i < toRemove; i++)
            {
                var entry = sortedEntries[i];
                memoryCache.Remove(entry.Key);
                DeleteFromDisk(entry.Key);
            }
        }
        
        private void CleanLeastUsedEntries()
        {
            var sortedEntries = new List<KeyValuePair<string, CachedAudioData>>(memoryCache);
            sortedEntries.Sort((a, b) => a.Value.accessCount.CompareTo(b.Value.accessCount));
            
            int toRemove = sortedEntries.Count - maxCacheEntries;
            for (int i = 0; i < toRemove; i++)
            {
                var entry = sortedEntries[i];
                memoryCache.Remove(entry.Key);
                DeleteFromDisk(entry.Key);
            }
        }
        
        private void LoadCacheMetadata()
        {
            // Implementar carga de metadatos si es necesario
        }
        
        private void UpdateStatistics()
        {
            statistics.totalRequests = statistics.memoryHits + statistics.diskHits + statistics.misses;
            statistics.hitRate = statistics.totalRequests > 0 ? 
                (float)(statistics.memoryHits + statistics.diskHits) / statistics.totalRequests : 0f;
            statistics.cacheSize = CurrentCacheSize;
            
            OnCacheStatsUpdated?.Invoke(statistics);
        }
    }
    
    /// <summary>
    /// Datos de audio cacheados
    /// </summary>
    [Serializable]
    public class CachedAudioData
    {
        public string cacheKey;
        public string text;
        public string voiceId;
        public TTSSettings settings;
        public AudioClip audioClip;
        public VisemeData[] visemes;
        public DateTime timestamp;
        public DateTime lastAccessed;
        public int accessCount;
    }
    
    /// <summary>
    /// Metadatos de cache para persistencia
    /// </summary>
    [Serializable]
    public class CacheMetadata
    {
        public string text;
        public string voiceId;
        public VisemeData[] visemes;
        public DateTime timestamp;
        public int accessCount;
    }
    
    /// <summary>
    /// Resultado de búsqueda en cache
    /// </summary>
    public class CacheResult
    {
        public AudioClip audioClip;
        public VisemeData[] visemes;
    }
    
    /// <summary>
    /// Estadísticas del cache
    /// </summary>
    [Serializable]
    public class CacheStatistics
    {
        public int memoryHits;
        public int diskHits;
        public int misses;
        public int totalRequests;
        public float hitRate;
        public int entries;
        public long cacheSize;
    }
}
