using System;
using UnityEngine;
using MoxieRobot.TTS.ElevenLabs;

namespace MoxieRobot.TTS.Visemes
{
    /// <summary>
    /// Tipos de visemas para animación facial
    /// Basado en el estándar MPEG-4 Facial Animation Parameters
    /// </summary>
    public enum VisemeType
    {
        Silence,    // Silencio - boca cerrada
        PP,         // P, B, M - labios cerrados
        FF,         // F, V - labio inferior toca dientes superiores
        TH,         // TH - lengua entre dientes
        DD,         // T, D, N, L - lengua toca paladar
        KK,         // K, G - parte posterior de lengua toca paladar
        CH,         // CH, J, SH - lengua arqueada
        SS,         // S, Z - lengua cerca del paladar
        NN,         // N, NG - resonancia nasal
        RR,         // R - lengua curvada
        AA,         // A (father) - boca muy abierta
        E,          // E (bed) - boca semi-abierta
        I,          // I (bit) - boca ligeramente abierta
        O,          // O (hot) - boca redonda
        U,          // U (book) - labios fruncidos
        AE,         // AE (cat) - boca abierta, lengua baja
        AH,         // AH (but) - boca neutral abierta
        AO,         // AO (law) - boca redonda abierta
        AW,         // AW (how) - transición de A a U
        AY,         // AY (my) - transición de A a I
        EH,         // EH (bet) - boca semi-abierta
        ER,         // ER (bird) - boca semi-cerrada con R
        EY,         // EY (bay) - transición de E a I
        IH,         // IH (bit) - boca ligeramente abierta
        IY,         // IY (beat) - boca casi cerrada
        OW,         // OW (boat) - transición de O a U
        OY,         // OY (boy) - transición de O a I
        UH,         // UH (book) - boca semi-cerrada
        UW,         // UW (boot) - labios muy fruncidos
        W,          // W - labios fruncidos en transición
        Y           // Y - lengua alta, labios extendidos
    }
    
    /// <summary>
    /// Datos de un visema individual
    /// </summary>
    [Serializable]
    public class VisemeData
    {
        [Header("Timing")]
        public float timestamp;      // Tiempo en segundos desde el inicio
        public float duration;       // Duración del visema en segundos
        
        [Header("Viseme")]
        public VisemeType visemeType;
        public float intensity;      // Intensidad del visema (0-1)
        
        [Header("Facial Animation")]
        public float jawOpen;        // Apertura de mandíbula (0-1)
        public float lipCornerPull;  // Estiramiento de comisuras (0-1)
        public float lipPucker;      // Fruncimiento de labios (0-1)
        public float tongueOut;      // Extensión de lengua (0-1)
        
        [Header("Expression")]
        public float eyebrowRaise;   // Elevación de cejas (0-1)
        public float eyeSquint;      // Entrecerrar ojos (0-1)
        public float cheekPuff;      // Inflado de mejillas (0-1)
        
        public VisemeData()
        {
            timestamp = 0f;
            duration = 0.1f;
            visemeType = VisemeType.Silence;
            intensity = 1f;
            jawOpen = 0f;
            lipCornerPull = 0f;
            lipPucker = 0f;
            tongueOut = 0f;
            eyebrowRaise = 0f;
            eyeSquint = 0f;
            cheekPuff = 0f;
        }
        
        public VisemeData(float time, VisemeType type, float intensityValue = 1f)
        {
            timestamp = time;
            duration = 0.1f;
            visemeType = type;
            intensity = intensityValue;
            
            // Configurar parámetros faciales basados en el tipo de visema
            SetFacialParametersForViseme(type, intensityValue);
        }
        
        /// <summary>
        /// Configura los parámetros faciales según el tipo de visema
        /// </summary>
        private void SetFacialParametersForViseme(VisemeType type, float intensityValue)
        {
            // Resetear todos los valores
            jawOpen = 0f;
            lipCornerPull = 0f;
            lipPucker = 0f;
            tongueOut = 0f;
            eyebrowRaise = 0f;
            eyeSquint = 0f;
            cheekPuff = 0f;
            
            switch (type)
            {
                case VisemeType.Silence:
                    // Boca cerrada, relajada
                    break;
                    
                case VisemeType.PP:
                    // P, B, M - labios cerrados con presión
                    lipPucker = 0.3f * intensityValue;
                    break;
                    
                case VisemeType.FF:
                    // F, V - labio inferior toca dientes superiores
                    jawOpen = 0.2f * intensityValue;
                    lipCornerPull = -0.2f * intensityValue;
                    break;
                    
                case VisemeType.TH:
                    // TH - lengua entre dientes
                    jawOpen = 0.3f * intensityValue;
                    tongueOut = 0.5f * intensityValue;
                    break;
                    
                case VisemeType.DD:
                    // T, D, N, L - lengua toca paladar
                    jawOpen = 0.4f * intensityValue;
                    break;
                    
                case VisemeType.KK:
                    // K, G - parte posterior de lengua
                    jawOpen = 0.3f * intensityValue;
                    break;
                    
                case VisemeType.CH:
                    // CH, J, SH - lengua arqueada
                    jawOpen = 0.4f * intensityValue;
                    lipCornerPull = 0.2f * intensityValue;
                    break;
                    
                case VisemeType.SS:
                    // S, Z - lengua cerca del paladar
                    jawOpen = 0.2f * intensityValue;
                    lipCornerPull = 0.3f * intensityValue;
                    break;
                    
                case VisemeType.NN:
                    // N, NG - resonancia nasal
                    jawOpen = 0.2f * intensityValue;
                    break;
                    
                case VisemeType.RR:
                    // R - lengua curvada
                    jawOpen = 0.3f * intensityValue;
                    lipCornerPull = 0.1f * intensityValue;
                    break;
                    
                case VisemeType.AA:
                    // A (father) - boca muy abierta
                    jawOpen = 0.8f * intensityValue;
                    break;
                    
                case VisemeType.E:
                    // E (bed) - boca semi-abierta
                    jawOpen = 0.5f * intensityValue;
                    lipCornerPull = 0.3f * intensityValue;
                    break;
                    
                case VisemeType.I:
                    // I (bit) - boca ligeramente abierta
                    jawOpen = 0.3f * intensityValue;
                    lipCornerPull = 0.5f * intensityValue;
                    break;
                    
                case VisemeType.O:
                    // O (hot) - boca redonda
                    jawOpen = 0.6f * intensityValue;
                    lipPucker = 0.4f * intensityValue;
                    break;
                    
                case VisemeType.U:
                    // U (book) - labios fruncidos
                    jawOpen = 0.3f * intensityValue;
                    lipPucker = 0.7f * intensityValue;
                    break;
                    
                case VisemeType.AE:
                    // AE (cat) - boca abierta, lengua baja
                    jawOpen = 0.6f * intensityValue;
                    lipCornerPull = 0.2f * intensityValue;
                    break;
                    
                case VisemeType.AH:
                    // AH (but) - boca neutral abierta
                    jawOpen = 0.4f * intensityValue;
                    break;
                    
                case VisemeType.AO:
                    // AO (law) - boca redonda abierta
                    jawOpen = 0.7f * intensityValue;
                    lipPucker = 0.3f * intensityValue;
                    break;
                    
                case VisemeType.UW:
                    // UW (boot) - labios muy fruncidos
                    jawOpen = 0.2f * intensityValue;
                    lipPucker = 0.8f * intensityValue;
                    break;
                    
                case VisemeType.W:
                    // W - labios fruncidos en transición
                    jawOpen = 0.3f * intensityValue;
                    lipPucker = 0.6f * intensityValue;
                    break;
                    
                case VisemeType.Y:
                    // Y - lengua alta, labios extendidos
                    jawOpen = 0.2f * intensityValue;
                    lipCornerPull = 0.4f * intensityValue;
                    break;
                    
                // Casos de transición (diptongos)
                case VisemeType.AW:
                case VisemeType.AY:
                case VisemeType.EY:
                case VisemeType.OW:
                case VisemeType.OY:
                    // Para diptongos, usar valores intermedios
                    jawOpen = 0.4f * intensityValue;
                    lipCornerPull = 0.2f * intensityValue;
                    break;
            }
        }
        
        /// <summary>
        /// Interpola entre dos visemas
        /// </summary>
        public static VisemeData Lerp(VisemeData from, VisemeData to, float t)
        {
            var result = new VisemeData();
            
            result.timestamp = Mathf.Lerp(from.timestamp, to.timestamp, t);
            result.duration = Mathf.Lerp(from.duration, to.duration, t);
            result.intensity = Mathf.Lerp(from.intensity, to.intensity, t);
            
            // Interpolar parámetros faciales
            result.jawOpen = Mathf.Lerp(from.jawOpen, to.jawOpen, t);
            result.lipCornerPull = Mathf.Lerp(from.lipCornerPull, to.lipCornerPull, t);
            result.lipPucker = Mathf.Lerp(from.lipPucker, to.lipPucker, t);
            result.tongueOut = Mathf.Lerp(from.tongueOut, to.tongueOut, t);
            result.eyebrowRaise = Mathf.Lerp(from.eyebrowRaise, to.eyebrowRaise, t);
            result.eyeSquint = Mathf.Lerp(from.eyeSquint, to.eyeSquint, t);
            result.cheekPuff = Mathf.Lerp(from.cheekPuff, to.cheekPuff, t);
            
            // Para el tipo de visema, usar el más cercano
            result.visemeType = t < 0.5f ? from.visemeType : to.visemeType;
            
            return result;
        }
        
        /// <summary>
        /// Crea una copia del visema
        /// </summary>
        public VisemeData Clone()
        {
            return new VisemeData
            {
                timestamp = this.timestamp,
                duration = this.duration,
                visemeType = this.visemeType,
                intensity = this.intensity,
                jawOpen = this.jawOpen,
                lipCornerPull = this.lipCornerPull,
                lipPucker = this.lipPucker,
                tongueOut = this.tongueOut,
                eyebrowRaise = this.eyebrowRaise,
                eyeSquint = this.eyeSquint,
                cheekPuff = this.cheekPuff
            };
        }
        
        public override string ToString()
        {
            return $"Viseme: {visemeType} at {timestamp:F2}s (intensity: {intensity:F2})";
        }
    }
    
    /// <summary>
    /// Configuración de animación de visemas
    /// </summary>
    [Serializable]
    public class VisemeAnimationSettings
    {
        [Header("Timing")]
        public float transitionSpeed = 5f;      // Velocidad de transición entre visemas
        public float holdDuration = 0.05f;      // Duración mínima de cada visema
        public float blendTime = 0.02f;         // Tiempo de mezcla entre visemas
        
        [Header("Intensity")]
        public float globalIntensity = 1f;      // Multiplicador global de intensidad
        public AnimationCurve intensityCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
        
        [Header("Smoothing")]
        public bool enableSmoothing = true;     // Suavizar transiciones
        public float smoothingFactor = 0.8f;    // Factor de suavizado
        
        [Header("Expression")]
        public bool enableExpressions = true;   // Habilitar expresiones adicionales
        public float expressionIntensity = 0.5f; // Intensidad de expresiones
    }
}
