using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using UnityEngine;
using MoxieRobot.TTS.ElevenLabs;

namespace MoxieRobot.TTS.Visemes
{
    /// <summary>
    /// Generador mejorado de visemas para animación facial sincronizada
    /// </summary>
    public class EnhancedVisemeGenerator : MonoBehaviour
    {
        [Header("Configuration")]
        [SerializeField] private VisemeAnimationSettings animationSettings;
        [SerializeField] private bool enableAdvancedPhonemeMapping = true;
        [SerializeField] private bool enableCoarticulation = true; // Coarticulación - influencia entre fonemas adyacentes
        
        [Header("Language Support")]
        [SerializeField] private PhonemeMapping[] phonemeMappings;
        
        // Mapeos de fonemas a visemas por idioma
        private Dictionary<SupportedLanguage, Dictionary<string, VisemeType>> languagePhonemeMap;
        
        // Cache de patrones de texto a fonemas
        private Dictionary<string, string[]> textToPhonemeCache = new Dictionary<string, string[]>();
        
        // Eventos
        public event Action<VisemeData[]> OnVisemesGenerated;
        
        private void Awake()
        {
            if (animationSettings == null)
                animationSettings = new VisemeAnimationSettings();
                
            InitializePhonemeMapping();
        }
        
        /// <summary>
        /// Inicializa el generador de visemas
        /// </summary>
        public void Initialize()
        {
            LoadPhonemeDatabase();
            Debug.Log("Enhanced Viseme Generator initialized");
        }
        
        /// <summary>
        /// Genera visemas a partir de texto
        /// </summary>
        public VisemeData[] GenerateVisemesFromText(string text, SupportedLanguage language)
        {
            if (string.IsNullOrEmpty(text))
                return new VisemeData[0];
            
            try
            {
                // 1. Convertir texto a fonemas
                string[] phonemes = TextToPhonemes(text, language);
                
                // 2. Mapear fonemas a visemas
                var visemes = PhonemesToVisemes(phonemes, language);
                
                // 3. Aplicar timing basado en duración estimada
                float estimatedDuration = EstimateAudioDuration(text);
                visemes = ApplyTiming(visemes, estimatedDuration);
                
                // 4. Aplicar coarticulación si está habilitada
                if (enableCoarticulation)
                    visemes = ApplyCoarticulation(visemes);
                
                // 5. Suavizar transiciones
                if (animationSettings.enableSmoothing)
                    visemes = SmoothTransitions(visemes);
                
                OnVisemesGenerated?.Invoke(visemes);
                return visemes;
            }
            catch (Exception e)
            {
                Debug.LogError($"Error generating visemes: {e.Message}");
                return GenerateFallbackVisemes(text);
            }
        }
        
        /// <summary>
        /// Genera visemas a partir de audio con timestamps
        /// </summary>
        public VisemeData[] GenerateVisemesFromAudio(AudioClip audio, float[] timestamps)
        {
            if (audio == null || timestamps == null)
                return new VisemeData[0];
            
            // Implementación simplificada - en producción usar análisis de audio real
            var visemes = new List<VisemeData>();
            
            for (int i = 0; i < timestamps.Length; i++)
            {
                var viseme = new VisemeData(timestamps[i], VisemeType.AA, 0.8f);
                visemes.Add(viseme);
            }
            
            return visemes.ToArray();
        }
        
        /// <summary>
        /// Convierte texto a fonemas usando reglas lingüísticas
        /// </summary>
        private string[] TextToPhonemes(string text, SupportedLanguage language)
        {
            // Verificar cache
            string cacheKey = $"{text}_{language}";
            if (textToPhonemeCache.ContainsKey(cacheKey))
                return textToPhonemeCache[cacheKey];
            
            var phonemes = new List<string>();
            
            // Limpiar y normalizar texto
            text = NormalizeText(text);
            
            // Dividir en palabras
            string[] words = text.Split(new char[] { ' ', '\t', '\n' }, StringSplitOptions.RemoveEmptyEntries);
            
            foreach (string word in words)
            {
                var wordPhonemes = WordToPhonemes(word, language);
                phonemes.AddRange(wordPhonemes);
                
                // Añadir pausa entre palabras
                phonemes.Add("SIL");
            }
            
            var result = phonemes.ToArray();
            textToPhonemeCache[cacheKey] = result;
            
            return result;
        }
        
        /// <summary>
        /// Convierte una palabra a fonemas
        /// </summary>
        private string[] WordToPhonemes(string word, SupportedLanguage language)
        {
            var phonemes = new List<string>();
            
            // Implementación simplificada basada en reglas
            // En producción, usar un diccionario fonético o API de pronunciación
            
            switch (language)
            {
                case SupportedLanguage.English:
                    phonemes.AddRange(EnglishWordToPhonemes(word));
                    break;
                case SupportedLanguage.Spanish:
                    phonemes.AddRange(SpanishWordToPhonemes(word));
                    break;
                case SupportedLanguage.French:
                    phonemes.AddRange(FrenchWordToPhonemes(word));
                    break;
                default:
                    phonemes.AddRange(EnglishWordToPhonemes(word)); // Fallback
                    break;
            }
            
            return phonemes.ToArray();
        }
        
        private string[] EnglishWordToPhonemes(string word)
        {
            // Implementación simplificada de reglas fonéticas del inglés
            var phonemes = new List<string>();
            word = word.ToLower();
            
            for (int i = 0; i < word.Length; i++)
            {
                char c = word[i];
                string phoneme = "";
                
                switch (c)
                {
                    case 'a':
                        phoneme = (i < word.Length - 1 && word[i + 1] == 'e') ? "EY" : "AE";
                        break;
                    case 'e':
                        phoneme = (i == word.Length - 1) ? "SIL" : "EH";
                        break;
                    case 'i':
                        phoneme = "IH";
                        break;
                    case 'o':
                        phoneme = "AO";
                        break;
                    case 'u':
                        phoneme = "UH";
                        break;
                    case 'b':
                        phoneme = "B";
                        break;
                    case 'p':
                        phoneme = "P";
                        break;
                    case 'm':
                        phoneme = "M";
                        break;
                    case 'f':
                        phoneme = "F";
                        break;
                    case 'v':
                        phoneme = "V";
                        break;
                    case 't':
                        phoneme = "T";
                        break;
                    case 'd':
                        phoneme = "D";
                        break;
                    case 'n':
                        phoneme = "N";
                        break;
                    case 'l':
                        phoneme = "L";
                        break;
                    case 'r':
                        phoneme = "R";
                        break;
                    case 's':
                        phoneme = "S";
                        break;
                    case 'z':
                        phoneme = "Z";
                        break;
                    case 'k':
                    case 'c':
                        phoneme = "K";
                        break;
                    case 'g':
                        phoneme = "G";
                        break;
                    case 'w':
                        phoneme = "W";
                        break;
                    case 'y':
                        phoneme = "Y";
                        break;
                    case 'h':
                        phoneme = "HH";
                        break;
                    default:
                        phoneme = "SIL";
                        break;
                }
                
                if (!string.IsNullOrEmpty(phoneme))
                    phonemes.Add(phoneme);
            }
            
            return phonemes.ToArray();
        }
        
        private string[] SpanishWordToPhonemes(string word)
        {
            // Implementación simplificada para español
            var phonemes = new List<string>();
            word = word.ToLower();
            
            // El español tiene una correspondencia más directa entre grafemas y fonemas
            foreach (char c in word)
            {
                string phoneme = "";
                
                switch (c)
                {
                    case 'a': phoneme = "A"; break;
                    case 'e': phoneme = "E"; break;
                    case 'i': phoneme = "I"; break;
                    case 'o': phoneme = "O"; break;
                    case 'u': phoneme = "U"; break;
                    case 'b': phoneme = "B"; break;
                    case 'p': phoneme = "P"; break;
                    case 'm': phoneme = "M"; break;
                    case 'f': phoneme = "F"; break;
                    case 't': phoneme = "T"; break;
                    case 'd': phoneme = "D"; break;
                    case 'n': phoneme = "N"; break;
                    case 'l': phoneme = "L"; break;
                    case 'r': phoneme = "R"; break;
                    case 's': phoneme = "S"; break;
                    case 'k': phoneme = "K"; break;
                    case 'g': phoneme = "G"; break;
                    default: phoneme = "SIL"; break;
                }
                
                if (!string.IsNullOrEmpty(phoneme))
                    phonemes.Add(phoneme);
            }
            
            return phonemes.ToArray();
        }
        
        private string[] FrenchWordToPhonemes(string word)
        {
            // Implementación simplificada para francés
            // Similar al español pero con algunas diferencias
            return SpanishWordToPhonemes(word); // Placeholder
        }
        
        /// <summary>
        /// Mapea fonemas a visemas
        /// </summary>
        private VisemeData[] PhonemesToVisemes(string[] phonemes, SupportedLanguage language)
        {
            var visemes = new List<VisemeData>();
            
            if (!languagePhonemeMap.ContainsKey(language))
                language = SupportedLanguage.English; // Fallback
            
            var phonemeMap = languagePhonemeMap[language];
            
            for (int i = 0; i < phonemes.Length; i++)
            {
                string phoneme = phonemes[i];
                VisemeType visemeType = VisemeType.Silence;
                
                if (phonemeMap.ContainsKey(phoneme))
                {
                    visemeType = phonemeMap[phoneme];
                }
                else
                {
                    // Mapeo por defecto basado en similitud
                    visemeType = GetDefaultVisemeForPhoneme(phoneme);
                }
                
                var viseme = new VisemeData(0f, visemeType, animationSettings.globalIntensity);
                visemes.Add(viseme);
            }
            
            return visemes.ToArray();
        }
        
        /// <summary>
        /// Aplica timing a los visemas basado en duración estimada
        /// </summary>
        private VisemeData[] ApplyTiming(VisemeData[] visemes, float totalDuration)
        {
            if (visemes.Length == 0) return visemes;
            
            float timePerViseme = totalDuration / visemes.Length;
            
            for (int i = 0; i < visemes.Length; i++)
            {
                visemes[i].timestamp = i * timePerViseme;
                visemes[i].duration = Math.Max(timePerViseme, animationSettings.holdDuration);
            }
            
            return visemes;
        }
        
        /// <summary>
        /// Aplica coarticulación (influencia entre fonemas adyacentes)
        /// </summary>
        private VisemeData[] ApplyCoarticulation(VisemeData[] visemes)
        {
            for (int i = 1; i < visemes.Length - 1; i++)
            {
                var current = visemes[i];
                var previous = visemes[i - 1];
                var next = visemes[i + 1];
                
                // Ajustar parámetros faciales basándose en contexto
                current.jawOpen = (previous.jawOpen + current.jawOpen + next.jawOpen) / 3f;
                current.lipCornerPull = (previous.lipCornerPull + current.lipCornerPull + next.lipCornerPull) / 3f;
                current.lipPucker = (previous.lipPucker + current.lipPucker + next.lipPucker) / 3f;
            }
            
            return visemes;
        }
        
        /// <summary>
        /// Suaviza transiciones entre visemas
        /// </summary>
        private VisemeData[] SmoothTransitions(VisemeData[] visemes)
        {
            var smoothed = new List<VisemeData>();
            
            for (int i = 0; i < visemes.Length; i++)
            {
                if (i == 0 || i == visemes.Length - 1)
                {
                    smoothed.Add(visemes[i]);
                }
                else
                {
                    var prev = visemes[i - 1];
                    var curr = visemes[i];
                    var next = visemes[i + 1];
                    
                    // Crear visema suavizado
                    var smoothedViseme = curr.Clone();
                    float factor = animationSettings.smoothingFactor;
                    
                    smoothedViseme.jawOpen = Mathf.Lerp(curr.jawOpen, (prev.jawOpen + next.jawOpen) / 2f, factor);
                    smoothedViseme.lipCornerPull = Mathf.Lerp(curr.lipCornerPull, (prev.lipCornerPull + next.lipCornerPull) / 2f, factor);
                    smoothedViseme.lipPucker = Mathf.Lerp(curr.lipPucker, (prev.lipPucker + next.lipPucker) / 2f, factor);
                    
                    smoothed.Add(smoothedViseme);
                }
            }
            
            return smoothed.ToArray();
        }
        
        private string NormalizeText(string text)
        {
            // Remover puntuación y normalizar
            text = Regex.Replace(text, @"[^\w\s]", "");
            text = text.ToLower().Trim();
            return text;
        }
        
        private float EstimateAudioDuration(string text)
        {
            // Estimación simple: ~150 palabras por minuto
            int wordCount = text.Split(' ').Length;
            return (wordCount / 150f) * 60f;
        }
        
        private VisemeType GetDefaultVisemeForPhoneme(string phoneme)
        {
            // Mapeo por defecto basado en similitud fonética
            switch (phoneme.ToUpper())
            {
                case "P":
                case "B":
                case "M":
                    return VisemeType.PP;
                case "F":
                case "V":
                    return VisemeType.FF;
                case "T":
                case "D":
                case "N":
                case "L":
                    return VisemeType.DD;
                case "K":
                case "G":
                    return VisemeType.KK;
                case "S":
                case "Z":
                    return VisemeType.SS;
                case "R":
                    return VisemeType.RR;
                case "A":
                    return VisemeType.AA;
                case "E":
                    return VisemeType.E;
                case "I":
                    return VisemeType.I;
                case "O":
                    return VisemeType.O;
                case "U":
                    return VisemeType.U;
                case "W":
                    return VisemeType.W;
                case "Y":
                    return VisemeType.Y;
                default:
                    return VisemeType.Silence;
            }
        }
        
        private VisemeData[] GenerateFallbackVisemes(string text)
        {
            // Generar visemas básicos como fallback
            var visemes = new List<VisemeData>();
            float duration = EstimateAudioDuration(text);
            int visemeCount = Math.Max(1, text.Length / 5); // Aproximadamente un visema cada 5 caracteres
            
            for (int i = 0; i < visemeCount; i++)
            {
                float time = (i / (float)visemeCount) * duration;
                var viseme = new VisemeData(time, VisemeType.AA, 0.5f);
                visemes.Add(viseme);
            }
            
            return visemes.ToArray();
        }
        
        private void InitializePhonemeMapping()
        {
            languagePhonemeMap = new Dictionary<SupportedLanguage, Dictionary<string, VisemeType>>();
            
            // Mapeo para inglés
            var englishMap = new Dictionary<string, VisemeType>
            {
                {"P", VisemeType.PP}, {"B", VisemeType.PP}, {"M", VisemeType.PP},
                {"F", VisemeType.FF}, {"V", VisemeType.FF},
                {"T", VisemeType.DD}, {"D", VisemeType.DD}, {"N", VisemeType.DD}, {"L", VisemeType.DD},
                {"K", VisemeType.KK}, {"G", VisemeType.KK},
                {"S", VisemeType.SS}, {"Z", VisemeType.SS},
                {"R", VisemeType.RR},
                {"A", VisemeType.AA}, {"AE", VisemeType.AE},
                {"E", VisemeType.E}, {"EH", VisemeType.EH},
                {"I", VisemeType.I}, {"IH", VisemeType.IH},
                {"O", VisemeType.O}, {"AO", VisemeType.AO},
                {"U", VisemeType.U}, {"UH", VisemeType.UH},
                {"W", VisemeType.W}, {"Y", VisemeType.Y},
                {"SIL", VisemeType.Silence}, {"HH", VisemeType.Silence}
            };
            
            languagePhonemeMap[SupportedLanguage.English] = englishMap;
            
            // Mapeo similar para otros idiomas
            languagePhonemeMap[SupportedLanguage.Spanish] = new Dictionary<string, VisemeType>(englishMap);
            languagePhonemeMap[SupportedLanguage.French] = new Dictionary<string, VisemeType>(englishMap);
            languagePhonemeMap[SupportedLanguage.German] = new Dictionary<string, VisemeType>(englishMap);
        }
        
        private void LoadPhonemeDatabase()
        {
            // Cargar base de datos de fonemas si existe
            // En producción, cargar desde archivos de recursos
        }
    }
    
    [Serializable]
    public class PhonemeMapping
    {
        public SupportedLanguage language;
        public PhonemeVisemeMap[] mappings;
    }
    
    [Serializable]
    public class PhonemeVisemeMap
    {
        public string phoneme;
        public VisemeType viseme;
    }
}
