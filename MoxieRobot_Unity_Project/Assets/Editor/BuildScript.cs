using UnityEngine;
using UnityEditor;
using System.IO;

/// <summary>
/// Script de construcción para MoxieRobot con ElevenLabs TTS
/// </summary>
public class BuildScript
{
    private static readonly string BUILD_PATH = "Build";
    private static readonly string APK_NAME = "MoxieRobot_ElevenLabs.apk";
    
    [MenuItem("MoxieRobot/Build Android APK")]
    public static void BuildAndroid()
    {
        Debug.Log("Starting MoxieRobot Android build...");
        
        // Configurar settings de build
        ConfigureAndroidBuildSettings();
        
        // Configurar escenas
        string[] scenes = GetScenesToBuild();
        
        // Crear directorio de build
        string buildPath = Path.Combine(BUILD_PATH, "Android");
        if (!Directory.Exists(buildPath))
        {
            Directory.CreateDirectory(buildPath);
        }
        
        string fullPath = Path.Combine(buildPath, APK_NAME);
        
        // Construir APK
        BuildPipeline.BuildPlayer(scenes, fullPath, BuildTarget.Android, BuildOptions.None);
        
        Debug.Log($"Android build completed: {fullPath}");
    }
    
    [MenuItem("MoxieRobot/Build iOS")]
    public static void BuildiOS()
    {
        Debug.Log("Starting MoxieRobot iOS build...");
        
        // Configurar settings de build
        ConfigureiOSBuildSettings();
        
        // Configurar escenas
        string[] scenes = GetScenesToBuild();
        
        // Crear directorio de build
        string buildPath = Path.Combine(BUILD_PATH, "iOS");
        if (!Directory.Exists(buildPath))
        {
            Directory.CreateDirectory(buildPath);
        }
        
        // Construir proyecto Xcode
        BuildPipeline.BuildPlayer(scenes, buildPath, BuildTarget.iOS, BuildOptions.None);
        
        Debug.Log($"iOS build completed: {buildPath}");
    }
    
    [MenuItem("MoxieRobot/Build Windows")]
    public static void BuildWindows()
    {
        Debug.Log("Starting MoxieRobot Windows build...");
        
        // Configurar settings de build
        ConfigureWindowsBuildSettings();
        
        // Configurar escenas
        string[] scenes = GetScenesToBuild();
        
        // Crear directorio de build
        string buildPath = Path.Combine(BUILD_PATH, "Windows");
        if (!Directory.Exists(buildPath))
        {
            Directory.CreateDirectory(buildPath);
        }
        
        string fullPath = Path.Combine(buildPath, "MoxieRobot_ElevenLabs.exe");
        
        // Construir ejecutable
        BuildPipeline.BuildPlayer(scenes, fullPath, BuildTarget.StandaloneWindows64, BuildOptions.None);
        
        Debug.Log($"Windows build completed: {fullPath}");
    }
    
    [MenuItem("MoxieRobot/Validate Project")]
    public static void ValidateProject()
    {
        Debug.Log("Validating MoxieRobot project...");
        
        bool isValid = true;
        
        // Verificar escenas
        string[] scenes = GetScenesToBuild();
        if (scenes.Length == 0)
        {
            Debug.LogError("No scenes found to build!");
            isValid = false;
        }
        
        // Verificar componentes TTS
        var ttsManager = Object.FindObjectOfType<MoxieRobot.TTS.ElevenLabs.ElevenLabsTTSManager>();
        if (ttsManager == null)
        {
            Debug.LogWarning("ElevenLabsTTSManager not found in scene. Make sure to add it before building.");
        }
        
        // Verificar API Key
        if (ttsManager != null && string.IsNullOrEmpty(ttsManager.ApiKey))
        {
            Debug.LogWarning("ElevenLabs API Key not configured. Set it in the TTS Manager component.");
        }
        
        // Verificar configuración Android
        if (PlayerSettings.Android.targetSdkVersion < AndroidSdkVersions.AndroidApiLevel28)
        {
            Debug.LogWarning("Android Target SDK should be API Level 28 or higher for modern compatibility.");
        }
        
        if (isValid)
        {
            Debug.Log("✅ Project validation completed successfully!");
        }
        else
        {
            Debug.LogError("❌ Project validation failed. Please fix the issues above.");
        }
    }
    
    private static void ConfigureAndroidBuildSettings()
    {
        // Configuración básica Android
        PlayerSettings.companyName = "Embodied Inc";
        PlayerSettings.productName = "MoxieRobot ElevenLabs";
        PlayerSettings.applicationIdentifier = "com.embodied.moxierobot.elevenlabs";
        
        // Configuración Android específica
        PlayerSettings.Android.minSdkVersion = AndroidSdkVersions.AndroidApiLevel21;
        PlayerSettings.Android.targetSdkVersion = AndroidSdkVersions.AndroidApiLevel30;
        PlayerSettings.Android.bundleVersionCode = 1;
        PlayerSettings.bundleVersion = "1.0.0";
        
        // Permisos necesarios
        PlayerSettings.Android.forceInternetPermission = true;
        PlayerSettings.Android.forceSDCardPermission = false;
        
        // Configuración de arquitectura
        PlayerSettings.Android.targetArchitectures = AndroidArchitecture.ARM64 | AndroidArchitecture.ARMv7;
        
        // Configuración de scripting
        PlayerSettings.SetScriptingBackend(BuildTargetGroup.Android, ScriptingImplementation.IL2CPP);
        PlayerSettings.Android.targetDeviceFamily = AndroidTargetDeviceFamily.Universal;
        
        Debug.Log("Android build settings configured");
    }
    
    private static void ConfigureiOSBuildSettings()
    {
        // Configuración básica iOS
        PlayerSettings.companyName = "Embodied Inc";
        PlayerSettings.productName = "MoxieRobot ElevenLabs";
        PlayerSettings.applicationIdentifier = "com.embodied.moxierobot.elevenlabs";
        
        // Configuración iOS específica
        PlayerSettings.iOS.targetOSVersionString = "11.0";
        PlayerSettings.bundleVersion = "1.0.0";
        PlayerSettings.iOS.buildNumber = "1";
        
        // Configuración de scripting
        PlayerSettings.SetScriptingBackend(BuildTargetGroup.iOS, ScriptingImplementation.IL2CPP);
        PlayerSettings.iOS.targetDevice = iOSTargetDevice.iPhoneAndiPad;
        
        Debug.Log("iOS build settings configured");
    }
    
    private static void ConfigureWindowsBuildSettings()
    {
        // Configuración básica Windows
        PlayerSettings.companyName = "Embodied Inc";
        PlayerSettings.productName = "MoxieRobot ElevenLabs";
        
        // Configuración Windows específica
        PlayerSettings.bundleVersion = "1.0.0";
        PlayerSettings.SetScriptingBackend(BuildTargetGroup.Standalone, ScriptingImplementation.Mono2x);
        
        Debug.Log("Windows build settings configured");
    }
    
    private static string[] GetScenesToBuild()
    {
        // Obtener todas las escenas habilitadas en Build Settings
        var scenes = new System.Collections.Generic.List<string>();
        
        foreach (EditorBuildSettingsScene scene in EditorBuildSettings.scenes)
        {
            if (scene.enabled)
            {
                scenes.Add(scene.path);
            }
        }
        
        // Si no hay escenas en Build Settings, usar la escena principal
        if (scenes.Count == 0)
        {
            string mainScene = "Assets/Scenes/MoxieRobot_Main.unity";
            if (File.Exists(mainScene))
            {
                scenes.Add(mainScene);
            }
        }
        
        return scenes.ToArray();
    }
    
    [MenuItem("MoxieRobot/Setup Build Settings")]
    public static void SetupBuildSettings()
    {
        Debug.Log("Setting up build settings...");
        
        // Configurar escenas en Build Settings
        var scenes = new System.Collections.Generic.List<EditorBuildSettingsScene>();
        
        string mainScene = "Assets/Scenes/MoxieRobot_Main.unity";
        if (File.Exists(mainScene))
        {
            scenes.Add(new EditorBuildSettingsScene(mainScene, true));
        }
        
        EditorBuildSettings.scenes = scenes.ToArray();
        
        // Configurar Player Settings generales
        PlayerSettings.companyName = "Embodied Inc";
        PlayerSettings.productName = "MoxieRobot ElevenLabs";
        
        // Configurar símbolos de compilación
        string defines = PlayerSettings.GetScriptingDefineSymbolsForGroup(BuildTargetGroup.Android);
        if (!defines.Contains("ELEVENLABS_TTS"))
        {
            defines += ";ELEVENLABS_TTS";
            PlayerSettings.SetScriptingDefineSymbolsForGroup(BuildTargetGroup.Android, defines);
        }
        
        Debug.Log("✅ Build settings configured successfully!");
    }
    
    [MenuItem("MoxieRobot/Clean Build Directory")]
    public static void CleanBuildDirectory()
    {
        if (Directory.Exists(BUILD_PATH))
        {
            Directory.Delete(BUILD_PATH, true);
            Debug.Log("Build directory cleaned");
        }
        
        // Recrear directorio
        Directory.CreateDirectory(BUILD_PATH);
        Debug.Log("Build directory recreated");
    }
    
    [MenuItem("MoxieRobot/Open Build Directory")]
    public static void OpenBuildDirectory()
    {
        string fullPath = Path.GetFullPath(BUILD_PATH);
        
        if (!Directory.Exists(fullPath))
        {
            Directory.CreateDirectory(fullPath);
        }
        
        EditorUtility.RevealInFinder(fullPath);
    }
}
