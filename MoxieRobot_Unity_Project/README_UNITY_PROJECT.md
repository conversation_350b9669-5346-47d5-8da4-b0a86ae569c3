# MoxieRobot Unity Project - ElevenLabs TTS Integration

Este es el **proyecto Unity completo** con la integración de ElevenLabs TTS para MoxieRobot. Incluye todo el código fuente, configuración y herramientas necesarias para compilar y desplegar la aplicación.

## 🚀 Instalación Rápida

### Opción 1: Build e Instalación Automática
```bash
# Construir e instalar automáticamente en el dispositivo
./build_and_install.sh

# Solo construir APK
./build_and_install.sh build

# Solo instalar APK existente
./build_and_install.sh install

# Ver logs de la aplicación
./build_and_install.sh logs
```

### Opción 2: Abrir en Unity Editor
1. Abrir Unity Hub
2. Seleccionar "Add project from disk"
3. Navegar a esta carpeta (`MoxieRobot_Unity_Project`)
4. Abrir el proyecto en Unity 2019.4 LTS o superior

## 📁 Estructura del Proyecto

```
MoxieRobot_Unity_Project/
├── 🔧 build_and_install.sh          # Script de build e instalación automática
├── 📄 README_UNITY_PROJECT.md       # Esta documentación
│
├── Assets/
│   ├── 🎬 Scenes/
│   │   └── MoxieRobot_Main.unity     # Escena principal con TTS configurado
│   │
│   ├── 📜 Scripts/
│   │   ├── MoxieRobotDemo.cs         # Script de demostración
│   │   └── TTS/                      # Sistema TTS completo
│   │       ├── Core/                 # Componentes centrales
│   │       ├── ElevenLabs/           # Integración ElevenLabs
│   │       └── Visemes/              # Sistema de visemas
│   │
│   └── 🔧 Editor/
│       └── BuildScript.cs            # Scripts de construcción Unity
│
├── ProjectSettings/                  # Configuración Unity
├── Packages/                         # Dependencias del proyecto
└── Build/                           # Directorio de builds generados
```

## ⚙️ Configuración Inicial

### 1. Configurar API Key de ElevenLabs

**En Unity Editor:**
1. Abrir la escena `Assets/Scenes/MoxieRobot_Main.unity`
2. Seleccionar el GameObject `TTS_Manager`
3. En el componente `ElevenLabsTTSManager`, configurar:
   - **API Key**: Tu clave de API de ElevenLabs
   - **Default Voice ID**: ID de voz por defecto (ej: `21m00Tcm4TlvDq8ikWAM`)
   - **Default Language**: Idioma por defecto

**En el código (alternativo):**
```csharp
// En MoxieRobotDemo.cs o cualquier script
var ttsManager = FindObjectOfType<ElevenLabsTTSManager>();
ttsManager.SetApiKey("tu-api-key-aqui");
```

### 2. Verificar Configuración Android

**En Unity Editor:**
1. Ir a `File > Build Settings`
2. Seleccionar `Android` como plataforma
3. Ir a `Player Settings`
4. Configurar:
   - **Company Name**: Embodied Inc
   - **Product Name**: MoxieRobot ElevenLabs
   - **Package Name**: `com.embodied.moxierobot.elevenlabs`
   - **Minimum API Level**: Android 5.0 (API Level 21)
   - **Target API Level**: Android 11 (API Level 30)

## 🔨 Métodos de Construcción

### Método 1: Script Automático (Recomendado)
```bash
# Build completo e instalación
./build_and_install.sh

# El script automáticamente:
# 1. Verifica prerrequisitos
# 2. Construye el APK (Unity o método alternativo)
# 3. Instala en el dispositivo
# 4. Inicia la aplicación
# 5. Opcionalmente muestra logs
```

### Método 2: Unity Editor
1. Abrir el proyecto en Unity
2. Ir a `MoxieRobot > Setup Build Settings` (configura automáticamente)
3. Ir a `MoxieRobot > Build Android APK`
4. El APK se genera en `Build/Android/MoxieRobot_ElevenLabs.apk`

### Método 3: Unity Command Line
```bash
# Desde la carpeta del proyecto
/path/to/Unity -batchmode -quit -projectPath . -buildTarget Android -executeMethod BuildScript.BuildAndroid
```

### Método 4: Build Alternativo (Sin Unity)
```bash
# Si Unity no está disponible, el script usa apktool
./build_and_install.sh build

# Esto:
# 1. Toma el APK original como base
# 2. Lo modifica para incluir cambios necesarios
# 3. Lo recompila y firma
```

## 🧪 Testing y Validación

### Tests Automáticos
```bash
# En Unity Editor, ir a:
# MoxieRobot > Validate Project

# O desde línea de comandos:
/path/to/Unity -batchmode -quit -projectPath . -executeMethod BuildScript.ValidateProject
```

### Tests Manuales
1. **Compatibilidad Legacy**:
   ```csharp
   TTSRequestCloudHandler.SpeakText("Hello world!");
   ```

2. **Nuevas Funcionalidades**:
   ```csharp
   ttsManager.SpeakText("¡Hola mundo!", null, SupportedLanguage.Spanish);
   ```

3. **Demo Interactivo**:
   - Ejecutar la aplicación
   - El script `MoxieRobotDemo` ejecuta demos automáticos
   - Probar controles manuales en la UI

## 📱 Instalación en Dispositivo

### Prerrequisitos
- Dispositivo Android con USB Debugging habilitado
- ADB instalado y en PATH
- Dispositivo conectado por USB

### Instalación Manual
```bash
# Verificar dispositivo conectado
adb devices

# Instalar APK
adb install -r Build/Android/MoxieRobot_ElevenLabs.apk

# Iniciar aplicación
adb shell am start -n com.embodied.moxierobot.elevenlabs/com.unity3d.player.UnityPlayerActivity
```

### Ver Logs
```bash
# Logs en tiempo real
adb logcat | grep -E "(Unity|MoxieRobot|TTS|ElevenLabs)"

# O usar el script
./build_and_install.sh logs
```

## 🔧 Solución de Problemas

### Problema: "Unity not found"
```bash
# Editar build_and_install.sh y ajustar UNITY_PATH
# O instalar Unity en ubicación estándar
```

### Problema: "API Key not configured"
```bash
# Configurar en Unity Editor o en código:
ttsManager.SetApiKey("tu-api-key");
```

### Problema: "Device not found"
```bash
# Verificar conexión ADB
adb devices

# Habilitar USB Debugging en dispositivo
# Configuración > Opciones de desarrollador > Depuración USB
```

### Problema: "Build failed"
```bash
# Verificar logs
cat build_install.log

# Usar build alternativo
./build_and_install.sh build
```

## 🎯 Funcionalidades Implementadas

### ✅ Compatibilidad Total
- Todo el código anterior funciona sin cambios
- Eventos legacy mantienen funcionalidad
- Interfaces existentes preservadas

### ✅ Nuevas Capacidades
- **15+ idiomas** con detección automática
- **100+ voces** de ElevenLabs
- **Cache inteligente** para optimización
- **Visemas avanzados** para animación facial
- **Configuración flexible** por contexto

### ✅ Herramientas de Desarrollo
- **Build automático** multiplataforma
- **Validación de proyecto** integrada
- **Logs estructurados** para debugging
- **Demo interactivo** para testing

## 📊 Comparación con Sistema Anterior

| Aspecto | CereVoice (Anterior) | ElevenLabs (Nuevo) |
|---------|---------------------|-------------------|
| **Dependencias** | 44MB libcerevoice_eng.so | 0MB (cloud API) |
| **Idiomas** | 1 (Inglés) | 15+ idiomas |
| **Voces** | ~5 voces | 100+ voces |
| **Calidad** | TTS estándar | IA de última generación |
| **Cache** | No | Inteligente con persistencia |
| **Configuración** | Limitada | Extensa y flexible |
| **Compatibilidad** | N/A | 100% con código anterior |

## 🚀 Próximos Pasos

1. **Configurar API Key** de ElevenLabs
2. **Ejecutar build automático**: `./build_and_install.sh`
3. **Probar en dispositivo** MoxieRobot
4. **Validar funcionalidades** con el demo
5. **Integrar con código existente** si es necesario
6. **Optimizar configuraciones** para producción

## 📞 Soporte

- **Logs de build**: `build_install.log`
- **Logs de Unity**: `build_install.log.unity`
- **Logs de aplicación**: `./build_and_install.sh logs`

---

**🎉 ¡Proyecto listo para usar!** 

El sistema MoxieRobot ahora cuenta con TTS de última generación manteniendo total compatibilidad con el código anterior.
