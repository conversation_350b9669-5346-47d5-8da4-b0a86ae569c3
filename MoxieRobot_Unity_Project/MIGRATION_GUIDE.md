# Guía de Migración: CereVoice → ElevenLabs TTS

Esta guía detalla el proceso completo de migración del sistema TTS CereVoice al nuevo sistema ElevenLabs para MoxieRobot.

## 📋 Resumen de Cambios

### Sistema Anterior (CereVoice)
- **Biblioteca nativa**: `libcerevoice_eng.so` (44MB)
- **Integración**: P/Invoke calls desde C#
- **Idiomas**: Solo inglés
- **Voces**: Limitadas, calidad estándar
- **Cache**: Básico o inexistente
- **Visemas**: Generación simple

### Sistema Nuevo (ElevenLabs)
- **API Cloud**: Integración HTTP REST
- **Idiomas**: 15+ idiomas soportados
- **Voces**: 100+ voces de alta calidad con IA
- **Cache**: Sistema inteligente con persistencia
- **Visemas**: Generación avanzada con coarticulación
- **Compatibilidad**: 100% compatible con código anterior

## 🔄 Proceso de Migración

### Fase 1: Preparación (30 minutos)

#### 1.1 Backup del Proyecto Original
```bash
# Crear backup completo
cp -r MoxieRobot_Original MoxieRobot_Backup_$(date +%Y%m%d)

# Backup específico de archivos TTS
mkdir TTS_Backup
cp -r Assets/Scripts/*TTS* TTS_Backup/
cp -r Assets/Plugins/Android/libcerevoice* TTS_Backup/
```

#### 1.2 Documentar Integraciones Actuales
```csharp
// Buscar todas las referencias a CereVoice en el código
// Archivos típicos a revisar:
// - TTSRequestCloudHandler.cs
// - TTSSpeechMarkPlayback.cs  
// - Cualquier script que use P/Invoke con CereVoice
```

#### 1.3 Obtener API Key de ElevenLabs
1. Registrarse en [ElevenLabs](https://elevenlabs.io)
2. Ir a Profile → API Keys
3. Generar nueva API key
4. Guardar de forma segura

### Fase 2: Instalación del Nuevo Sistema (45 minutos)

#### 2.1 Importar Código Fuente
```bash
# Copiar todos los scripts del nuevo sistema
cp -r MoxieRobot_ElevenLabs_Source/Assets/Scripts/TTS/ Assets/Scripts/
```

#### 2.2 Configurar Dependencias
```csharp
// En Unity, verificar que estas librerías estén disponibles:
// - Newtonsoft.Json (para serialización JSON)
// - UnityWebRequest (incluido en Unity)
// - System.Security.Cryptography (para cache hashing)
```

#### 2.3 Configurar Componentes en Escena

```csharp
// 1. Crear GameObject "TTS_Manager"
GameObject ttsManager = new GameObject("TTS_Manager");

// 2. Añadir componentes principales
ttsManager.AddComponent<ElevenLabsTTSManager>();
ttsManager.AddComponent<TTSCompatibilityAdapter>();
ttsManager.AddComponent<VoiceSelectionManager>();
ttsManager.AddComponent<MultiLanguageSupport>();
ttsManager.AddComponent<AudioCacheManager>();
ttsManager.AddComponent<EnhancedVisemeGenerator>();

// 3. Configurar AudioSource
AudioSource audioSource = ttsManager.AddComponent<AudioSource>();
audioSource.playOnAwake = false;
audioSource.spatialBlend = 0f; // 2D sound
```

### Fase 3: Configuración (30 minutos)

#### 3.1 Configurar ElevenLabsTTSManager
```csharp
// En el Inspector de Unity:
[SerializeField] private string apiKey = "tu-api-key-aqui";
[SerializeField] private string defaultVoiceId = "21m00Tcm4TlvDq8ikWAM"; // Rachel
[SerializeField] private SupportedLanguage defaultLanguage = SupportedLanguage.English;
[SerializeField] private bool enableDebugLogs = true; // Para testing inicial
```

#### 3.2 Configurar Cache
```csharp
// AudioCacheManager settings:
[SerializeField] private long maxCacheSizeBytes = 100 * 1024 * 1024; // 100MB
[SerializeField] private int maxCacheEntries = 1000;
[SerializeField] private float cacheExpirationDays = 7f;
[SerializeField] private bool enablePersistentCache = true;

// Frases comunes para precargar:
[SerializeField] private string[] commonPhrases = {
    "Hello!",
    "How are you today?",
    "That's great!",
    "Let's play a game!",
    "Goodbye!"
};
```

#### 3.3 Configurar Visemas
```csharp
// EnhancedVisemeGenerator settings:
[SerializeField] private bool enableAdvancedPhonemeMapping = true;
[SerializeField] private bool enableCoarticulation = true;

// VisemeAnimationSettings:
public VisemeAnimationSettings animationSettings = new VisemeAnimationSettings
{
    transitionSpeed = 5f,
    holdDuration = 0.05f,
    blendTime = 0.02f,
    globalIntensity = 1f,
    enableSmoothing = true,
    smoothingFactor = 0.8f
};
```

### Fase 4: Actualización de Código (60 minutos)

#### 4.1 Código que NO Necesita Cambios
```csharp
// ✅ Este código sigue funcionando exactamente igual:
TTSRequestCloudHandler.SpeakText("Hello world!");
TTSRequestCloudHandler.StopTTS();
bool isSpeaking = TTSRequestCloudHandler.IsSpeaking;

// ✅ Eventos legacy también funcionan:
TTSRequestCloudHandler.OnTTSCompleteEvent += OnTTSComplete;
TTSRequestCloudHandler.OnTTSStreamEvent += OnTTSStream;
```

#### 4.2 Código Opcional para Mejorar (Nuevas Funcionalidades)
```csharp
// 🆕 Usar nuevas funcionalidades cuando sea conveniente:

// Selección específica de voz
ttsManager.SpeakText("Hello!", "21m00Tcm4TlvDq8ikWAM");

// Configuración avanzada
var settings = TTSSettings.ForConversation();
ttsManager.SpeakText("Hello!", null, null, settings);

// Soporte multiidioma
ttsManager.SetLanguage(SupportedLanguage.Spanish);
ttsManager.SpeakText("¡Hola mundo!");
```

#### 4.3 Remover Referencias Antiguas (Opcional)
```csharp
// ❌ Remover estas líneas si existen:
// [DllImport("libcerevoice_eng")]
// private static extern IntPtr CSharp_CPRCEN_engine_load_voice(...);

// ❌ Remover archivos:
// - libcerevoice_eng.so
// - Cualquier wrapper P/Invoke de CereVoice
```

### Fase 5: Testing y Validación (45 minutos)

#### 5.1 Tests Básicos
```csharp
// Test 1: Funcionalidad básica
[Test]
public void TestBasicTTS()
{
    TTSRequestCloudHandler.SpeakText("Test message");
    Assert.IsTrue(TTSRequestCloudHandler.IsSpeaking);
}

// Test 2: Compatibilidad con código anterior
[Test] 
public void TestLegacyCompatibility()
{
    bool eventFired = false;
    TTSRequestCloudHandler.OnTTSCompleteEvent += (clip) => eventFired = true;
    
    TTSRequestCloudHandler.SpeakText("Test");
    // Esperar a que complete...
    Assert.IsTrue(eventFired);
}

// Test 3: Nuevas funcionalidades
[Test]
public void TestMultiLanguage()
{
    var ttsManager = FindObjectOfType<ElevenLabsTTSManager>();
    ttsManager.SetLanguage(SupportedLanguage.Spanish);
    ttsManager.SpeakText("Hola mundo");
    Assert.AreEqual(SupportedLanguage.Spanish, ttsManager.CurrentLanguage);
}
```

#### 5.2 Tests de Rendimiento
```csharp
// Test de cache
[Test]
public void TestCachePerformance()
{
    var cacheManager = FindObjectOfType<AudioCacheManager>();
    
    // Primera llamada (sin cache)
    var startTime = Time.realtimeSinceStartup;
    ttsManager.SpeakText("Cache test message");
    var firstCallTime = Time.realtimeSinceStartup - startTime;
    
    // Segunda llamada (con cache)
    startTime = Time.realtimeSinceStartup;
    ttsManager.SpeakText("Cache test message");
    var secondCallTime = Time.realtimeSinceStartup - startTime;
    
    Assert.IsTrue(secondCallTime < firstCallTime * 0.5f); // 50% más rápido
}
```

#### 5.3 Tests de Integración
```csharp
// Test completo de flujo TTS
[Test]
public IEnumerator TestCompleteTTSFlow()
{
    bool completed = false;
    AudioClip resultClip = null;
    
    TTSRequestCloudHandler.OnTTSCompleteEvent += (clip) => {
        completed = true;
        resultClip = clip;
    };
    
    TTSRequestCloudHandler.SpeakText("Integration test message");
    
    // Esperar hasta 10 segundos
    float timeout = 10f;
    while (!completed && timeout > 0)
    {
        yield return new WaitForSeconds(0.1f);
        timeout -= 0.1f;
    }
    
    Assert.IsTrue(completed, "TTS should complete within timeout");
    Assert.IsNotNull(resultClip, "Audio clip should be generated");
    Assert.IsTrue(resultClip.length > 0, "Audio clip should have content");
}
```

### Fase 6: Optimización y Configuración Final (30 minutos)

#### 6.1 Optimizar Configuración de Cache
```csharp
// Ajustar según el dispositivo y uso esperado
var cacheManager = FindObjectOfType<AudioCacheManager>();

// Para dispositivos con memoria limitada:
cacheManager.maxCacheSizeBytes = 50 * 1024 * 1024; // 50MB
cacheManager.maxCacheEntries = 500;

// Para dispositivos con más memoria:
cacheManager.maxCacheSizeBytes = 200 * 1024 * 1024; // 200MB
cacheManager.maxCacheEntries = 2000;
```

#### 6.2 Configurar Voces por Defecto
```csharp
// Configurar voces recomendadas por contexto
var voiceManager = FindObjectOfType<VoiceSelectionManager>();

// Voz para conversación general (femenina, amigable)
voiceManager.SelectVoice("21m00Tcm4TlvDq8ikWAM"); // Rachel

// Para contenido infantil
voiceManager.SelectBestVoice(
    VoiceGender.Female, 
    VoiceAge.Young, 
    VoiceStyle.Friendly
);
```

#### 6.3 Configurar Idiomas
```csharp
// Configurar detección automática
var multiLang = FindObjectOfType<MultiLanguageSupport>();
multiLang.autoDetectLanguage = true;

// Configurar idiomas preferidos
multiLang.SetLanguage(SupportedLanguage.English); // Por defecto
```

## 🚨 Problemas Comunes y Soluciones

### Problema 1: "API Key Invalid"
```csharp
// Solución:
// 1. Verificar que la API key sea correcta
// 2. Verificar que la cuenta tenga créditos
// 3. Verificar conectividad a internet

// Debug:
Debug.Log($"Using API Key: {apiKey.Substring(0, 8)}...");
```

### Problema 2: "Audio no se reproduce"
```csharp
// Solución:
// 1. Verificar que AudioSource esté configurado
// 2. Verificar que el volumen no esté en 0
// 3. Verificar que no haya otros AudioSources interfiriendo

// Debug:
var audioSource = GetComponent<AudioSource>();
Debug.Log($"AudioSource volume: {audioSource.volume}");
Debug.Log($"AudioSource clip: {audioSource.clip?.name}");
```

### Problema 3: "Visemas no se sincronizan"
```csharp
// Solución:
// 1. Verificar que EnhancedVisemeGenerator esté activo
// 2. Verificar configuración de timing
// 3. Verificar que el controlador facial esté conectado

// Debug:
var visemeGen = FindObjectOfType<EnhancedVisemeGenerator>();
Debug.Log($"Viseme generator active: {visemeGen.enabled}");
```

### Problema 4: "Cache no funciona"
```csharp
// Solución:
// 1. Verificar permisos de escritura
// 2. Verificar espacio en disco
// 3. Verificar configuración de cache

// Debug:
var cacheManager = FindObjectOfType<AudioCacheManager>();
Debug.Log($"Cache directory: {Application.persistentDataPath}/AudioCache");
Debug.Log($"Cache enabled: {cacheManager.IsCacheEnabled}");
```

## ✅ Checklist de Migración

### Pre-Migración
- [ ] Backup completo del proyecto
- [ ] Documentar integraciones actuales
- [ ] Obtener API Key de ElevenLabs
- [ ] Verificar requisitos de Unity

### Durante Migración
- [ ] Importar código fuente nuevo
- [ ] Configurar componentes en escena
- [ ] Configurar API Key y settings
- [ ] Configurar cache y optimizaciones

### Post-Migración
- [ ] Ejecutar tests básicos
- [ ] Verificar compatibilidad con código anterior
- [ ] Probar nuevas funcionalidades
- [ ] Optimizar configuración para producción
- [ ] Documentar cambios específicos del proyecto

### Validación Final
- [ ] TTS básico funciona
- [ ] Eventos legacy funcionan
- [ ] Cache funciona correctamente
- [ ] Visemas se sincronizan
- [ ] Rendimiento es aceptable
- [ ] No hay memory leaks
- [ ] Funciona en dispositivo target

## 📊 Métricas de Éxito

### Rendimiento
- **Latencia primera llamada**: < 2 segundos
- **Latencia cache hit**: < 100ms
- **Uso de memoria**: < 50MB adicionales
- **Tasa de cache hit**: > 70% después de uso normal

### Calidad
- **Calidad de audio**: Notablemente mejor que CereVoice
- **Sincronización visemas**: > 95% precisión
- **Estabilidad**: Sin crashes relacionados con TTS
- **Compatibilidad**: 100% código anterior funciona

### Funcionalidad
- **Idiomas**: Al menos 5 idiomas funcionando
- **Voces**: Al menos 10 voces disponibles
- **Cache**: Persistencia entre sesiones
- **Configuración**: Todas las opciones accesibles

## 🎯 Próximos Pasos

Después de completar la migración:

1. **Monitorear rendimiento** en producción
2. **Recopilar feedback** de usuarios
3. **Optimizar configuraciones** basándose en uso real
4. **Explorar funcionalidades avanzadas** como voice cloning
5. **Implementar analytics** para uso de TTS
6. **Considerar optimizaciones** adicionales

---

**¡Migración completada exitosamente!** 🎉

El sistema MoxieRobot ahora cuenta con TTS de última generación con ElevenLabs, manteniendo total compatibilidad con el código anterior y añadiendo potentes nuevas funcionalidades.
