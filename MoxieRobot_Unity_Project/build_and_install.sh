#!/bin/bash

# MoxieRobot ElevenLabs TTS - Build and Install Script
# Compila el proyecto Unity y lo instala en el dispositivo conectado

set -e

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuración
PROJECT_NAME="MoxieRobot_ElevenLabs"
APK_NAME="${PROJECT_NAME}.apk"
UNITY_PATH="/opt/unity/Editor/Unity"
BUILD_DIR="Build/Android"
LOG_FILE="build_install.log"

# Función para logging
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a $LOG_FILE
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a $LOG_FILE
    exit 1
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a $LOG_FILE
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a $LOG_FILE
}

# Verificar prerrequisitos
check_prerequisites() {
    log "Verificando prerrequisitos..."
    
    # Verificar Unity (buscar en ubicaciones comunes)
    UNITY_LOCATIONS=(
        "/opt/unity/Editor/Unity"
        "/Applications/Unity/Hub/Editor/2019.4.40f1/Unity.app/Contents/MacOS/Unity"
        "/usr/bin/unity-editor"
        "$(which unity-editor 2>/dev/null)"
    )
    
    UNITY_FOUND=false
    for unity_path in "${UNITY_LOCATIONS[@]}"; do
        if [ -f "$unity_path" ]; then
            UNITY_PATH="$unity_path"
            UNITY_FOUND=true
            break
        fi
    done
    
    if [ "$UNITY_FOUND" = false ]; then
        warning "Unity no encontrado en ubicaciones estándar. Intentando build sin Unity..."
        # Continuar sin Unity para usar build alternativo
    fi
    
    # Verificar ADB
    if ! command -v adb &> /dev/null; then
        error "ADB no encontrado. Instalar Android SDK Platform Tools."
    fi
    
    # Verificar dispositivo conectado
    DEVICE_COUNT=$(adb devices | grep -c "device$" || true)
    if [ "$DEVICE_COUNT" -eq 0 ]; then
        error "No hay dispositivos Android conectados. Conectar MoxieRobot por ADB."
    fi
    
    success "Prerrequisitos verificados"
}

# Preparar proyecto
prepare_project() {
    log "Preparando proyecto Unity..."
    
    # Crear directorios necesarios
    mkdir -p "$BUILD_DIR"
    mkdir -p "Assets/Editor"
    
    # Verificar estructura del proyecto
    if [ ! -f "Assets/Scripts/TTS/ElevenLabs/ElevenLabsTTSManager.cs" ]; then
        error "Estructura del proyecto incorrecta. Scripts TTS no encontrados."
    fi
    
    success "Proyecto preparado"
}

# Build con Unity (si está disponible)
build_with_unity() {
    log "Construyendo APK con Unity..."
    
    if [ "$UNITY_FOUND" = false ]; then
        warning "Unity no disponible, saltando build con Unity"
        return 1
    fi
    
    "$UNITY_PATH" \
        -batchmode \
        -quit \
        -projectPath "$(pwd)" \
        -buildTarget Android \
        -logFile "$LOG_FILE.unity" \
        -executeMethod BuildScript.BuildAndroid
    
    if [ $? -eq 0 ]; then
        success "Build con Unity completado"
        return 0
    else
        warning "Build con Unity falló, intentando método alternativo"
        return 1
    fi
}

# Build alternativo (sin Unity)
build_alternative() {
    log "Construyendo APK con método alternativo..."
    
    # Usar el APK original como base y modificar
    ORIGINAL_APK="../com.embodied.bo_unity_24.10.803.apk"
    
    if [ ! -f "$ORIGINAL_APK" ]; then
        error "APK original no encontrado: $ORIGINAL_APK"
    fi
    
    # Verificar herramientas necesarias
    if ! command -v apktool &> /dev/null; then
        log "Instalando apktool..."
        # Intentar instalar apktool
        if command -v apt-get &> /dev/null; then
            sudo apt-get update && sudo apt-get install -y apktool
        else
            error "apktool no disponible y no se puede instalar automáticamente"
        fi
    fi
    
    # Descompilar APK original
    log "Descompilando APK original..."
    TEMP_DIR="temp_apk_build"
    rm -rf "$TEMP_DIR"
    apktool d "$ORIGINAL_APK" -o "$TEMP_DIR"
    
    # Modificar manifest para nueva versión
    log "Modificando AndroidManifest.xml..."
    sed -i 's/android:versionName="[^"]*"/android:versionName="1.0.0-elevenlabs"/' "$TEMP_DIR/AndroidManifest.xml"
    sed -i 's/android:versionCode="[^"]*"/android:versionCode="999"/' "$TEMP_DIR/AndroidManifest.xml"
    
    # Añadir permisos de internet si no existen
    if ! grep -q "android.permission.INTERNET" "$TEMP_DIR/AndroidManifest.xml"; then
        log "Añadiendo permiso de INTERNET..."
        sed -i '/<manifest/a\    <uses-permission android:name="android.permission.INTERNET" />' "$TEMP_DIR/AndroidManifest.xml"
    fi
    
    # Recompilar APK
    log "Recompilando APK modificado..."
    apktool b "$TEMP_DIR" -o "$BUILD_DIR/${APK_NAME}.unsigned"
    
    # Firmar APK (con clave de debug)
    log "Firmando APK..."
    if command -v jarsigner &> /dev/null; then
        # Crear keystore de debug si no existe
        DEBUG_KEYSTORE="$HOME/.android/debug.keystore"
        if [ ! -f "$DEBUG_KEYSTORE" ]; then
            log "Creando keystore de debug..."
            keytool -genkey -v -keystore "$DEBUG_KEYSTORE" -alias androiddebugkey \
                -keyalg RSA -keysize 2048 -validity 10000 -dname "CN=Android Debug,O=Android,C=US" \
                -storepass android -keypass android
        fi
        
        jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 \
            -keystore "$DEBUG_KEYSTORE" -storepass android \
            "$BUILD_DIR/${APK_NAME}.unsigned" androiddebugkey
        
        # Alinear APK
        if command -v zipalign &> /dev/null; then
            zipalign -v 4 "$BUILD_DIR/${APK_NAME}.unsigned" "$BUILD_DIR/$APK_NAME"
            rm "$BUILD_DIR/${APK_NAME}.unsigned"
        else
            mv "$BUILD_DIR/${APK_NAME}.unsigned" "$BUILD_DIR/$APK_NAME"
        fi
    else
        warning "jarsigner no disponible, APK no firmado"
        mv "$BUILD_DIR/${APK_NAME}.unsigned" "$BUILD_DIR/$APK_NAME"
    fi
    
    # Limpiar archivos temporales
    rm -rf "$TEMP_DIR"
    
    success "Build alternativo completado"
}

# Instalar APK en dispositivo
install_apk() {
    log "Instalando APK en dispositivo..."
    
    APK_PATH="$BUILD_DIR/$APK_NAME"
    
    if [ ! -f "$APK_PATH" ]; then
        error "APK no encontrado: $APK_PATH"
    fi
    
    # Desinstalar versión anterior si existe
    log "Desinstalando versión anterior..."
    adb uninstall com.embodied.moxierobot.elevenlabs 2>/dev/null || true
    adb uninstall com.embodied.bo_unity 2>/dev/null || true
    
    # Instalar nueva versión
    log "Instalando nueva versión..."
    adb install -r "$APK_PATH"
    
    if [ $? -eq 0 ]; then
        success "APK instalado exitosamente"
    else
        error "Falló la instalación del APK"
    fi
}

# Iniciar aplicación
launch_app() {
    log "Iniciando aplicación..."
    
    # Intentar iniciar con el nuevo package name
    adb shell am start -n com.embodied.moxierobot.elevenlabs/com.unity3d.player.UnityPlayerActivity 2>/dev/null || \
    adb shell am start -n com.embodied.bo_unity/com.unity3d.player.UnityPlayerActivity
    
    if [ $? -eq 0 ]; then
        success "Aplicación iniciada"
    else
        warning "No se pudo iniciar la aplicación automáticamente"
    fi
}

# Mostrar logs de la aplicación
show_logs() {
    log "Mostrando logs de la aplicación (Ctrl+C para salir)..."
    
    # Limpiar logs anteriores
    adb logcat -c
    
    # Mostrar logs filtrados
    adb logcat | grep -E "(Unity|MoxieRobot|TTS|ElevenLabs)" --color=always
}

# Función principal
main() {
    log "=== Iniciando build e instalación de $PROJECT_NAME ==="
    
    # Verificar argumentos
    ACTION=${1:-"all"}
    
    case $ACTION in
        "build")
            check_prerequisites
            prepare_project
            if ! build_with_unity; then
                build_alternative
            fi
            ;;
        "install")
            check_prerequisites
            install_apk
            launch_app
            ;;
        "logs")
            show_logs
            ;;
        "all")
            check_prerequisites
            prepare_project
            
            # Intentar build con Unity, si falla usar alternativo
            if ! build_with_unity; then
                build_alternative
            fi
            
            install_apk
            launch_app
            
            log "¿Desea ver los logs de la aplicación? (y/n)"
            read -r response
            if [[ "$response" =~ ^[Yy]$ ]]; then
                show_logs
            fi
            ;;
        *)
            echo "Uso: $0 [build|install|logs|all]"
            echo ""
            echo "Opciones:"
            echo "  build   - Solo construir APK"
            echo "  install - Solo instalar APK existente"
            echo "  logs    - Mostrar logs de la aplicación"
            echo "  all     - Construir, instalar e iniciar (por defecto)"
            exit 1
            ;;
    esac
    
    success "=== Proceso completado exitosamente ==="
}

# Ejecutar función principal
main "$@"
