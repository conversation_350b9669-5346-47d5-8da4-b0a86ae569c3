!!brut.androlib.meta.MetaInfo
apkFileName: com.embodied.bo_unity_24.10.803.apk
compressionType: false
doNotCompress:
- resources.arsc
- classes.dex
- assets/DO_NOT_DELETE.txt
- assets/bin/Data/e8a51ceab4352a74083e42e62c209e9d.resource
- assets/bin/Data/38b413953de6adc44938277f34162e84.resource
- assets/bin/Data/3a0b201dc0865114393e09239e428e44.resource
- assets/bin/Data/5a9bc0f773dc74412af60dac2c4c5a27.resource
- assets/bin/Data/sharedassets1.resource
- assets/bin/Data/adf4ac320acd342dc9e1e3f5f71bcc82.resource
- assets/bin/Data/d43ce13ae635447e6b060e090ad505cc.resource
- png
- lib/armeabi-v7a/libMonoPosixHelper.so
- lib/armeabi-v7a/libbo-analytics.so
- lib/armeabi-v7a/libbo-audio.so
- lib/armeabi-v7a/libbo-brain.so
- lib/armeabi-v7a/libbo-dispatch.so
- lib/armeabi-v7a/libbo-fusion.so
- lib/armeabi-v7a/libbo-logger.so
- lib/armeabi-v7a/libbo-system-monitor.so
- lib/armeabi-v7a/libbo-vision.so
- lib/armeabi-v7a/libbsk.so
- lib/armeabi-v7a/libc++_shared.so
- lib/armeabi-v7a/libcerevoice_eng.so
- lib/armeabi-v7a/libchatscript.so
- lib/armeabi-v7a/libdevset.so
- lib/armeabi-v7a/libev.so
- lib/armeabi-v7a/libiconv.so
- lib/armeabi-v7a/liblizzerface.so
- lib/armeabi-v7a/libmain.so
- lib/armeabi-v7a/libmonobdwgc-2.0.so
- lib/armeabi-v7a/libmxnet.so
- lib/armeabi-v7a/libnative-lib.so
- lib/armeabi-v7a/librfc.so
- lib/armeabi-v7a/librobinface.so
- lib/armeabi-v7a/libtensorflowlite.so
- lib/armeabi-v7a/libtensorflowlite_gpu_delegate.so
- lib/armeabi-v7a/libunity.so
- lib/armeabi-v7a/libusb.so
- lib/armeabi-v7a/libwatchdog.so
- lib/armeabi-v7a/libxgb.so
- lib/armeabi-v7a/libzbar.so
isFrameworkApk: false
packageInfo:
  forcedPackageId: '127'
  renameManifestPackage: null
sdkInfo:
  minSdkVersion: '28'
  targetSdkVersion: '30'
sharedLibrary: false
sparseResources: false
unknownFiles: {}
usesFramework:
  ids:
  - 1
  tag: null
version: 2.7.0-dirty
versionInfo:
  versionCode: '1'
  versionName: 24.10.803
