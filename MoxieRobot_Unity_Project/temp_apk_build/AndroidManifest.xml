<?xml version="1.0" encoding="utf-8" standalone="no"?><manifest xmlns:android="http://schemas.android.com/apk/res/android" android:compileSdkVersion="30" android:compileSdkVersionCodename="11" android:installLocation="preferExternal" package="com.embodied.moxierobot.elevenlabs" android:versionCode="999" android:versionName="1.0.0-elevenlabs" platformBuildVersionCode="30" platformBuildVersionName="11">
    <supports-screens android:anyDensity="true" android:largeScreens="true" android:normalScreens="true" android:smallScreens="true" android:xlargeScreens="true"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.SET_TIME"/>
    <uses-permission android:name="android.permission.SET_TIME_ZONE"/>
    <uses-permission android:name="android.permission.GET_ACCOUNTS"/>
    <uses-permission android:name="android.permission.WAKE_LOCK"/>
    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE"/>
    <uses-permission android:name="android.permission.REBOOT"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.BLUETOOTH"/>
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN"/>
    <uses-permission android:name="android.permission.RECORD_AUDIO"/>
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE"/>
    <uses-permission android:name="android.permission.CAMERA"/>
    <uses-permission android:name="android.permission.READ_LOGS"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS"/>
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS"/>
    <uses-feature android:name="android.hardware.camera2.full"/>
    <permission android:name="com.amazonaws.unity.permission.C2D_MESSAGE" android:protectionLevel="signature"/>
    <uses-permission android:name="com.amazonaws.unity.permission.C2D_MESSAGE"/>
    <uses-feature android:glEsVersion="0x00030000"/>
    <uses-feature android:name="android.hardware.touchscreen" android:required="false"/>
    <uses-feature android:name="android.hardware.touchscreen.multitouch" android:required="false"/>
    <uses-feature android:name="android.hardware.touchscreen.multitouch.distinct" android:required="false"/>
    <application android:debuggable="true" android:extractNativeLibs="true" android:icon="@mipmap/app_icon" android:label="@string/app_name" android:theme="@android:style/Theme.NoTitleBar">
        <meta-data android:name="com.google.android.gms.version" android:value="12451000"/>
        <activity android:configChanges="density|fontScale|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode" android:hardwareAccelerated="false" android:label="@string/app_name" android:launchMode="singleTask" android:name="com.unity3d.player.UnityPlayerActivity" android:resizeableActivity="false" android:screenOrientation="fullUser">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
                <category android:name="android.intent.category.LEANBACK_LAUNCHER"/>
            </intent-filter>
            <meta-data android:name="unityplayer.UnityActivity" android:value="true"/>
            <meta-data android:name="unityplayer.ForwardNativeEventsToDalvik" android:value="false"/>
            <meta-data android:name="android.notch_support" android:value="true"/>
        </activity>
        <activity android:enabled="true" android:exported="true" android:label="Bo Launcher" android:name="me.embodied.RobotLauncher" android:process=":borobotlauncher">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.HOME"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <receiver android:name="com.amazonaws.unity.GCMBroadcastReceiver" android:permission="com.google.android.c2dm.permission.SEND">
            <intent-filter>
                <action android:name="com.google.android.c2dm.intent.RECEIVE"/>
                <action android:name="com.google.android.c2dm.intent.REGISTRATION"/>
                <category android:name="com.amazonaws.unity"/>
            </intent-filter>
        </receiver>
        <service android:name="com.amazonaws.unity.GCMIntentService"/>
        <service android:enabled="true" android:exported="true" android:name="me.embodied.services.ServiceLauncher" android:process=":boservicelauncher">
            <intent-filter>
                <action android:name=".Launcher"/>
            </intent-filter>
        </service>
        <service android:enabled="true" android:exported="true" android:name="me.embodied.services.BoDispatch" android:process=":bodispatch"/>
        <service android:enabled="true" android:exported="true" android:name="me.embodied.services.BoAudio" android:process=":boaudio"/>
        <service android:enabled="true" android:exported="true" android:name="me.embodied.services.BoAnalytics" android:process=":boanalytics"/>
        <service android:enabled="true" android:exported="true" android:name="me.embodied.services.BoBrain" android:process=":bobrain"/>
        <service android:enabled="true" android:exported="true" android:name="me.embodied.services.BoFusion" android:process=":bofusion"/>
        <service android:enabled="true" android:exported="true" android:name="me.embodied.services.BoVision" android:process=":bovision"/>
        <service android:enabled="true" android:exported="true" android:name="me.embodied.services.BoLogger" android:process=":bologger"/>
        <service android:enabled="true" android:exported="true" android:name="me.embodied.services.BoSystemMonitor" android:process=":bosysmon"/>
        <service android:enabled="true" android:exported="true" android:name="me.embodied.services.BoUpdater" android:process=":syscommon"/>
        <service android:enabled="true" android:exported="true" android:name="me.embodied.services.XMOSDFU" android:process=":syscommon"/>
        <service android:enabled="true" android:exported="true" android:name="me.embodied.services.BoXmosWatchdog" android:process=":boxmoswd"/>
        <meta-data android:name="unity.splash-mode" android:value="0"/>
        <meta-data android:name="unity.splash-enable" android:value="true"/>
        <meta-data android:name="unity.launch-fullscreen" android:value="true"/>
        <meta-data android:name="unity.allow-resizable-window" android:value="false"/>
        <meta-data android:name="notch.config" android:value="portrait|landscape"/>
        <meta-data android:name="unity.build-id" android:value="5a627658-7532-46e4-930d-3da8354d99ec"/>
    </application>
</manifest>