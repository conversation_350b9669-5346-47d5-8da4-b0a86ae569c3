{"root": [{"assemblyName": "Assembly-CSharp", "nameSpace": "Embodied", "className": "EBDebug", "methodName": "StaticInit", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Assembly-CSharp", "nameSpace": "Embodied.Robot", "className": "RobotDebug", "methodName": "StaticInit", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Sirenix.Utilities", "nameSpace": "Sirenix.Utilities", "className": "UnityVersion", "methodName": "EnsureLoaded", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Sirenix.Serialization", "nameSpace": "Sirenix.Serialization", "className": "UnitySerializationInitializer", "methodName": "InitializeRuntime", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Sirenix.Serialization", "nameSpace": "Sirenix.Serialization.Utilities", "className": "UnityVersion", "methodName": "EnsureLoaded", "loadTypes": 1, "isUnityClass": false}]}