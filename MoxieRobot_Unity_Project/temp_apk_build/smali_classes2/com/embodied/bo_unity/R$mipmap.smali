.class public final Lcom/embodied/bo_unity/R$mipmap;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/embodied/bo_unity/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "mipmap"
.end annotation


# static fields
.field public static final app_icon:I = 0x7f030000

.field public static final app_icon_round:I = 0x7f030001

.field public static final ic_launcher_background:I = 0x7f030002

.field public static final ic_launcher_foreground:I = 0x7f030003


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
