.class final Lcom/unity3d/player/HFPStatus$a;
.super Ljava/lang/Enum;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/unity3d/player/HFPStatus;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4018
    name = "a"
.end annotation


# static fields
.field public static final enum a:I

.field public static final enum b:I

.field private static final synthetic c:[I


# direct methods
.method static constructor <clinit>()V
    .locals 4

    const/4 v0, 0x1

    sput v0, Lcom/unity3d/player/HFPStatus$a;->a:I

    const/4 v1, 0x2

    sput v1, Lcom/unity3d/player/HFPStatus$a;->b:I

    new-array v2, v1, [I

    const/4 v3, 0x0

    aput v0, v2, v3

    aput v1, v2, v0

    sput-object v2, Lcom/unity3d/player/HFPStatus$a;->c:[I

    return-void
.end method
