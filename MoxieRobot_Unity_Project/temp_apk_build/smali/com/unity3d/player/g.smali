.class public final Lcom/unity3d/player/g;
.super Landroid/app/Fragment;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/unity3d/player/g$a;
    }
.end annotation


# instance fields
.field private final a:Lcom/unity3d/player/IPermissionRequestCallbacks;

.field private final b:Landroid/app/Activity;

.field private final c:Landroid/os/Looper;


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Landroid/app/Fragment;-><init>()V

    const/4 v0, 0x0

    iput-object v0, p0, Lcom/unity3d/player/g;->a:Lcom/unity3d/player/IPermissionRequestCallbacks;

    iput-object v0, p0, Lcom/unity3d/player/g;->b:Landroid/app/Activity;

    iput-object v0, p0, Lcom/unity3d/player/g;->c:Landroid/os/Looper;

    return-void
.end method

.method public constructor <init>(Landroid/app/Activity;Lcom/unity3d/player/IPermissionRequestCallbacks;)V
    .locals 0

    invoke-direct {p0}, Landroid/app/Fragment;-><init>()V

    iput-object p2, p0, Lcom/unity3d/player/g;->a:Lcom/unity3d/player/IPermissionRequestCallbacks;

    iput-object p1, p0, Lcom/unity3d/player/g;->b:Landroid/app/Activity;

    invoke-static {}, Landroid/os/Looper;->myLooper()Landroid/os/Looper;

    move-result-object p1

    iput-object p1, p0, Lcom/unity3d/player/g;->c:Landroid/os/Looper;

    return-void
.end method

.method static synthetic a(Lcom/unity3d/player/g;[Ljava/lang/String;)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/unity3d/player/g;->a([Ljava/lang/String;)V

    return-void
.end method

.method private a([Ljava/lang/String;)V
    .locals 4

    array-length v0, p1

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v0, :cond_0

    aget-object v2, p1, v1

    iget-object v3, p0, Lcom/unity3d/player/g;->a:Lcom/unity3d/player/IPermissionRequestCallbacks;

    invoke-interface {v3, v2}, Lcom/unity3d/player/IPermissionRequestCallbacks;->onPermissionDenied(Ljava/lang/String;)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method


# virtual methods
.method public final onCreate(Landroid/os/Bundle;)V
    .locals 1

    invoke-super {p0, p1}, Landroid/app/Fragment;->onCreate(Landroid/os/Bundle;)V

    invoke-virtual {p0}, Lcom/unity3d/player/g;->getArguments()Landroid/os/Bundle;

    move-result-object p1

    const-string v0, "PermissionNames"

    invoke-virtual {p1, v0}, Landroid/os/Bundle;->getStringArray(Ljava/lang/String;)[Ljava/lang/String;

    move-result-object p1

    const v0, 0x178e9

    invoke-virtual {p0, p1, v0}, Lcom/unity3d/player/g;->requestPermissions([Ljava/lang/String;I)V

    return-void
.end method

.method public final onRequestPermissionsResult(I[Ljava/lang/String;[I)V
    .locals 9

    const v0, 0x178e9

    if-eq p1, v0, :cond_0

    return-void

    :cond_0
    array-length p1, p2

    const/4 v0, 0x4

    if-nez p1, :cond_2

    const-string p1, "Permission request dismissed, considering all as denied"

    invoke-static {v0, p1}, Lcom/unity3d/player/f;->Log(ILjava/lang/String;)V

    iget-object p1, p0, Lcom/unity3d/player/g;->a:Lcom/unity3d/player/IPermissionRequestCallbacks;

    if-eqz p1, :cond_7

    iget-object p1, p0, Lcom/unity3d/player/g;->b:Landroid/app/Activity;

    if-eqz p1, :cond_7

    iget-object p1, p0, Lcom/unity3d/player/g;->c:Landroid/os/Looper;

    if-eqz p1, :cond_7

    invoke-virtual {p0}, Lcom/unity3d/player/g;->getArguments()Landroid/os/Bundle;

    move-result-object p1

    const-string p2, "PermissionNames"

    invoke-virtual {p1, p2}, Landroid/os/Bundle;->getStringArray(Ljava/lang/String;)[Ljava/lang/String;

    move-result-object p1

    iget-object p2, p0, Lcom/unity3d/player/g;->a:Lcom/unity3d/player/IPermissionRequestCallbacks;

    instance-of p2, p2, Lcom/unity3d/player/UnityPermissions$ModalWaitForPermissionResponse;

    if-eqz p2, :cond_1

    invoke-direct {p0, p1}, Lcom/unity3d/player/g;->a([Ljava/lang/String;)V

    goto/16 :goto_4

    :cond_1
    new-instance p2, Landroid/os/Handler;

    iget-object p3, p0, Lcom/unity3d/player/g;->c:Landroid/os/Looper;

    invoke-direct {p2, p3}, Landroid/os/Handler;-><init>(Landroid/os/Looper;)V

    new-instance p3, Lcom/unity3d/player/g$1;

    invoke-direct {p3, p0, p1}, Lcom/unity3d/player/g$1;-><init>(Lcom/unity3d/player/g;[Ljava/lang/String;)V

    invoke-virtual {p2, p3}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    goto/16 :goto_4

    :cond_2
    const/4 p1, 0x0

    :goto_0
    array-length v1, p2

    if-ge p1, v1, :cond_7

    array-length v1, p3

    if-ge p1, v1, :cond_7

    aget v1, p3, p1

    const/4 v2, -0x1

    if-ne v1, v2, :cond_3

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    aget-object v2, p2, p1

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v2, " denied.\n"

    goto :goto_1

    :cond_3
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    aget-object v2, p2, p1

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v2, " granted"

    :goto_1
    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Lcom/unity3d/player/f;->Log(ILjava/lang/String;)V

    iget-object v1, p0, Lcom/unity3d/player/g;->a:Lcom/unity3d/player/IPermissionRequestCallbacks;

    if-eqz v1, :cond_6

    iget-object v2, p0, Lcom/unity3d/player/g;->b:Landroid/app/Activity;

    if-eqz v2, :cond_6

    iget-object v2, p0, Lcom/unity3d/player/g;->c:Landroid/os/Looper;

    if-eqz v2, :cond_6

    instance-of v2, v1, Lcom/unity3d/player/UnityPermissions$ModalWaitForPermissionResponse;

    if-eqz v2, :cond_4

    aget-object v2, p2, p1

    invoke-interface {v1, v2}, Lcom/unity3d/player/IPermissionRequestCallbacks;->onPermissionGranted(Ljava/lang/String;)V

    goto :goto_3

    :cond_4
    aget-object v1, p2, p1

    if-nez v1, :cond_5

    const-string v1, "<null>"

    goto :goto_2

    :cond_5
    aget-object v1, p2, p1

    :goto_2
    move-object v5, v1

    new-instance v1, Landroid/os/Handler;

    iget-object v2, p0, Lcom/unity3d/player/g;->c:Landroid/os/Looper;

    invoke-direct {v1, v2}, Landroid/os/Handler;-><init>(Landroid/os/Looper;)V

    new-instance v8, Lcom/unity3d/player/g$a;

    iget-object v4, p0, Lcom/unity3d/player/g;->a:Lcom/unity3d/player/IPermissionRequestCallbacks;

    aget v6, p3, p1

    iget-object v2, p0, Lcom/unity3d/player/g;->b:Landroid/app/Activity;

    invoke-virtual {v2, v5}, Landroid/app/Activity;->shouldShowRequestPermissionRationale(Ljava/lang/String;)Z

    move-result v7

    move-object v2, v8

    move-object v3, p0

    invoke-direct/range {v2 .. v7}, Lcom/unity3d/player/g$a;-><init>(Lcom/unity3d/player/g;Lcom/unity3d/player/IPermissionRequestCallbacks;Ljava/lang/String;IZ)V

    invoke-virtual {v1, v8}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_6
    :goto_3
    add-int/lit8 p1, p1, 0x1

    goto :goto_0

    :cond_7
    :goto_4
    invoke-virtual {p0}, Lcom/unity3d/player/g;->getActivity()Landroid/app/Activity;

    move-result-object p1

    invoke-virtual {p1}, Landroid/app/Activity;->getFragmentManager()Landroid/app/FragmentManager;

    move-result-object p1

    invoke-virtual {p1}, Landroid/app/FragmentManager;->beginTransaction()Landroid/app/FragmentTransaction;

    move-result-object p1

    invoke-virtual {p1, p0}, Landroid/app/FragmentTransaction;->remove(Landroid/app/Fragment;)Landroid/app/FragmentTransaction;

    invoke-virtual {p1}, Landroid/app/FragmentTransaction;->commit()I

    return-void
.end method
