.class public Lcom/unity3d/player/GoogleARCoreApi;
.super Ljava/lang/Object;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final native initializeARCore(Landroid/app/Activity;)V
.end method

.method public final native pauseARCore()V
.end method

.method public final native resumeARCore()V
.end method
