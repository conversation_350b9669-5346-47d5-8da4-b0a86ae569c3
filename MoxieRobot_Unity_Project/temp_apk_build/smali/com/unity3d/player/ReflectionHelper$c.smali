.class public interface abstract Lcom/unity3d/player/ReflectionHelper$c;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/reflect/InvocationHandler;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/unity3d/player/ReflectionHelper;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x60c
    name = "c"
.end annotation


# virtual methods
.method public abstract a(JZ)V
.end method
