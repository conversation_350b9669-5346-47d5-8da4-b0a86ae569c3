.class final Lcom/unity3d/player/g$1;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/unity3d/player/g;->onRequestPermissionsResult(I[Ljava/lang/String;[I)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic a:[Ljava/lang/String;

.field final synthetic b:Lcom/unity3d/player/g;


# direct methods
.method constructor <init>(Lcom/unity3d/player/g;[Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/unity3d/player/g$1;->b:Lcom/unity3d/player/g;

    iput-object p2, p0, Lcom/unity3d/player/g$1;->a:[Ljava/lang/String;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    iget-object v0, p0, Lcom/unity3d/player/g$1;->b:Lcom/unity3d/player/g;

    iget-object v1, p0, Lcom/unity3d/player/g$1;->a:[Ljava/lang/String;

    invoke-static {v0, v1}, Lcom/unity3d/player/g;->a(Lcom/unity3d/player/g;[Ljava/lang/String;)V

    return-void
.end method
