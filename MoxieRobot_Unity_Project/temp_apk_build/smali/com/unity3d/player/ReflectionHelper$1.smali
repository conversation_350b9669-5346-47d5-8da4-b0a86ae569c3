.class final Lcom/unity3d/player/ReflectionHelper$1;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/unity3d/player/ReflectionHelper$c;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/unity3d/player/ReflectionHelper;->newProxyInstance(Lcom/unity3d/player/UnityPlayer;J[Ljava/lang/Class;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x8
    name = null
.end annotation


# instance fields
.field final synthetic a:J

.field final synthetic b:Lcom/unity3d/player/UnityPlayer;

.field final synthetic c:[Ljava/lang/Class;

.field private d:Ljava/lang/Runnable;

.field private e:Lcom/unity3d/player/UnityPlayer;

.field private f:J

.field private g:J

.field private h:Z


# direct methods
.method constructor <init>(JLcom/unity3d/player/UnityPlayer;[Ljava/lang/Class;)V
    .locals 2

    iput-wide p1, p0, Lcom/unity3d/player/ReflectionHelper$1;->a:J

    iput-object p3, p0, Lcom/unity3d/player/ReflectionHelper$1;->b:Lcom/unity3d/player/UnityPlayer;

    iput-object p4, p0, Lcom/unity3d/player/ReflectionHelper$1;->c:[Ljava/lang/Class;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance p1, Lcom/unity3d/player/ReflectionHelper$b;

    invoke-static {}, Lcom/unity3d/player/ReflectionHelper;->a()J

    move-result-wide p2

    iget-wide v0, p0, Lcom/unity3d/player/ReflectionHelper$1;->a:J

    invoke-direct {p1, p2, p3, v0, v1}, Lcom/unity3d/player/ReflectionHelper$b;-><init>(JJ)V

    iput-object p1, p0, Lcom/unity3d/player/ReflectionHelper$1;->d:Ljava/lang/Runnable;

    iget-object p1, p0, Lcom/unity3d/player/ReflectionHelper$1;->b:Lcom/unity3d/player/UnityPlayer;

    iput-object p1, p0, Lcom/unity3d/player/ReflectionHelper$1;->e:Lcom/unity3d/player/UnityPlayer;

    invoke-static {}, Lcom/unity3d/player/ReflectionHelper;->a()J

    move-result-wide p1

    iput-wide p1, p0, Lcom/unity3d/player/ReflectionHelper$1;->f:J

    return-void
.end method

.method private a(Ljava/lang/Object;Ljava/lang/reflect/Method;[Ljava/lang/Object;)Ljava/lang/Object;
    .locals 7

    const/4 v0, 0x0

    if-nez p3, :cond_0

    :try_start_0
    new-array p3, v0, [Ljava/lang/Object;

    :cond_0
    invoke-virtual {p2}, Ljava/lang/reflect/Method;->getDeclaringClass()Ljava/lang/Class;

    move-result-object v1

    const-class v2, Ljava/lang/invoke/MethodHandles$Lookup;

    const/4 v3, 0x2

    new-array v4, v3, [Ljava/lang/Class;

    const-class v5, Ljava/lang/Class;

    aput-object v5, v4, v0

    sget-object v5, Ljava/lang/Integer;->TYPE:Ljava/lang/Class;

    const/4 v6, 0x1

    aput-object v5, v4, v6

    invoke-virtual {v2, v4}, Ljava/lang/Class;->getDeclaredConstructor([Ljava/lang/Class;)Ljava/lang/reflect/Constructor;

    move-result-object v2

    invoke-virtual {v2, v6}, Ljava/lang/reflect/Constructor;->setAccessible(Z)V

    new-array v4, v3, [Ljava/lang/Object;

    aput-object v1, v4, v0

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    aput-object v3, v4, v6

    invoke-virtual {v2, v4}, Ljava/lang/reflect/Constructor;->newInstance([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/invoke/MethodHandles$Lookup;

    invoke-virtual {v2, v1}, Ljava/lang/invoke/MethodHandles$Lookup;->in(Ljava/lang/Class;)Ljava/lang/invoke/MethodHandles$Lookup;

    move-result-object v2

    invoke-virtual {v2, p2, v1}, Ljava/lang/invoke/MethodHandles$Lookup;->unreflectSpecial(Ljava/lang/reflect/Method;Ljava/lang/Class;)Ljava/lang/invoke/MethodHandle;

    move-result-object p2

    invoke-virtual {p2, p1}, Ljava/lang/invoke/MethodHandle;->bindTo(Ljava/lang/Object;)Ljava/lang/invoke/MethodHandle;

    move-result-object p1

    invoke-virtual {p1, p3}, Ljava/lang/invoke/MethodHandle;->invokeWithArguments([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1
    :try_end_0
    .catch Ljava/lang/NoClassDefFoundError; {:try_start_0 .. :try_end_0} :catch_0

    return-object p1

    :catch_0
    move-exception p1

    const/4 p1, 0x6

    new-array p2, v0, [Ljava/lang/Object;

    const-string p3, "Java interface default methods are only supported since Android Oreo"

    invoke-static {p3, p2}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p2

    invoke-static {p1, p2}, Lcom/unity3d/player/f;->Log(ILjava/lang/String;)V

    iget-wide p1, p0, Lcom/unity3d/player/ReflectionHelper$1;->g:J

    invoke-static {p1, p2}, Lcom/unity3d/player/ReflectionHelper;->a(J)V

    const/4 p1, 0x0

    return-object p1
.end method


# virtual methods
.method public final a(JZ)V
    .locals 0

    iput-wide p1, p0, Lcom/unity3d/player/ReflectionHelper$1;->g:J

    iput-boolean p3, p0, Lcom/unity3d/player/ReflectionHelper$1;->h:Z

    return-void
.end method

.method protected final finalize()V
    .locals 5

    sget-boolean v0, Lcom/unity3d/player/ReflectionHelper;->LOG:Z

    if-eqz v0, :cond_0

    const/4 v0, 0x3

    const/4 v1, 0x2

    new-array v1, v1, [Ljava/lang/Object;

    const/4 v2, 0x0

    iget-wide v3, p0, Lcom/unity3d/player/ReflectionHelper$1;->a:J

    invoke-static {v3, v4}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v3

    aput-object v3, v1, v2

    const/4 v2, 0x1

    iget-object v3, p0, Lcom/unity3d/player/ReflectionHelper$1;->c:[Ljava/lang/Class;

    invoke-static {v3}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v3

    aput-object v3, v1, v2

    const-string v2, "ReflectionHelper.Proxy.finalize(%d, %s)"

    invoke-static {v2, v1}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Lcom/unity3d/player/f;->Log(ILjava/lang/String;)V

    :cond_0
    iget-object v0, p0, Lcom/unity3d/player/ReflectionHelper$1;->e:Lcom/unity3d/player/UnityPlayer;

    iget-object v1, p0, Lcom/unity3d/player/ReflectionHelper$1;->d:Ljava/lang/Runnable;

    invoke-virtual {v0, v1}, Lcom/unity3d/player/UnityPlayer;->queueGLThreadEvent(Ljava/lang/Runnable;)V

    invoke-super {p0}, Ljava/lang/Object;->finalize()V

    return-void
.end method

.method public final invoke(Ljava/lang/Object;Ljava/lang/reflect/Method;[Ljava/lang/Object;)Ljava/lang/Object;
    .locals 5

    sget-boolean v0, Lcom/unity3d/player/ReflectionHelper;->LOG:Z

    const/4 v1, 0x0

    if-eqz v0, :cond_1

    const/4 v0, 0x4

    new-array v0, v0, [Ljava/lang/Object;

    iget-wide v2, p0, Lcom/unity3d/player/ReflectionHelper$1;->a:J

    invoke-static {v2, v3}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v2

    aput-object v2, v0, v1

    const/4 v2, 0x1

    iget-object v3, p0, Lcom/unity3d/player/ReflectionHelper$1;->c:[Ljava/lang/Class;

    invoke-static {v3}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v3

    aput-object v3, v0, v2

    const/4 v2, 0x2

    invoke-virtual {p2}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object v3

    aput-object v3, v0, v2

    if-nez p3, :cond_0

    const-string v2, "<null>"

    goto :goto_0

    :cond_0
    invoke-static {p3}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v2

    :goto_0
    const/4 v3, 0x3

    aput-object v2, v0, v3

    const-string v2, "ReflectionHelper.Proxy.invoke(%d, %s, %s, %s)"

    invoke-static {v2, v0}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    invoke-static {v3, v0}, Lcom/unity3d/player/f;->Log(ILjava/lang/String;)V

    :cond_1
    iget-wide v2, p0, Lcom/unity3d/player/ReflectionHelper$1;->f:J

    invoke-static {v2, v3}, Lcom/unity3d/player/ReflectionHelper;->beginProxyCall(J)Z

    move-result v0

    if-nez v0, :cond_2

    const/4 p1, 0x6

    const-string p2, "Scripting proxy object was destroyed, because Unity player was unloaded."

    invoke-static {p1, p2}, Lcom/unity3d/player/f;->Log(ILjava/lang/String;)V

    const/4 p1, 0x0

    return-object p1

    :cond_2
    const-wide/16 v2, 0x0

    :try_start_0
    iput-wide v2, p0, Lcom/unity3d/player/ReflectionHelper$1;->g:J

    iput-boolean v1, p0, Lcom/unity3d/player/ReflectionHelper$1;->h:Z

    iget-wide v0, p0, Lcom/unity3d/player/ReflectionHelper$1;->a:J

    invoke-virtual {p2}, Ljava/lang/reflect/Method;->getName()Ljava/lang/String;

    move-result-object v4

    invoke-static {v0, v1, v4, p3}, Lcom/unity3d/player/ReflectionHelper;->a(JLjava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    iget-boolean v1, p0, Lcom/unity3d/player/ReflectionHelper$1;->h:Z

    if-eqz v1, :cond_4

    invoke-virtual {p2}, Ljava/lang/reflect/Method;->getModifiers()I

    move-result v1

    and-int/lit16 v1, v1, 0x400

    if-nez v1, :cond_3

    invoke-direct {p0, p1, p2, p3}, Lcom/unity3d/player/ReflectionHelper$1;->a(Ljava/lang/Object;Ljava/lang/reflect/Method;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    invoke-static {}, Lcom/unity3d/player/ReflectionHelper;->endProxyCall()V

    return-object p1

    :cond_3
    :try_start_1
    iget-wide p1, p0, Lcom/unity3d/player/ReflectionHelper$1;->g:J

    :goto_1
    invoke-static {p1, p2}, Lcom/unity3d/player/ReflectionHelper;->a(J)V

    goto :goto_2

    :cond_4
    iget-wide p1, p0, Lcom/unity3d/player/ReflectionHelper$1;->g:J

    cmp-long p1, p1, v2

    if-eqz p1, :cond_5

    iget-wide p1, p0, Lcom/unity3d/player/ReflectionHelper$1;->g:J
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    goto :goto_1

    :cond_5
    :goto_2
    invoke-static {}, Lcom/unity3d/player/ReflectionHelper;->endProxyCall()V

    return-object v0

    :catchall_0
    move-exception p1

    invoke-static {}, Lcom/unity3d/player/ReflectionHelper;->endProxyCall()V

    throw p1
.end method
