.class final Lcom/unity3d/player/UnityPlayer$b;
.super Ljava/lang/Enum;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/unity3d/player/UnityPlayer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4018
    name = "b"
.end annotation


# static fields
.field public static final enum a:I

.field public static final enum b:I

.field public static final enum c:I

.field private static final synthetic d:[I


# direct methods
.method static constructor <clinit>()V
    .locals 5

    const/4 v0, 0x1

    sput v0, Lcom/unity3d/player/UnityPlayer$b;->a:I

    const/4 v1, 0x2

    sput v1, Lcom/unity3d/player/UnityPlayer$b;->b:I

    const/4 v2, 0x3

    sput v2, Lcom/unity3d/player/UnityPlayer$b;->c:I

    new-array v3, v2, [I

    const/4 v4, 0x0

    aput v0, v3, v4

    aput v1, v3, v0

    aput v2, v3, v1

    sput-object v3, Lcom/unity3d/player/UnityPlayer$b;->d:[I

    return-void
.end method
