.class interface abstract Lcom/unity3d/player/d;
.super Ljava/lang/Object;


# virtual methods
.method public abstract a(Lcom/unity3d/player/IAssetPackManagerDownloadStatusCallback;)Ljava/lang/Object;
.end method

.method public abstract a(Ljava/lang/String;)Ljava/lang/String;
.end method

.method public abstract a(Landroid/app/Activity;Lcom/unity3d/player/IAssetPackManagerMobileDataConfirmationCallback;)V
.end method

.method public abstract a(Ljava/lang/Object;)V
.end method

.method public abstract a([Ljava/lang/String;)V
.end method

.method public abstract a([Ljava/lang/String;Lcom/unity3d/player/IAssetPackManagerDownloadStatusCallback;)V
.end method

.method public abstract a([Ljava/lang/String;Lcom/unity3d/player/IAssetPackManagerStatusQueryCallback;)V
.end method

.method public abstract b(Ljava/lang/String;)V
.end method
