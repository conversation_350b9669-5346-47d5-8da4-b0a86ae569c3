.class public Lme/embodied/DeviceSettings;
.super Ljava/lang/Object;
.source "DeviceSettings.java"


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 11
    const-string v0, "devset"

    invoke-static {v0}, Ljava/lang/System;->loadLibrary(Ljava/lang/String;)V

    .line 12
    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 8
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static native getBoolS(Ljava/lang/String;)Z
.end method

.method public static native getIntS(Ljava/lang/String;)I
.end method

.method public static native getStringS(Ljava/lang/String;)Ljava/lang/String;
.end method
