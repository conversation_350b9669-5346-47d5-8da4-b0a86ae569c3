.class public Lme/embodied/services/BoSystemMonitor;
.super Lme/embodied/services/Service;
.source "BoSystemMonitor.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lme/embodied/services/BoSystemMonitor$TZState;
    }
.end annotation


# static fields
.field private static final ACTION_OS_SET_TIMEZONE:Ljava/lang/String; = "com.embodied.osctrl.set_timezone"

.field private static final MEDIA_VOLUME_CHANGE_ACTION:Ljava/lang/String; = "android.media.VOLUME_CHANGED_ACTION"

.field private static final PREFERRED_ZONES:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field private static final TAG:Ljava/lang/String; = "bo-system-monitor"


# instance fields
.field auto_timezone_enabled_:Z

.field last_volume_:I

.field last_wifi_state_:Z

.field ntp_service_:Lme/embodied/NTPService;

.field timezone_input_:Ljava/lang/String;

.field timezone_state_:Lme/embodied/services/BoSystemMonitor$TZState;

.field volume_scaler_:Lme/embodied/VolumeScale$Scaler;

.field wifi_recovery_thread_:Ljava/lang/Thread;


# direct methods
.method static constructor <clinit>()V
    .locals 6

    .line 301
    new-instance v0, Ljava/util/HashSet;

    const-string v1, "US/Pacific"

    const-string v2, "US/Mountain"

    const-string v3, "US/Central"

    const-string v4, "US/Eastern"

    const-string v5, "US/Hawaii"

    filled-new-array {v1, v2, v3, v4, v5}, [Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/util/HashSet;-><init>(Ljava/util/Collection;)V

    sput-object v0, Lme/embodied/services/BoSystemMonitor;->PREFERRED_ZONES:Ljava/util/Set;

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    .line 53
    const-string v0, "bo-system-monitor"

    invoke-direct {p0, v0}, Lme/embodied/services/Service;-><init>(Ljava/lang/String;)V

    .line 44
    sget-object v0, Lme/embodied/services/BoSystemMonitor$TZState;->UNKNOWN:Lme/embodied/services/BoSystemMonitor$TZState;

    iput-object v0, p0, Lme/embodied/services/BoSystemMonitor;->timezone_state_:Lme/embodied/services/BoSystemMonitor$TZState;

    .line 54
    const/4 v0, -0x1

    iput v0, p0, Lme/embodied/services/BoSystemMonitor;->last_volume_:I

    .line 55
    sget-object v0, Lme/embodied/VolumeScale$Scalers;->SCALER_TENSTEP:Lme/embodied/VolumeScale$Scalers;

    invoke-static {v0}, Lme/embodied/VolumeScale;->createScaler(Lme/embodied/VolumeScale$Scalers;)Lme/embodied/VolumeScale$Scaler;

    move-result-object v0

    iput-object v0, p0, Lme/embodied/services/BoSystemMonitor;->volume_scaler_:Lme/embodied/VolumeScale$Scaler;

    .line 56
    new-instance v0, Lme/embodied/NTPService;

    invoke-direct {v0, p0}, Lme/embodied/NTPService;-><init>(Landroid/content/Context;)V

    iput-object v0, p0, Lme/embodied/services/BoSystemMonitor;->ntp_service_:Lme/embodied/NTPService;

    .line 57
    return-void
.end method

.method private static native BroadcastSystemVolume(I)V
.end method

.method private native InitNative(J)V
.end method

.method static synthetic access$000(Lme/embodied/services/BoSystemMonitor;)V
    .locals 0

    .line 29
    invoke-direct {p0}, Lme/embodied/services/BoSystemMonitor;->reportCurrentVolume()V

    return-void
.end method

.method static synthetic access$100(Lme/embodied/services/BoSystemMonitor;ZLjava/lang/String;)V
    .locals 0

    .line 29
    invoke-direct {p0, p1, p2}, Lme/embodied/services/BoSystemMonitor;->reportWifiStateInt(ZLjava/lang/String;)V

    return-void
.end method

.method static synthetic access$200(Lme/embodied/services/BoSystemMonitor;JI)V
    .locals 0

    .line 29
    invoke-direct {p0, p1, p2, p3}, Lme/embodied/services/BoSystemMonitor;->reportWifiRSSI(JI)V

    return-void
.end method

.method private static guessTimezone(Ljava/lang/String;)Ljava/lang/String;
    .locals 10
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/text/ParseException;
        }
    .end annotation

    .line 307
    const-string v0, "UTC"

    invoke-static {v0}, Ljava/util/TimeZone;->getTimeZone(Ljava/lang/String;)Ljava/util/TimeZone;

    move-result-object v0

    invoke-static {v0}, Ljava/util/Calendar;->getInstance(Ljava/util/TimeZone;)Ljava/util/Calendar;

    move-result-object v0

    .line 309
    const-string v1, "Z"

    const-string v2, "+00:00"

    invoke-virtual {p0, v1, v2}, Ljava/lang/String;->replace(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object p0

    .line 311
    const/4 v1, 0x0

    :try_start_0
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v3, 0x16

    invoke-virtual {p0, v1, v3}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/16 v3, 0x17

    invoke-virtual {p0, v3}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v2, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0
    :try_end_0
    .catch Ljava/lang/IndexOutOfBoundsException; {:try_start_0 .. :try_end_0} :catch_0

    .line 314
    nop

    .line 316
    new-instance v2, Ljava/text/SimpleDateFormat;

    const-string v3, "yyyy-MM-dd\'T\'HH:mm:ssZ"

    invoke-direct {v2, v3}, Ljava/text/SimpleDateFormat;-><init>(Ljava/lang/String;)V

    invoke-virtual {v2, p0}, Ljava/text/SimpleDateFormat;->parse(Ljava/lang/String;)Ljava/util/Date;

    move-result-object p0

    .line 317
    invoke-virtual {v0, p0}, Ljava/util/Calendar;->setTime(Ljava/util/Date;)V

    .line 321
    const/16 p0, 0xb

    invoke-virtual {v0, p0}, Ljava/util/Calendar;->get(I)I

    move-result v2

    const/16 v3, 0xc

    if-le v2, v3, :cond_0

    .line 324
    invoke-virtual {v0, p0}, Ljava/util/Calendar;->get(I)I

    move-result p0

    rsub-int/lit8 p0, p0, 0x18

    mul-int/lit8 p0, p0, 0x3c

    invoke-virtual {v0, v3}, Ljava/util/Calendar;->get(I)I

    move-result v2

    sub-int/2addr p0, v2

    mul-int/lit8 p0, p0, 0x3c

    mul-int/lit16 p0, p0, 0x3e8

    goto :goto_0

    .line 327
    :cond_0
    invoke-virtual {v0, p0}, Ljava/util/Calendar;->get(I)I

    move-result p0

    neg-int p0, p0

    mul-int/lit8 p0, p0, 0x3c

    invoke-virtual {v0, v3}, Ljava/util/Calendar;->get(I)I

    move-result v2

    sub-int/2addr p0, v2

    mul-int/lit8 p0, p0, 0x3c

    mul-int/lit16 p0, p0, 0x3e8

    .line 331
    :goto_0
    invoke-virtual {v0}, Ljava/util/Calendar;->getTimeInMillis()J

    move-result-wide v2

    .line 332
    const/4 v0, 0x0

    .line 334
    new-instance v4, Ljava/util/ArrayList;

    invoke-direct {v4}, Ljava/util/ArrayList;-><init>()V

    .line 335
    invoke-static {p0}, Ljava/util/TimeZone;->getAvailableIDs(I)[Ljava/lang/String;

    move-result-object v5

    array-length v6, v5

    move v7, v1

    :goto_1
    if-ge v7, v6, :cond_1

    aget-object v8, v5, v7

    .line 336
    invoke-virtual {v4, v8}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 335
    add-int/lit8 v7, v7, 0x1

    goto :goto_1

    .line 338
    :cond_1
    const v5, 0x36ee80

    sub-int v6, p0, v5

    invoke-static {v6}, Ljava/util/TimeZone;->getAvailableIDs(I)[Ljava/lang/String;

    move-result-object v6

    array-length v7, v6

    move v8, v1

    :goto_2
    if-ge v8, v7, :cond_2

    aget-object v9, v6, v8

    .line 339
    invoke-virtual {v4, v9}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 338
    add-int/lit8 v8, v8, 0x1

    goto :goto_2

    .line 341
    :cond_2
    add-int/2addr v5, p0

    invoke-static {v5}, Ljava/util/TimeZone;->getAvailableIDs(I)[Ljava/lang/String;

    move-result-object v5

    array-length v6, v5

    move v7, v1

    :goto_3
    if-ge v7, v6, :cond_3

    aget-object v8, v5, v7

    .line 342
    invoke-virtual {v4, v8}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 341
    add-int/lit8 v7, v7, 0x1

    goto :goto_3

    .line 344
    :cond_3
    invoke-virtual {v4}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object v4

    :goto_4
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    move-result v5

    if-eqz v5, :cond_6

    invoke-interface {v4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Ljava/lang/String;

    .line 345
    invoke-static {v5}, Ljava/util/TimeZone;->getTimeZone(Ljava/lang/String;)Ljava/util/TimeZone;

    move-result-object v6

    .line 346
    invoke-virtual {v6, v2, v3}, Ljava/util/TimeZone;->getOffset(J)I

    move-result v6

    if-ne v6, p0, :cond_5

    .line 347
    sget-object v6, Lme/embodied/services/BoSystemMonitor;->PREFERRED_ZONES:Ljava/util/Set;

    invoke-interface {v6, v5}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    move-result v6

    if-eqz v6, :cond_4

    .line 348
    return-object v5

    .line 349
    :cond_4
    if-nez v0, :cond_5

    .line 350
    move-object v0, v5

    .line 352
    :cond_5
    goto :goto_4

    .line 353
    :cond_6
    if-eqz v0, :cond_7

    .line 356
    return-object v0

    .line 354
    :cond_7
    new-instance p0, Ljava/text/ParseException;

    const-string v0, "No matches found."

    invoke-direct {p0, v0, v1}, Ljava/text/ParseException;-><init>(Ljava/lang/String;I)V

    throw p0

    .line 312
    :catch_0
    move-exception p0

    .line 313
    new-instance p0, Ljava/text/ParseException;

    const-string v0, "Invalid length"

    invoke-direct {p0, v0, v1}, Ljava/text/ParseException;-><init>(Ljava/lang/String;I)V

    throw p0
.end method

.method private initStateListeners()V
    .locals 3

    .line 249
    new-instance v0, Lme/embodied/services/BoSystemMonitor$3;

    invoke-direct {v0, p0}, Lme/embodied/services/BoSystemMonitor$3;-><init>(Lme/embodied/services/BoSystemMonitor;)V

    .line 278
    new-instance v1, Landroid/content/IntentFilter;

    const-string v2, "android.media.VOLUME_CHANGED_ACTION"

    invoke-direct {v1, v2}, Landroid/content/IntentFilter;-><init>(Ljava/lang/String;)V

    .line 279
    const-string v2, "android.net.wifi.STATE_CHANGE"

    invoke-virtual {v1, v2}, Landroid/content/IntentFilter;->addAction(Ljava/lang/String;)V

    .line 280
    const-string v2, "android.net.conn.CONNECTIVITY_CHANGE"

    invoke-virtual {v1, v2}, Landroid/content/IntentFilter;->addAction(Ljava/lang/String;)V

    .line 281
    const-string v2, "android.net.wifi.RSSI_CHANGED"

    invoke-virtual {v1, v2}, Landroid/content/IntentFilter;->addAction(Ljava/lang/String;)V

    .line 282
    invoke-virtual {p0, v0, v1}, Lme/embodied/services/BoSystemMonitor;->registerReceiver(Landroid/content/BroadcastReceiver;Landroid/content/IntentFilter;)Landroid/content/Intent;

    .line 285
    invoke-direct {p0}, Lme/embodied/services/BoSystemMonitor;->reportCurrentVolume()V

    .line 286
    invoke-direct {p0}, Lme/embodied/services/BoSystemMonitor;->reportCurrentWifi()V

    .line 287
    return-void
.end method

.method private reportCurrentVolume()V
    .locals 5

    .line 83
    invoke-static {}, Lme/embodied/VolumeScale;->UpdateLimits()V

    .line 84
    const-string v0, "audio"

    invoke-virtual {p0, v0}, Lme/embodied/services/BoSystemMonitor;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/media/AudioManager;

    .line 85
    const/4 v1, 0x3

    invoke-virtual {v0, v1}, Landroid/media/AudioManager;->getStreamVolume(I)I

    move-result v2

    .line 88
    sget v3, Lme/embodied/VolumeScale;->VOLUME_CEILING:I

    const-string v4, "bo-system-monitor"

    if-le v2, v3, :cond_0

    .line 89
    sget v2, Lme/embodied/VolumeScale;->VOLUME_CEILING:I

    .line 90
    const-string v3, "Current volume exceeds bounds.  Adjusting to max."

    invoke-static {v4, v3}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 91
    const/4 v3, 0x0

    invoke-virtual {v0, v1, v2, v3}, Landroid/media/AudioManager;->setStreamVolume(III)V

    .line 94
    :cond_0
    invoke-static {v2}, Lme/embodied/VolumeScale;->validSystemVolume(I)I

    move-result v0

    .line 96
    iget v1, p0, Lme/embodied/services/BoSystemMonitor;->last_volume_:I

    if-eq v1, v0, :cond_1

    .line 97
    iput v0, p0, Lme/embodied/services/BoSystemMonitor;->last_volume_:I

    .line 98
    iget-object v1, p0, Lme/embodied/services/BoSystemMonitor;->volume_scaler_:Lme/embodied/VolumeScale$Scaler;

    invoke-interface {v1, v0}, Lme/embodied/VolumeScale$Scaler;->systemToPercent(I)I

    move-result v1

    .line 99
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "CURR VOLUME: "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v0, " / "

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v0, "%"

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v4, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 100
    invoke-static {v1}, Lme/embodied/services/BoSystemMonitor;->BroadcastSystemVolume(I)V

    .line 102
    :cond_1
    return-void
.end method

.method private reportCurrentWifi()V
    .locals 3

    .line 146
    const-string v0, "wifi"

    invoke-virtual {p0, v0}, Lme/embodied/services/BoSystemMonitor;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/net/wifi/WifiManager;

    .line 147
    invoke-virtual {v0}, Landroid/net/wifi/WifiManager;->getConnectionInfo()Landroid/net/wifi/WifiInfo;

    move-result-object v0

    .line 148
    invoke-virtual {v0}, Landroid/net/wifi/WifiInfo;->getSupplicantState()Landroid/net/wifi/SupplicantState;

    move-result-object v1

    sget-object v2, Landroid/net/wifi/SupplicantState;->COMPLETED:Landroid/net/wifi/SupplicantState;

    if-ne v1, v2, :cond_0

    .line 149
    const/4 v1, 0x1

    invoke-virtual {v0}, Landroid/net/wifi/WifiInfo;->getSSID()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v1, v0}, Lme/embodied/services/BoSystemMonitor;->reportWifiStateInt(ZLjava/lang/String;)V

    goto :goto_0

    .line 151
    :cond_0
    const/4 v0, 0x0

    const/4 v1, 0x0

    invoke-direct {p0, v0, v1}, Lme/embodied/services/BoSystemMonitor;->reportWifiStateInt(ZLjava/lang/String;)V

    .line 153
    :goto_0
    return-void
.end method

.method private native reportWifiRSSI(JI)V
.end method

.method private native reportWifiState(JZLjava/lang/String;)V
.end method

.method private reportWifiStateInt(ZLjava/lang/String;)V
    .locals 2

    .line 140
    iget-wide v0, p0, Lme/embodied/services/BoSystemMonitor;->ptr:J

    invoke-direct {p0, v0, v1, p1, p2}, Lme/embodied/services/BoSystemMonitor;->reportWifiState(JZLjava/lang/String;)V

    .line 141
    invoke-virtual {p0, p1}, Lme/embodied/services/BoSystemMonitor;->handleWifiState(Z)V

    .line 142
    return-void
.end method

.method private requestSetTimezone(Ljava/lang/String;)V
    .locals 3

    .line 191
    new-instance v0, Landroid/content/Intent;

    invoke-direct {v0}, Landroid/content/Intent;-><init>()V

    .line 192
    const-string v1, "com.embodied.osctrl.set_timezone"

    invoke-virtual {v0, v1}, Landroid/content/Intent;->setAction(Ljava/lang/String;)Landroid/content/Intent;

    .line 193
    const-string v1, "id"

    invoke-virtual {v0, v1, p1}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Ljava/lang/String;)Landroid/content/Intent;

    .line 194
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Requesting to set timezone to \'"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p1, "\'"

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const-string v1, "bo-system-monitor"

    invoke-static {v1, p1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 195
    invoke-virtual {p0, v0}, Lme/embodied/services/BoSystemMonitor;->sendBroadcast(Landroid/content/Intent;)V

    .line 196
    return-void
.end method


# virtual methods
.method protected NativeReady()V
    .locals 2

    .line 61
    const-string v0, "bo-system-monitor"

    const-string v1, "Native service was created."

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 62
    iget-wide v0, p0, Lme/embodied/services/BoSystemMonitor;->ptr:J

    invoke-direct {p0, v0, v1}, Lme/embodied/services/BoSystemMonitor;->InitNative(J)V

    .line 63
    invoke-direct {p0}, Lme/embodied/services/BoSystemMonitor;->initStateListeners()V

    .line 64
    return-void
.end method

.method public OnInternetRecoveryRequest()V
    .locals 2

    .line 231
    const-string v0, "bo-system-monitor"

    const-string v1, "Attempting to recover internet by restarting wifi."

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 232
    new-instance v0, Lme/embodied/services/BoSystemMonitor$2;

    invoke-direct {v0, p0}, Lme/embodied/services/BoSystemMonitor$2;-><init>(Lme/embodied/services/BoSystemMonitor;)V

    .line 245
    invoke-virtual {v0}, Lme/embodied/services/BoSystemMonitor$2;->start()V

    .line 246
    return-void
.end method

.method public OnTimeZoneInfo(Ljava/lang/String;Ljava/lang/String;)V
    .locals 1

    .line 201
    iget-boolean v0, p0, Lme/embodied/services/BoSystemMonitor;->auto_timezone_enabled_:Z

    if-nez v0, :cond_0

    .line 202
    return-void

    .line 205
    :cond_0
    if-eqz p2, :cond_3

    invoke-virtual {p2}, Ljava/lang/String;->length()I

    move-result v0

    if-lez v0, :cond_3

    .line 207
    iget-object p1, p0, Lme/embodied/services/BoSystemMonitor;->timezone_state_:Lme/embodied/services/BoSystemMonitor$TZState;

    sget-object v0, Lme/embodied/services/BoSystemMonitor$TZState;->SET_TRUE:Lme/embodied/services/BoSystemMonitor$TZState;

    if-ne p1, v0, :cond_1

    iget-object p1, p0, Lme/embodied/services/BoSystemMonitor;->timezone_state_:Lme/embodied/services/BoSystemMonitor$TZState;

    sget-object v0, Lme/embodied/services/BoSystemMonitor$TZState;->SET_TRUE:Lme/embodied/services/BoSystemMonitor$TZState;

    if-ne p1, v0, :cond_2

    iget-object p1, p0, Lme/embodied/services/BoSystemMonitor;->timezone_input_:Ljava/lang/String;

    invoke-virtual {p2, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_2

    .line 208
    :cond_1
    sget-object p1, Lme/embodied/services/BoSystemMonitor$TZState;->SET_TRUE:Lme/embodied/services/BoSystemMonitor$TZState;

    iput-object p1, p0, Lme/embodied/services/BoSystemMonitor;->timezone_state_:Lme/embodied/services/BoSystemMonitor$TZState;

    .line 209
    iput-object p2, p0, Lme/embodied/services/BoSystemMonitor;->timezone_input_:Ljava/lang/String;

    .line 210
    invoke-direct {p0, p2}, Lme/embodied/services/BoSystemMonitor;->requestSetTimezone(Ljava/lang/String;)V

    .line 212
    :cond_2
    return-void

    .line 215
    :cond_3
    if-eqz p1, :cond_5

    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result p2

    if-lez p2, :cond_5

    .line 217
    iget-object p2, p0, Lme/embodied/services/BoSystemMonitor;->timezone_state_:Lme/embodied/services/BoSystemMonitor$TZState;

    sget-object v0, Lme/embodied/services/BoSystemMonitor$TZState;->UNKNOWN:Lme/embodied/services/BoSystemMonitor$TZState;

    if-eq p2, v0, :cond_4

    iget-object p2, p0, Lme/embodied/services/BoSystemMonitor;->timezone_state_:Lme/embodied/services/BoSystemMonitor$TZState;

    sget-object v0, Lme/embodied/services/BoSystemMonitor$TZState;->SET_GUESS:Lme/embodied/services/BoSystemMonitor$TZState;

    if-ne p2, v0, :cond_5

    iget-object p2, p0, Lme/embodied/services/BoSystemMonitor;->timezone_input_:Ljava/lang/String;

    invoke-virtual {p1, p2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p2

    if-nez p2, :cond_5

    .line 218
    :cond_4
    sget-object p2, Lme/embodied/services/BoSystemMonitor$TZState;->SET_GUESS:Lme/embodied/services/BoSystemMonitor$TZState;

    iput-object p2, p0, Lme/embodied/services/BoSystemMonitor;->timezone_state_:Lme/embodied/services/BoSystemMonitor$TZState;

    .line 219
    iput-object p1, p0, Lme/embodied/services/BoSystemMonitor;->timezone_input_:Ljava/lang/String;

    .line 221
    :try_start_0
    invoke-static {p1}, Lme/embodied/services/BoSystemMonitor;->guessTimezone(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    invoke-direct {p0, p1}, Lme/embodied/services/BoSystemMonitor;->requestSetTimezone(Ljava/lang/String;)V
    :try_end_0
    .catch Ljava/text/ParseException; {:try_start_0 .. :try_end_0} :catch_0

    .line 224
    goto :goto_0

    .line 222
    :catch_0
    move-exception p1

    .line 223
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "Failed to guess timezone from timestamp string: "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v0, p0, Lme/embodied/services/BoSystemMonitor;->timezone_input_:Ljava/lang/String;

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    const-string v0, "bo-system-monitor"

    invoke-static {v0, p2, p1}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)I

    .line 227
    :cond_5
    :goto_0
    return-void
.end method

.method public OnVolumeChangeRequest(IZ)V
    .locals 6

    .line 107
    invoke-static {}, Lme/embodied/VolumeScale;->UpdateLimits()V

    .line 108
    const-string v0, "audio"

    invoke-virtual {p0, v0}, Lme/embodied/services/BoSystemMonitor;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/media/AudioManager;

    .line 109
    const/4 v1, 0x3

    invoke-virtual {v0, v1}, Landroid/media/AudioManager;->getStreamVolume(I)I

    move-result v2

    invoke-static {v2}, Lme/embodied/VolumeScale;->validSystemVolume(I)I

    move-result v2

    .line 111
    if-eqz p2, :cond_0

    .line 113
    iget-object p2, p0, Lme/embodied/services/BoSystemMonitor;->volume_scaler_:Lme/embodied/VolumeScale$Scaler;

    invoke-interface {p2, v2}, Lme/embodied/VolumeScale$Scaler;->systemToPercent(I)I

    move-result p2

    .line 115
    add-int/2addr p1, p2

    .line 120
    :cond_0
    const/4 p2, 0x0

    const/16 v3, 0x64

    if-le p1, v3, :cond_1

    move p1, v3

    goto :goto_0

    :cond_1
    if-gez p1, :cond_2

    move p1, p2

    .line 121
    :cond_2
    :goto_0
    iget-object v3, p0, Lme/embodied/services/BoSystemMonitor;->volume_scaler_:Lme/embodied/VolumeScale$Scaler;

    invoke-interface {v3, p1}, Lme/embodied/VolumeScale$Scaler;->percentToSystem(I)I

    move-result v3

    .line 122
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "VOLUME_REQ: "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v5, " / "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string p1, "%"

    invoke-virtual {v4, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const-string v4, "bo-system-monitor"

    invoke-static {v4, p1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 124
    if-eq v3, v2, :cond_3

    .line 125
    invoke-virtual {v0, v1, v3, p2}, Landroid/media/AudioManager;->setStreamVolume(III)V

    .line 126
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string p2, "Adjusting system volume units to: "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v4, p1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_1

    .line 128
    :cond_3
    const-string p1, "Ignoring volume change, matches current volume already."

    invoke-static {v4, p1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 131
    :goto_1
    return-void
.end method

.method public handleWifiState(Z)V
    .locals 2

    .line 157
    monitor-enter p0

    .line 158
    :try_start_0
    iget-boolean v0, p0, Lme/embodied/services/BoSystemMonitor;->last_wifi_state_:Z

    if-ne v0, p1, :cond_0

    .line 159
    monitor-exit p0

    return-void

    .line 160
    :cond_0
    iput-boolean p1, p0, Lme/embodied/services/BoSystemMonitor;->last_wifi_state_:Z

    .line 161
    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 163
    if-eqz p1, :cond_2

    .line 164
    const-string p1, "bo-system-monitor"

    const-string v0, "WifiState=Connected"

    invoke-static {p1, v0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 165
    iget-object p1, p0, Lme/embodied/services/BoSystemMonitor;->wifi_recovery_thread_:Ljava/lang/Thread;

    if-eqz p1, :cond_1

    .line 166
    invoke-virtual {p1}, Ljava/lang/Thread;->interrupt()V

    .line 168
    :cond_1
    iget-object p1, p0, Lme/embodied/services/BoSystemMonitor;->ntp_service_:Lme/embodied/NTPService;

    invoke-virtual {p1}, Lme/embodied/NTPService;->onNetworkConnect()V

    goto :goto_0

    .line 171
    :cond_2
    const-string p1, "bo-system-monitor"

    const-string v0, "WifiState=Lost, Attempting recovery."

    invoke-static {p1, v0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 172
    iget-wide v0, p0, Lme/embodied/services/BoSystemMonitor;->ptr:J

    const/4 p1, 0x0

    invoke-direct {p0, v0, v1, p1}, Lme/embodied/services/BoSystemMonitor;->reportWifiRSSI(JI)V

    .line 173
    iget-object p1, p0, Lme/embodied/services/BoSystemMonitor;->ntp_service_:Lme/embodied/NTPService;

    invoke-virtual {p1}, Lme/embodied/NTPService;->onNetworkDisconnect()V

    .line 174
    new-instance p1, Lme/embodied/services/BoSystemMonitor$1;

    invoke-direct {p1, p0}, Lme/embodied/services/BoSystemMonitor$1;-><init>(Lme/embodied/services/BoSystemMonitor;)V

    iput-object p1, p0, Lme/embodied/services/BoSystemMonitor;->wifi_recovery_thread_:Ljava/lang/Thread;

    .line 185
    invoke-virtual {p1}, Ljava/lang/Thread;->start()V

    .line 187
    :goto_0
    return-void

    .line 161
    :catchall_0
    move-exception p1

    :try_start_1
    monitor-exit p0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw p1
.end method

.method public onStartCommand(Landroid/content/Intent;II)I
    .locals 4

    .line 70
    const-string v0, "bo-system-monitor"

    :try_start_0
    invoke-virtual {p0}, Lme/embodied/services/BoSystemMonitor;->getContentResolver()Landroid/content/ContentResolver;

    move-result-object v1

    const-string v2, "auto_time_zone"

    const/4 v3, 0x0

    invoke-static {v1, v2, v3}, Landroid/provider/Settings$Global;->getInt(Landroid/content/ContentResolver;Ljava/lang/String;I)I

    move-result v1

    if-eqz v1, :cond_0

    const/4 v3, 0x1

    :cond_0
    iput-boolean v3, p0, Lme/embodied/services/BoSystemMonitor;->auto_timezone_enabled_:Z

    .line 71
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Setting timezone automatically is "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-boolean v2, p0, Lme/embodied/services/BoSystemMonitor;->auto_timezone_enabled_:Z

    if-eqz v2, :cond_1

    const-string v2, "ENABLED"

    goto :goto_0

    :cond_1
    const-string v2, "DISABLED"

    :goto_0
    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 74
    goto :goto_1

    .line 72
    :catch_0
    move-exception v1

    .line 73
    const-string v2, "Failed to determine automatic timezone setting."

    invoke-static {v0, v2, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)I

    .line 75
    :goto_1
    invoke-super {p0, p1, p2, p3}, Lme/embodied/services/Service;->onStartCommand(Landroid/content/Intent;II)I

    move-result p1

    return p1
.end method

.method public wifiAddNetwork(Ljava/lang/String;Ljava/lang/String;IZZ)V
    .locals 2

    .line 361
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Adding Wifi Network: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "bo-system-monitor"

    invoke-static {v1, v0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 362
    invoke-static/range {p0 .. p5}, Lme/embodied/NetworkUtil;->addWifiNetwork(Landroid/content/Context;Ljava/lang/String;Ljava/lang/String;IZZ)Lme/embodied/NetworkUtil$WifiResult;

    .line 363
    return-void
.end method

.method public wifiClearNetworks()V
    .locals 2

    .line 367
    const-string v0, "bo-system-monitor"

    const-string v1, "Clearing Wifi Networks"

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 368
    invoke-static {p0}, Lme/embodied/NetworkUtil;->removeAllNetworks(Landroid/content/Context;)Z

    .line 369
    return-void
.end method
