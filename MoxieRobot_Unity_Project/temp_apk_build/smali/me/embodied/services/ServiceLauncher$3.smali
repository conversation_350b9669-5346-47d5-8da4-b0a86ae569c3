.class Lme/embodied/services/ServiceLauncher$3;
.super Landroid/content/BroadcastReceiver;
.source "ServiceLauncher.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lme/embodied/services/ServiceLauncher;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lme/embodied/services/ServiceLauncher;


# direct methods
.method constructor <init>(Lme/embodied/services/ServiceLauncher;)V
    .locals 0

    .line 238
    iput-object p1, p0, Lme/embodied/services/ServiceLauncher$3;->this$0:Lme/embodied/services/ServiceLauncher;

    invoke-direct {p0}, Landroid/content/BroadcastReceiver;-><init>()V

    return-void
.end method


# virtual methods
.method public onReceive(Landroid/content/Context;Landroid/content/Intent;)V
    .locals 1

    .line 241
    invoke-virtual {p2}, Landroid/content/Intent;->getAction()Ljava/lang/String;

    move-result-object p1

    const-string v0, "com.embodied.useralarm"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equalsIgnoreCase(Ljava/lang/String;)Z

    move-result p1

    if-eqz p1, :cond_0

    .line 242
    invoke-virtual {p2}, Landroid/content/Intent;->getData()Landroid/net/Uri;

    move-result-object p1

    invoke-virtual {p1}, Landroid/net/Uri;->getSchemeSpecificPart()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result p1

    .line 243
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "RX User Alarm: "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    const-string v0, "bo-launcher-s"

    invoke-static {v0, p2}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 244
    iget-object p2, p0, Lme/embodied/services/ServiceLauncher$3;->this$0:Lme/embodied/services/ServiceLauncher;

    invoke-static {p2}, Lme/embodied/services/ServiceLauncher;->access$300(Lme/embodied/services/ServiceLauncher;)Lme/embodied/services/Launcher;

    move-result-object p2

    invoke-virtual {p2, p1}, Lme/embodied/services/Launcher;->OnUserAlarmPing(I)V

    .line 246
    :cond_0
    return-void
.end method
