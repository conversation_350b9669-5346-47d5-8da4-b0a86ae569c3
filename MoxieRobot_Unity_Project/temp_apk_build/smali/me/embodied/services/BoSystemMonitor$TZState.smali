.class final enum Lme/embodied/services/BoSystemMonitor$TZState;
.super Ljava/lang/Enum;
.source "BoSystemMonitor.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lme/embodied/services/BoSystemMonitor;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4018
    name = "TZState"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lme/embodied/services/BoSystemMonitor$TZState;",
        ">;"
    }
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lme/embodied/services/BoSystemMonitor$TZState;

.field public static final enum SET_GUESS:Lme/embodied/services/BoSystemMonitor$TZState;

.field public static final enum SET_TRUE:Lme/embodied/services/BoSystemMonitor$TZState;

.field public static final enum UNKNOWN:Lme/embodied/services/BoSystemMonitor$TZState;


# direct methods
.method static constructor <clinit>()V
    .locals 6

    .line 39
    new-instance v0, Lme/embodied/services/BoSystemMonitor$TZState;

    const-string v1, "UNKNOWN"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lme/embodied/services/BoSystemMonitor$TZState;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lme/embodied/services/BoSystemMonitor$TZState;->UNKNOWN:Lme/embodied/services/BoSystemMonitor$TZState;

    .line 40
    new-instance v0, Lme/embodied/services/BoSystemMonitor$TZState;

    const-string v1, "SET_GUESS"

    const/4 v3, 0x1

    invoke-direct {v0, v1, v3}, Lme/embodied/services/BoSystemMonitor$TZState;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lme/embodied/services/BoSystemMonitor$TZState;->SET_GUESS:Lme/embodied/services/BoSystemMonitor$TZState;

    .line 41
    new-instance v0, Lme/embodied/services/BoSystemMonitor$TZState;

    const-string v1, "SET_TRUE"

    const/4 v4, 0x2

    invoke-direct {v0, v1, v4}, Lme/embodied/services/BoSystemMonitor$TZState;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lme/embodied/services/BoSystemMonitor$TZState;->SET_TRUE:Lme/embodied/services/BoSystemMonitor$TZState;

    .line 38
    const/4 v1, 0x3

    new-array v1, v1, [Lme/embodied/services/BoSystemMonitor$TZState;

    sget-object v5, Lme/embodied/services/BoSystemMonitor$TZState;->UNKNOWN:Lme/embodied/services/BoSystemMonitor$TZState;

    aput-object v5, v1, v2

    sget-object v2, Lme/embodied/services/BoSystemMonitor$TZState;->SET_GUESS:Lme/embodied/services/BoSystemMonitor$TZState;

    aput-object v2, v1, v3

    aput-object v0, v1, v4

    sput-object v1, Lme/embodied/services/BoSystemMonitor$TZState;->$VALUES:[Lme/embodied/services/BoSystemMonitor$TZState;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 38
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lme/embodied/services/BoSystemMonitor$TZState;
    .locals 1

    .line 38
    const-class v0, Lme/embodied/services/BoSystemMonitor$TZState;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lme/embodied/services/BoSystemMonitor$TZState;

    return-object p0
.end method

.method public static values()[Lme/embodied/services/BoSystemMonitor$TZState;
    .locals 1

    .line 38
    sget-object v0, Lme/embodied/services/BoSystemMonitor$TZState;->$VALUES:[Lme/embodied/services/BoSystemMonitor$TZState;

    invoke-virtual {v0}, [Lme/embodied/services/BoSystemMonitor$TZState;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lme/embodied/services/BoSystemMonitor$TZState;

    return-object v0
.end method
