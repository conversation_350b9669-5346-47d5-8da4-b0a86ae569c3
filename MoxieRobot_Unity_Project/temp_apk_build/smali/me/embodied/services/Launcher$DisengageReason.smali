.class final enum Lme/embodied/services/Launcher$DisengageReason;
.super Ljava/lang/Enum;
.source "Launcher.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lme/embodied/services/Launcher;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4018
    name = "DisengageReason"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lme/embodied/services/Launcher$DisengageReason;",
        ">;"
    }
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lme/embodied/services/Launcher$DisengageReason;

.field public static final enum TELEHEALTH:Lme/embodied/services/Launcher$DisengageReason;

.field public static final enum UNPAIRING:Lme/embodied/services/Launcher$DisengageReason;

.field public static final enum USER_DATA_UPDATE:Lme/embodied/services/Launcher$DisengageReason;


# direct methods
.method static constructor <clinit>()V
    .locals 6

    .line 99
    new-instance v0, Lme/embodied/services/Launcher$DisengageReason;

    const-string v1, "UNPAIRING"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lme/embodied/services/Launcher$DisengageReason;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lme/embodied/services/Launcher$DisengageReason;->UNPAIRING:Lme/embodied/services/Launcher$DisengageReason;

    .line 100
    new-instance v0, Lme/embodied/services/Launcher$DisengageReason;

    const-string v1, "TELEHEALTH"

    const/4 v3, 0x1

    invoke-direct {v0, v1, v3}, Lme/embodied/services/Launcher$DisengageReason;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lme/embodied/services/Launcher$DisengageReason;->TELEHEALTH:Lme/embodied/services/Launcher$DisengageReason;

    .line 101
    new-instance v0, Lme/embodied/services/Launcher$DisengageReason;

    const-string v1, "USER_DATA_UPDATE"

    const/4 v4, 0x2

    invoke-direct {v0, v1, v4}, Lme/embodied/services/Launcher$DisengageReason;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lme/embodied/services/Launcher$DisengageReason;->USER_DATA_UPDATE:Lme/embodied/services/Launcher$DisengageReason;

    .line 98
    const/4 v1, 0x3

    new-array v1, v1, [Lme/embodied/services/Launcher$DisengageReason;

    sget-object v5, Lme/embodied/services/Launcher$DisengageReason;->UNPAIRING:Lme/embodied/services/Launcher$DisengageReason;

    aput-object v5, v1, v2

    sget-object v2, Lme/embodied/services/Launcher$DisengageReason;->TELEHEALTH:Lme/embodied/services/Launcher$DisengageReason;

    aput-object v2, v1, v3

    aput-object v0, v1, v4

    sput-object v1, Lme/embodied/services/Launcher$DisengageReason;->$VALUES:[Lme/embodied/services/Launcher$DisengageReason;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 98
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lme/embodied/services/Launcher$DisengageReason;
    .locals 1

    .line 98
    const-class v0, Lme/embodied/services/Launcher$DisengageReason;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lme/embodied/services/Launcher$DisengageReason;

    return-object p0
.end method

.method public static values()[Lme/embodied/services/Launcher$DisengageReason;
    .locals 1

    .line 98
    sget-object v0, Lme/embodied/services/Launcher$DisengageReason;->$VALUES:[Lme/embodied/services/Launcher$DisengageReason;

    invoke-virtual {v0}, [Lme/embodied/services/Launcher$DisengageReason;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lme/embodied/services/Launcher$DisengageReason;

    return-object v0
.end method
