.class interface abstract Lme/embodied/services/Launcher$ComponentFilter;
.super Ljava/lang/Object;
.source "Launcher.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lme/embodied/services/Launcher;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x608
    name = "ComponentFilter"
.end annotation


# virtual methods
.method public abstract ComponentAllowed(Lme/embodied/services/Launcher$BOComponent;Z)Z
.end method
