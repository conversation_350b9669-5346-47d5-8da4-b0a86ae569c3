.class public Lme/embodied/services/BoAudio;
.super Lme/embodied/services/Service;
.source "BoAudio.java"


# static fields
.field static final SERVICE_LIB:Ljava/lang/String; = "bo-audio"


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 19
    invoke-static {}, Lme/embodied/services/BoAudio;->serviceInitName()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v0}, Lme/embodied/services/Service;-><init>(Ljava/lang/String;)V

    .line 20
    return-void
.end method

.method static serviceInitName()Ljava/lang/String;
    .locals 4

    .line 10
    const-string v0, "bo-audio"

    :try_start_0
    const-string v1, "OMP_NUM_THREADS"

    const-string v2, "1"

    const/4 v3, 0x1

    invoke-static {v1, v2, v3}, Landroid/system/Os;->setenv(Ljava/lang/String;Ljava/lang/String;Z)V

    .line 11
    const-string v1, "Set OMP_NUM_THREADS to ONE"

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 14
    goto :goto_0

    .line 12
    :catch_0
    move-exception v1

    .line 13
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Failed to set OMP_NUM_THREADS: "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 15
    :goto_0
    return-object v0
.end method
