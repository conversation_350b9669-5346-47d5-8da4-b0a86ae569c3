.class Lme/embodied/services/Service$TimedRelease;
.super Ljava/lang/Thread;
.source "Service.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lme/embodied/services/Service;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = "TimedRelease"
.end annotation


# instance fields
.field private is_complete_:Z

.field final synthetic this$0:Lme/embodied/services/Service;


# direct methods
.method constructor <init>(Lme/embodied/services/Service;)V
    .locals 0

    .line 105
    iput-object p1, p0, Lme/embodied/services/Service$TimedRelease;->this$0:Lme/embodied/services/Service;

    invoke-direct {p0}, Ljava/lang/Thread;-><init>()V

    .line 106
    const/4 p1, 0x0

    iput-boolean p1, p0, Lme/embodied/services/Service$TimedRelease;->is_complete_:Z

    return-void
.end method


# virtual methods
.method public run()V
    .locals 3

    .line 109
    iget-object v0, p0, Lme/embodied/services/Service$TimedRelease;->this$0:Lme/embodied/services/Service;

    iget-wide v1, v0, Lme/embodied/services/Service;->ptr:J

    invoke-virtual {v0, v1, v2}, Lme/embodied/services/Service;->ReleaseService(J)V

    .line 110
    monitor-enter p0

    .line 112
    const/4 v0, 0x1

    :try_start_0
    iput-boolean v0, p0, Lme/embodied/services/Service$TimedRelease;->is_complete_:Z

    .line 113
    invoke-virtual {p0}, Ljava/lang/Object;->notifyAll()V

    .line 114
    monitor-exit p0

    .line 115
    return-void

    .line 114
    :catchall_0
    move-exception v0

    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw v0
.end method

.method declared-synchronized timed_release(J)Z
    .locals 0

    monitor-enter p0

    .line 119
    :try_start_0
    invoke-virtual {p0}, Lme/embodied/services/Service$TimedRelease;->start()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 121
    :try_start_1
    invoke-virtual {p0, p1, p2}, Ljava/lang/Object;->wait(J)V
    :try_end_1
    .catch Ljava/lang/InterruptedException; {:try_start_1 .. :try_end_1} :catch_0
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 124
    goto :goto_0

    .line 122
    :catch_0
    move-exception p1

    .line 123
    :try_start_2
    iget-object p1, p0, Lme/embodied/services/Service$TimedRelease;->this$0:Lme/embodied/services/Service;

    invoke-virtual {p1}, Lme/embodied/services/Service;->ServiceName()Ljava/lang/String;

    move-result-object p1

    const-string p2, "Background release interrupted."

    invoke-static {p1, p2}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 125
    :goto_0
    iget-boolean p1, p0, Lme/embodied/services/Service$TimedRelease;->is_complete_:Z
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    monitor-exit p0

    return p1

    .line 118
    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method
