.class Lme/embodied/services/Launcher$6;
.super Ljava/lang/Thread;
.source "Launcher.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lme/embodied/services/Launcher;->OnConfigComplete()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lme/embodied/services/Launcher;


# direct methods
.method constructor <init>(Lme/embodied/services/Launcher;)V
    .locals 0

    .line 1339
    iput-object p1, p0, Lme/embodied/services/Launcher$6;->this$0:Lme/embodied/services/Launcher;

    invoke-direct {p0}, Ljava/lang/Thread;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 5

    .line 1341
    nop

    .line 1342
    const-string v0, "file_sync_wait"

    invoke-static {v0}, Lme/embodied/DeviceSettings;->getIntS(Ljava/lang/String;)I

    move-result v0

    .line 1343
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Configuration completed. Waiting up to "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v2, "s for file syncs before starting Bo-Unity."

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const-string v2, "bo-launcher-j"

    invoke-static {v2, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    const/4 v1, 0x0

    .line 1345
    :cond_0
    add-int/lit8 v1, v1, 0x1

    if-le v1, v0, :cond_1

    .line 1347
    const-string v0, "BO#8016 File sync wait gave up after max wait."

    invoke-static {v2, v0}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 1348
    goto :goto_0

    .line 1351
    :cond_1
    const-wide/16 v3, 0x3e8

    :try_start_0
    invoke-static {v3, v4}, Ljava/lang/Thread;->sleep(J)V
    :try_end_0
    .catch Ljava/lang/InterruptedException; {:try_start_0 .. :try_end_0} :catch_0

    .line 1355
    nop

    .line 1356
    invoke-static {}, Lme/embodied/services/Launcher;->access$1300()Z

    move-result v3

    if-nez v3, :cond_0

    goto :goto_0

    .line 1352
    :catch_0
    move-exception v0

    .line 1353
    const-string v0, "File sync wait interrupted."

    invoke-static {v2, v0}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 1354
    nop

    .line 1358
    :goto_0
    const-string v0, "FileSync wait complete. Starting Bo-Unity."

    invoke-static {v2, v0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1359
    iget-object v0, p0, Lme/embodied/services/Launcher$6;->this$0:Lme/embodied/services/Launcher;

    sget-object v1, Lme/embodied/services/Launcher$LauncherState;->STATE_STARTUP:Lme/embodied/services/Launcher$LauncherState;

    invoke-static {v0, v1}, Lme/embodied/services/Launcher;->access$500(Lme/embodied/services/Launcher;Lme/embodied/services/Launcher$LauncherState;)V

    .line 1360
    return-void
.end method
