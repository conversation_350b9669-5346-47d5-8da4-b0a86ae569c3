.class Lme/embodied/services/XMOSDFU$IncomingHandler;
.super Landroid/os/Handler;
.source "XMOSDFU.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lme/embodied/services/XMOSDFU;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x2
    name = "IncomingHandler"
.end annotation


# instance fields
.field private final activity_:Ljava/lang/ref/WeakReference;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/lang/ref/WeakReference<",
            "Lme/embodied/services/XMOSDFU;",
            ">;"
        }
    .end annotation
.end field

.field final synthetic this$0:Lme/embodied/services/XMOSDFU;


# direct methods
.method constructor <init>(Lme/embodied/services/XMOSDFU;Ljava/lang/ref/WeakReference;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/ref/WeakReference<",
            "Lme/embodied/services/XMOSDFU;",
            ">;)V"
        }
    .end annotation

    .line 299
    iput-object p1, p0, Lme/embodied/services/XMOSDFU$IncomingHandler;->this$0:Lme/embodied/services/XMOSDFU;

    invoke-direct {p0}, Landroid/os/Handler;-><init>()V

    .line 300
    iput-object p2, p0, Lme/embodied/services/XMOSDFU$IncomingHandler;->activity_:Ljava/lang/ref/WeakReference;

    .line 301
    return-void
.end method


# virtual methods
.method public handleMessage(Landroid/os/Message;)V
    .locals 6

    .line 305
    iget v0, p1, Landroid/os/Message;->what:I

    const/4 v1, 0x2

    if-eq v0, v1, :cond_0

    goto :goto_0

    .line 307
    :cond_0
    iget-object v0, p1, Landroid/os/Message;->obj:Ljava/lang/Object;

    const-string v2, "XMOSDFU"

    if-nez v0, :cond_1

    .line 308
    const-string v0, "No state was passed in the message."

    invoke-static {v2, v0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_0

    .line 310
    :cond_1
    new-instance v0, Landroid/os/Bundle;

    invoke-direct {v0}, Landroid/os/Bundle;-><init>()V

    .line 311
    iget-object v0, p1, Landroid/os/Message;->obj:Ljava/lang/Object;

    check-cast v0, Landroid/os/Bundle;

    .line 313
    const-string v3, "progress"

    invoke-virtual {v0, v3}, Landroid/os/Bundle;->getInt(Ljava/lang/String;)I

    move-result v3

    .line 314
    const-string v4, "state"

    invoke-virtual {v0, v4}, Landroid/os/Bundle;->getInt(Ljava/lang/String;)I

    move-result v0

    .line 316
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "XMOS State: "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v5, ", Progress: "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-static {v2, v3}, Landroid/util/Log;->v(Ljava/lang/String;Ljava/lang/String;)I

    .line 318
    if-eq v0, v1, :cond_3

    const/4 v1, 0x3

    if-eq v0, v1, :cond_2

    .line 321
    goto :goto_0

    .line 331
    :cond_2
    const-string v0, "BO#1041 XMOS Update Failed!"

    invoke-static {v2, v0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 332
    iget-object v0, p0, Lme/embodied/services/XMOSDFU$IncomingHandler;->this$0:Lme/embodied/services/XMOSDFU;

    invoke-static {v0}, Lme/embodied/services/XMOSDFU;->access$600(Lme/embodied/services/XMOSDFU;)V

    .line 333
    iget-object v0, p0, Lme/embodied/services/XMOSDFU$IncomingHandler;->this$0:Lme/embodied/services/XMOSDFU;

    invoke-static {v0}, Lme/embodied/services/XMOSDFU;->access$700(Lme/embodied/services/XMOSDFU;)V

    goto :goto_0

    .line 325
    :cond_3
    iget-object v0, p0, Lme/embodied/services/XMOSDFU$IncomingHandler;->this$0:Lme/embodied/services/XMOSDFU;

    invoke-virtual {v0}, Lme/embodied/services/XMOSDFU;->XmosUpdateSharedPref()V

    .line 326
    iget-object v0, p0, Lme/embodied/services/XMOSDFU$IncomingHandler;->this$0:Lme/embodied/services/XMOSDFU;

    invoke-static {v0}, Lme/embodied/services/XMOSDFU;->access$600(Lme/embodied/services/XMOSDFU;)V

    .line 327
    iget-object v0, p0, Lme/embodied/services/XMOSDFU$IncomingHandler;->this$0:Lme/embodied/services/XMOSDFU;

    invoke-static {v0}, Lme/embodied/services/XMOSDFU;->access$700(Lme/embodied/services/XMOSDFU;)V

    .line 328
    const-string v0, "BO#1040 XMOS Update Succeeded"

    invoke-static {v2, v0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 329
    nop

    .line 338
    :goto_0
    invoke-super {p0, p1}, Landroid/os/Handler;->handleMessage(Landroid/os/Message;)V

    .line 340
    return-void
.end method
