.class final enum Lme/embodied/services/Launcher$UserDataRecoveryMode;
.super Ljava/lang/Enum;
.source "Launcher.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lme/embodied/services/Launcher;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4018
    name = "UserDataRecoveryMode"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lme/embodied/services/Launcher$UserDataRecoveryMode;",
        ">;"
    }
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lme/embodied/services/Launcher$UserDataRecoveryMode;

.field public static final enum RECOVER_CLOUD:Lme/embodied/services/Launcher$UserDataRecoveryMode;

.field public static final enum RECOVER_LOCAL:Lme/embodied/services/Launcher$UserDataRecoveryMode;

.field public static final enum UPDATE_CLOUD:Lme/embodied/services/Launcher$UserDataRecoveryMode;


# direct methods
.method static constructor <clinit>()V
    .locals 6

    .line 89
    new-instance v0, Lme/embodied/services/Launcher$UserDataRecoveryMode;

    const-string v1, "RECOVER_CLOUD"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lme/embodied/services/Launcher$UserDataRecoveryMode;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lme/embodied/services/Launcher$UserDataRecoveryMode;->RECOVER_CLOUD:Lme/embodied/services/Launcher$UserDataRecoveryMode;

    .line 90
    new-instance v0, Lme/embodied/services/Launcher$UserDataRecoveryMode;

    const-string v1, "RECOVER_LOCAL"

    const/4 v3, 0x1

    invoke-direct {v0, v1, v3}, Lme/embodied/services/Launcher$UserDataRecoveryMode;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lme/embodied/services/Launcher$UserDataRecoveryMode;->RECOVER_LOCAL:Lme/embodied/services/Launcher$UserDataRecoveryMode;

    .line 91
    new-instance v0, Lme/embodied/services/Launcher$UserDataRecoveryMode;

    const-string v1, "UPDATE_CLOUD"

    const/4 v4, 0x2

    invoke-direct {v0, v1, v4}, Lme/embodied/services/Launcher$UserDataRecoveryMode;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lme/embodied/services/Launcher$UserDataRecoveryMode;->UPDATE_CLOUD:Lme/embodied/services/Launcher$UserDataRecoveryMode;

    .line 88
    const/4 v1, 0x3

    new-array v1, v1, [Lme/embodied/services/Launcher$UserDataRecoveryMode;

    sget-object v5, Lme/embodied/services/Launcher$UserDataRecoveryMode;->RECOVER_CLOUD:Lme/embodied/services/Launcher$UserDataRecoveryMode;

    aput-object v5, v1, v2

    sget-object v2, Lme/embodied/services/Launcher$UserDataRecoveryMode;->RECOVER_LOCAL:Lme/embodied/services/Launcher$UserDataRecoveryMode;

    aput-object v2, v1, v3

    aput-object v0, v1, v4

    sput-object v1, Lme/embodied/services/Launcher$UserDataRecoveryMode;->$VALUES:[Lme/embodied/services/Launcher$UserDataRecoveryMode;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 88
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lme/embodied/services/Launcher$UserDataRecoveryMode;
    .locals 1

    .line 88
    const-class v0, Lme/embodied/services/Launcher$UserDataRecoveryMode;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lme/embodied/services/Launcher$UserDataRecoveryMode;

    return-object p0
.end method

.method public static values()[Lme/embodied/services/Launcher$UserDataRecoveryMode;
    .locals 1

    .line 88
    sget-object v0, Lme/embodied/services/Launcher$UserDataRecoveryMode;->$VALUES:[Lme/embodied/services/Launcher$UserDataRecoveryMode;

    invoke-virtual {v0}, [Lme/embodied/services/Launcher$UserDataRecoveryMode;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lme/embodied/services/Launcher$UserDataRecoveryMode;

    return-object v0
.end method
