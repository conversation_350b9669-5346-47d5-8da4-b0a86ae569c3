.class public Lme/embodied/services/ServiceLauncher;
.super Landroid/app/Service;
.source "ServiceLauncher.java"


# static fields
.field private static final ACTION_EMBODIED_SHUTDOWN_LAUNCHER:Ljava/lang/String; = "ACTION_EMBODIED_SHUTDOWN_LAUNCHER"

.field public static final ACTION_HEARTBEAT:Ljava/lang/String; = "com.embodied.heartbeat"

.field public static final ACTION_OTA_CHECK_COMPLETE:Ljava/lang/String; = "com.embodied.ota_check_complete"

.field public static final ACTION_OTA_STATUS:Ljava/lang/String; = "com.embodied.osctrl.ota_status"

.field public static final ACTION_USERALARM:Ljava/lang/String; = "com.embodied.useralarm"

.field public static final ACTION_WAKEWORD:Ljava/lang/String; = "com.embodied.wakeword_detect"

.field private static final CHANNEL_ID:Ljava/lang/String; = "ServiceLauncherChannel"

.field private static final EXTRA_FROM_UNITY:Ljava/lang/String; = "unity"

.field private static final EXTRA_FROM_UNIT_TEST:Ljava/lang/String; = "utest"

.field public static final REBOOT_CLEAR_FILE:Ljava/io/File;

.field private static final TAG:Ljava/lang/String; = "bo-launcher-s"

.field private static final USER_ALARM_SCHEME:Ljava/lang/String; = "ebalarm"


# instance fields
.field private alarm_running_:Z

.field private init_needed_:Z

.field private final intentReceiver:Landroid/content/BroadcastReceiver;

.field private launcher_:Lme/embodied/services/Launcher;

.field private final otaCheckReceiver:Landroid/content/BroadcastReceiver;

.field private final userAlarmReceiver:Landroid/content/BroadcastReceiver;

.field private xmos_ready_:Z

.field private xmos_run_on_complete_:Z

.field private xmos_update_started_:Z


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 41
    new-instance v0, Ljava/io/File;

    const-string v1, "/sdcard/EmbodiedData/.otaRebootRequired"

    invoke-direct {v0, v1}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    sput-object v0, Lme/embodied/services/ServiceLauncher;->REBOOT_CLEAR_FILE:Ljava/io/File;

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    .line 25
    invoke-direct {p0}, Landroid/app/Service;-><init>()V

    .line 45
    const/4 v0, 0x0

    iput-boolean v0, p0, Lme/embodied/services/ServiceLauncher;->xmos_run_on_complete_:Z

    .line 46
    iput-boolean v0, p0, Lme/embodied/services/ServiceLauncher;->xmos_update_started_:Z

    .line 47
    iput-boolean v0, p0, Lme/embodied/services/ServiceLauncher;->xmos_ready_:Z

    .line 48
    iput-boolean v0, p0, Lme/embodied/services/ServiceLauncher;->alarm_running_:Z

    .line 49
    const/4 v0, 0x1

    iput-boolean v0, p0, Lme/embodied/services/ServiceLauncher;->init_needed_:Z

    .line 138
    new-instance v0, Lme/embodied/services/ServiceLauncher$1;

    invoke-direct {v0, p0}, Lme/embodied/services/ServiceLauncher$1;-><init>(Lme/embodied/services/ServiceLauncher;)V

    iput-object v0, p0, Lme/embodied/services/ServiceLauncher;->otaCheckReceiver:Landroid/content/BroadcastReceiver;

    .line 212
    new-instance v0, Lme/embodied/services/ServiceLauncher$2;

    invoke-direct {v0, p0}, Lme/embodied/services/ServiceLauncher$2;-><init>(Lme/embodied/services/ServiceLauncher;)V

    iput-object v0, p0, Lme/embodied/services/ServiceLauncher;->intentReceiver:Landroid/content/BroadcastReceiver;

    .line 238
    new-instance v0, Lme/embodied/services/ServiceLauncher$3;

    invoke-direct {v0, p0}, Lme/embodied/services/ServiceLauncher$3;-><init>(Lme/embodied/services/ServiceLauncher;)V

    iput-object v0, p0, Lme/embodied/services/ServiceLauncher;->userAlarmReceiver:Landroid/content/BroadcastReceiver;

    return-void
.end method

.method public static LaunchExtern(Landroid/content/Context;)V
    .locals 3

    .line 250
    new-instance v0, Landroid/content/Intent;

    const-class v1, Lme/embodied/services/ServiceLauncher;

    invoke-direct {v0, p0, v1}, Landroid/content/Intent;-><init>(Landroid/content/Context;Ljava/lang/Class;)V

    .line 251
    const-string v1, "unity"

    const/4 v2, 0x1

    invoke-virtual {v0, v1, v2}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Z)Landroid/content/Intent;

    .line 252
    invoke-virtual {p0, v0}, Landroid/content/Context;->startForegroundService(Landroid/content/Intent;)Landroid/content/ComponentName;

    .line 253
    return-void
.end method

.method public static StopExtern(Landroid/content/Context;)V
    .locals 1

    .line 256
    const-string p0, "bo-launcher-s"

    const-string v0, "Received StopExtern from Unity.  Currently defunct."

    invoke-static {p0, v0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 257
    return-void
.end method

.method static synthetic access$000(Lme/embodied/services/ServiceLauncher;)Landroid/content/BroadcastReceiver;
    .locals 0

    .line 25
    iget-object p0, p0, Lme/embodied/services/ServiceLauncher;->otaCheckReceiver:Landroid/content/BroadcastReceiver;

    return-object p0
.end method

.method static synthetic access$102(Lme/embodied/services/ServiceLauncher;Z)Z
    .locals 0

    .line 25
    iput-boolean p1, p0, Lme/embodied/services/ServiceLauncher;->xmos_ready_:Z

    return p1
.end method

.method static synthetic access$200(Lme/embodied/services/ServiceLauncher;)Z
    .locals 0

    .line 25
    iget-boolean p0, p0, Lme/embodied/services/ServiceLauncher;->xmos_run_on_complete_:Z

    return p0
.end method

.method static synthetic access$300(Lme/embodied/services/ServiceLauncher;)Lme/embodied/services/Launcher;
    .locals 0

    .line 25
    iget-object p0, p0, Lme/embodied/services/ServiceLauncher;->launcher_:Lme/embodied/services/Launcher;

    return-object p0
.end method

.method private createNotificationChannel()V
    .locals 4

    .line 171
    new-instance v0, Landroid/app/NotificationChannel;

    const-string v1, "ServiceLauncherChannel"

    const-string v2, "Embodied Service Launcher Channel"

    const/4 v3, 0x2

    invoke-direct {v0, v1, v2, v3}, Landroid/app/NotificationChannel;-><init>(Ljava/lang/String;Ljava/lang/CharSequence;I)V

    .line 177
    const-string v1, "Notifications from the Service Launcher"

    invoke-virtual {v0, v1}, Landroid/app/NotificationChannel;->setDescription(Ljava/lang/String;)V

    .line 179
    const-class v1, Landroid/app/NotificationManager;

    invoke-virtual {p0, v1}, Lme/embodied/services/ServiceLauncher;->getSystemService(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroid/app/NotificationManager;

    .line 180
    invoke-virtual {v1, v0}, Landroid/app/NotificationManager;->createNotificationChannel(Landroid/app/NotificationChannel;)V

    .line 181
    return-void
.end method

.method private startForeground()V
    .locals 6

    .line 185
    new-instance v0, Landroid/content/Intent;

    invoke-direct {v0}, Landroid/content/Intent;-><init>()V

    .line 186
    const-string v1, "ACTION_EMBODIED_SHUTDOWN_LAUNCHER"

    invoke-virtual {v0, v1}, Landroid/content/Intent;->setAction(Ljava/lang/String;)Landroid/content/Intent;

    .line 187
    const/4 v1, 0x0

    invoke-static {p0, v1, v0, v1}, Landroid/app/PendingIntent;->getBroadcast(Landroid/content/Context;ILandroid/content/Intent;I)Landroid/app/PendingIntent;

    move-result-object v0

    .line 188
    new-instance v2, Landroid/app/Notification$Action$Builder;

    const/4 v3, 0x0

    const-string v4, "Shutdown"

    invoke-direct {v2, v3, v4, v0}, Landroid/app/Notification$Action$Builder;-><init>(Landroid/graphics/drawable/Icon;Ljava/lang/CharSequence;Landroid/app/PendingIntent;)V

    invoke-virtual {v2}, Landroid/app/Notification$Action$Builder;->build()Landroid/app/Notification$Action;

    move-result-object v0

    .line 190
    new-instance v2, Landroid/content/Intent;

    const-class v3, Lme/embodied/services/ServiceLauncher;

    invoke-direct {v2, p0, v3}, Landroid/content/Intent;-><init>(Landroid/content/Context;Ljava/lang/Class;)V

    .line 191
    invoke-static {p0, v1, v2, v1}, Landroid/app/PendingIntent;->getActivity(Landroid/content/Context;ILandroid/content/Intent;I)Landroid/app/PendingIntent;

    move-result-object v2

    .line 194
    sget-object v3, Landroid/graphics/Bitmap$Config;->ARGB_8888:Landroid/graphics/Bitmap$Config;

    const/16 v4, 0x64

    invoke-static {v4, v4, v3}, Landroid/graphics/Bitmap;->createBitmap(IILandroid/graphics/Bitmap$Config;)Landroid/graphics/Bitmap;

    move-result-object v3

    .line 195
    const/16 v4, 0xc8

    const/16 v5, 0xff

    invoke-static {v1, v4, v5}, Landroid/graphics/Color;->rgb(III)I

    move-result v1

    invoke-virtual {v3, v1}, Landroid/graphics/Bitmap;->eraseColor(I)V

    .line 196
    invoke-static {v3}, Landroid/graphics/drawable/Icon;->createWithBitmap(Landroid/graphics/Bitmap;)Landroid/graphics/drawable/Icon;

    move-result-object v1

    .line 199
    new-instance v3, Landroid/app/Notification$Builder;

    const-string v4, "ServiceLauncherChannel"

    invoke-direct {v3, p0, v4}, Landroid/app/Notification$Builder;-><init>(Landroid/content/Context;Ljava/lang/String;)V

    .line 200
    const-string v4, "service"

    invoke-virtual {v3, v4}, Landroid/app/Notification$Builder;->setCategory(Ljava/lang/String;)Landroid/app/Notification$Builder;

    move-result-object v3

    .line 201
    const-string v4, "Embodied Service Launcher"

    invoke-virtual {v3, v4}, Landroid/app/Notification$Builder;->setContentTitle(Ljava/lang/CharSequence;)Landroid/app/Notification$Builder;

    move-result-object v3

    .line 202
    const-string v4, "Running..."

    invoke-virtual {v3, v4}, Landroid/app/Notification$Builder;->setContentText(Ljava/lang/CharSequence;)Landroid/app/Notification$Builder;

    move-result-object v3

    .line 203
    invoke-virtual {v3, v2}, Landroid/app/Notification$Builder;->setContentIntent(Landroid/app/PendingIntent;)Landroid/app/Notification$Builder;

    move-result-object v2

    .line 204
    const/4 v3, 0x1

    invoke-virtual {v2, v3}, Landroid/app/Notification$Builder;->setOngoing(Z)Landroid/app/Notification$Builder;

    move-result-object v2

    .line 205
    invoke-virtual {v2, v1}, Landroid/app/Notification$Builder;->setSmallIcon(Landroid/graphics/drawable/Icon;)Landroid/app/Notification$Builder;

    move-result-object v1

    .line 206
    invoke-virtual {v1, v0}, Landroid/app/Notification$Builder;->addAction(Landroid/app/Notification$Action;)Landroid/app/Notification$Builder;

    move-result-object v0

    .line 207
    invoke-virtual {v0}, Landroid/app/Notification$Builder;->build()Landroid/app/Notification;

    move-result-object v0

    .line 208
    invoke-virtual {p0, v3, v0}, Lme/embodied/services/ServiceLauncher;->startForeground(ILandroid/app/Notification;)V

    .line 209
    return-void
.end method


# virtual methods
.method makeAlarmIntent(Ljava/lang/String;)Landroid/app/PendingIntent;
    .locals 1

    .line 269
    new-instance v0, Landroid/content/Intent;

    invoke-direct {v0}, Landroid/content/Intent;-><init>()V

    .line 270
    invoke-virtual {v0, p1}, Landroid/content/Intent;->setAction(Ljava/lang/String;)Landroid/content/Intent;

    .line 271
    const/4 p1, 0x0

    invoke-static {p0, p1, v0, p1}, Landroid/app/PendingIntent;->getBroadcast(Landroid/content/Context;ILandroid/content/Intent;I)Landroid/app/PendingIntent;

    move-result-object p1

    return-object p1
.end method

.method public makeUserAlarmIntent(I)Landroid/app/PendingIntent;
    .locals 3

    .line 260
    new-instance v0, Landroid/content/Intent;

    invoke-direct {v0}, Landroid/content/Intent;-><init>()V

    .line 261
    const-string v1, "com.embodied.useralarm"

    invoke-virtual {v0, v1}, Landroid/content/Intent;->setAction(Ljava/lang/String;)Landroid/content/Intent;

    .line 263
    invoke-static {p1}, Ljava/lang/Integer;->toString(I)Ljava/lang/String;

    move-result-object p1

    const-string v1, "ebalarm"

    const/4 v2, 0x0

    invoke-static {v1, p1, v2}, Landroid/net/Uri;->fromParts(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Landroid/net/Uri;

    move-result-object p1

    invoke-virtual {v0, p1}, Landroid/content/Intent;->setData(Landroid/net/Uri;)Landroid/content/Intent;

    .line 264
    const/4 p1, 0x0

    invoke-static {p0, p1, v0, p1}, Landroid/app/PendingIntent;->getBroadcast(Landroid/content/Context;ILandroid/content/Intent;I)Landroid/app/PendingIntent;

    move-result-object p1

    return-object p1
.end method

.method public onBind(Landroid/content/Intent;)Landroid/os/IBinder;
    .locals 0

    .line 167
    const/4 p1, 0x0

    return-object p1
.end method

.method public onCreate()V
    .locals 3

    .line 53
    invoke-super {p0}, Landroid/app/Service;->onCreate()V

    .line 55
    invoke-direct {p0}, Lme/embodied/services/ServiceLauncher;->createNotificationChannel()V

    .line 57
    new-instance v0, Landroid/content/IntentFilter;

    const-string v1, "ACTION_EMBODIED_SHUTDOWN_LAUNCHER"

    invoke-direct {v0, v1}, Landroid/content/IntentFilter;-><init>(Ljava/lang/String;)V

    .line 58
    const-string v1, "com.embodied.wakeword_detect"

    invoke-virtual {v0, v1}, Landroid/content/IntentFilter;->addAction(Ljava/lang/String;)V

    .line 59
    const-string v1, "com.embodied.service_report"

    invoke-virtual {v0, v1}, Landroid/content/IntentFilter;->addAction(Ljava/lang/String;)V

    .line 60
    const-string v1, "com.embodied.heartbeat"

    invoke-virtual {v0, v1}, Landroid/content/IntentFilter;->addAction(Ljava/lang/String;)V

    .line 61
    const-string v2, "com.embodied.osctrl.ota_status"

    invoke-virtual {v0, v2}, Landroid/content/IntentFilter;->addAction(Ljava/lang/String;)V

    .line 63
    iget-object v2, p0, Lme/embodied/services/ServiceLauncher;->intentReceiver:Landroid/content/BroadcastReceiver;

    invoke-virtual {p0, v2, v0}, Lme/embodied/services/ServiceLauncher;->registerReceiver(Landroid/content/BroadcastReceiver;Landroid/content/IntentFilter;)Landroid/content/Intent;

    .line 65
    new-instance v0, Landroid/content/IntentFilter;

    invoke-direct {v0}, Landroid/content/IntentFilter;-><init>()V

    .line 66
    const-string v2, "com.embodied.ota_check_complete"

    invoke-virtual {v0, v2}, Landroid/content/IntentFilter;->addAction(Ljava/lang/String;)V

    .line 67
    iget-object v2, p0, Lme/embodied/services/ServiceLauncher;->otaCheckReceiver:Landroid/content/BroadcastReceiver;

    invoke-virtual {p0, v2, v0}, Lme/embodied/services/ServiceLauncher;->registerReceiver(Landroid/content/BroadcastReceiver;Landroid/content/IntentFilter;)Landroid/content/Intent;

    .line 69
    new-instance v0, Landroid/content/IntentFilter;

    invoke-direct {v0}, Landroid/content/IntentFilter;-><init>()V

    .line 70
    const-string v2, "com.embodied.useralarm"

    invoke-virtual {v0, v2}, Landroid/content/IntentFilter;->addAction(Ljava/lang/String;)V

    .line 71
    const-string v2, "ebalarm"

    invoke-virtual {v0, v2}, Landroid/content/IntentFilter;->addDataScheme(Ljava/lang/String;)V

    .line 72
    iget-object v2, p0, Lme/embodied/services/ServiceLauncher;->userAlarmReceiver:Landroid/content/BroadcastReceiver;

    invoke-virtual {p0, v2, v0}, Lme/embodied/services/ServiceLauncher;->registerReceiver(Landroid/content/BroadcastReceiver;Landroid/content/IntentFilter;)Landroid/content/Intent;

    .line 75
    sget-object v0, Lme/embodied/services/ServiceLauncher;->REBOOT_CLEAR_FILE:Ljava/io/File;

    invoke-virtual {v0}, Ljava/io/File;->delete()Z

    .line 77
    new-instance v0, Lme/embodied/services/Launcher;

    invoke-virtual {p0, v1}, Lme/embodied/services/ServiceLauncher;->makeAlarmIntent(Ljava/lang/String;)Landroid/app/PendingIntent;

    move-result-object v1

    invoke-direct {v0, p0, v1}, Lme/embodied/services/Launcher;-><init>(Lme/embodied/services/ServiceLauncher;Landroid/app/PendingIntent;)V

    iput-object v0, p0, Lme/embodied/services/ServiceLauncher;->launcher_:Lme/embodied/services/Launcher;

    .line 78
    return-void
.end method

.method public onDestroy()V
    .locals 1

    .line 158
    iget-object v0, p0, Lme/embodied/services/ServiceLauncher;->intentReceiver:Landroid/content/BroadcastReceiver;

    invoke-virtual {p0, v0}, Lme/embodied/services/ServiceLauncher;->unregisterReceiver(Landroid/content/BroadcastReceiver;)V

    .line 159
    iget-object v0, p0, Lme/embodied/services/ServiceLauncher;->launcher_:Lme/embodied/services/Launcher;

    if-eqz v0, :cond_0

    .line 160
    invoke-virtual {v0}, Lme/embodied/services/Launcher;->Stop()V

    .line 162
    :cond_0
    invoke-super {p0}, Landroid/app/Service;->onDestroy()V

    .line 163
    return-void
.end method

.method public onStartCommand(Landroid/content/Intent;II)I
    .locals 5

    .line 82
    invoke-direct {p0}, Lme/embodied/services/ServiceLauncher;->startForeground()V

    .line 85
    iget-boolean p2, p0, Lme/embodied/services/ServiceLauncher;->init_needed_:Z

    if-eqz p2, :cond_0

    .line 88
    invoke-static {}, Lme/embodied/KeyMaker;->provisionKeysCheck()V

    .line 91
    invoke-virtual {p0}, Lme/embodied/services/ServiceLauncher;->getContentResolver()Landroid/content/ContentResolver;

    move-result-object p2

    invoke-static {p2}, Lme/embodied/RebootCause;->updateValidity(Landroid/content/ContentResolver;)V

    .line 95
    :cond_0
    const-string p2, "bo-launcher-s"

    const-string p3, "Ensure Wifi is enabled."

    invoke-static {p2, p3}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 96
    const/4 p3, 0x1

    invoke-static {p0, p3}, Lme/embodied/NetworkUtil;->enableWifi(Landroid/content/Context;Z)V

    .line 98
    const-string v0, "utest"

    if-eqz p1, :cond_2

    .line 99
    invoke-virtual {p1, v0}, Landroid/content/Intent;->hasExtra(Ljava/lang/String;)Z

    move-result v1

    if-nez v1, :cond_2

    .line 100
    iget-boolean v1, p0, Lme/embodied/services/ServiceLauncher;->xmos_update_started_:Z

    if-eqz v1, :cond_1

    .line 101
    const-string v1, "XMOSDFU already started."

    invoke-static {p2, v1}, Landroid/util/Log;->v(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_0

    .line 103
    :cond_1
    iput-boolean p3, p0, Lme/embodied/services/ServiceLauncher;->xmos_update_started_:Z

    .line 104
    const-string v1, "Starting XMOUSDFU"

    invoke-static {p2, v1}, Landroid/util/Log;->v(Ljava/lang/String;Ljava/lang/String;)I

    .line 105
    new-instance v1, Landroid/content/Intent;

    invoke-direct {v1}, Landroid/content/Intent;-><init>()V

    .line 106
    new-instance v2, Landroid/content/ComponentName;

    const-class v3, Lme/embodied/services/XMOSDFU;

    invoke-virtual {v3}, Ljava/lang/Class;->getCanonicalName()Ljava/lang/String;

    move-result-object v3

    const-string v4, "com.embodied.bo_unity"

    invoke-direct {v2, v4, v3}, Landroid/content/ComponentName;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    invoke-virtual {v1, v2}, Landroid/content/Intent;->setComponent(Landroid/content/ComponentName;)Landroid/content/Intent;

    .line 107
    invoke-virtual {p0, v1}, Lme/embodied/services/ServiceLauncher;->startService(Landroid/content/Intent;)Landroid/content/ComponentName;

    .line 112
    :cond_2
    :goto_0
    if-nez p1, :cond_3

    .line 113
    const-string p1, "Restart."

    invoke-static {p2, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 114
    iget-object p1, p0, Lme/embodied/services/ServiceLauncher;->launcher_:Lme/embodied/services/Launcher;

    invoke-virtual {p1}, Lme/embodied/services/Launcher;->StartConfigActivity()V

    goto :goto_1

    .line 116
    :cond_3
    const-string v1, "unity"

    invoke-virtual {p1, v1}, Landroid/content/Intent;->hasExtra(Ljava/lang/String;)Z

    move-result v1

    if-eqz v1, :cond_5

    .line 117
    const-string p1, "Start triggered from Unity."

    invoke-static {p2, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 118
    iget-boolean p1, p0, Lme/embodied/services/ServiceLauncher;->xmos_ready_:Z

    if-nez p1, :cond_4

    .line 119
    iput-boolean p3, p0, Lme/embodied/services/ServiceLauncher;->xmos_run_on_complete_:Z

    .line 120
    const-string p1, "XMOS is not ready, deferring launching services."

    invoke-static {p2, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_1

    .line 122
    :cond_4
    const-string p1, "XMOS is ready, launching services."

    invoke-static {p2, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 123
    iget-object p1, p0, Lme/embodied/services/ServiceLauncher;->launcher_:Lme/embodied/services/Launcher;

    invoke-virtual {p1}, Lme/embodied/services/Launcher;->LaunchServices()V

    goto :goto_1

    .line 125
    :cond_5
    invoke-virtual {p1, v0}, Landroid/content/Intent;->hasExtra(Ljava/lang/String;)Z

    move-result v1

    if-eqz v1, :cond_6

    .line 126
    const-string v1, "Starting UNIT TEST"

    invoke-static {p2, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 127
    iget-object p2, p0, Lme/embodied/services/ServiceLauncher;->launcher_:Lme/embodied/services/Launcher;

    invoke-virtual {p1, v0}, Landroid/content/Intent;->getStringExtra(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Lme/embodied/services/Launcher;->UnitTestServices(Ljava/lang/String;)V

    goto :goto_1

    .line 129
    :cond_6
    const-string p1, "Start."

    invoke-static {p2, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 130
    iget-object p1, p0, Lme/embodied/services/ServiceLauncher;->launcher_:Lme/embodied/services/Launcher;

    invoke-virtual {p1}, Lme/embodied/services/Launcher;->StartConfigActivity()V

    .line 134
    :goto_1
    const/4 p1, 0x0

    iput-boolean p1, p0, Lme/embodied/services/ServiceLauncher;->init_needed_:Z

    .line 135
    return p3
.end method
