.class final enum Lme/embodied/services/Launcher$FeatureFlags;
.super Ljava/lang/Enum;
.source "Launcher.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lme/embodied/services/Launcher;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4018
    name = "FeatureFlags"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lme/embodied/services/Launcher$FeatureFlags;",
        ">;"
    }
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lme/embodied/services/Launcher$FeatureFlags;

.field public static final enum FEA_ALWAYS_ONLINE:Lme/embodied/services/Launcher$FeatureFlags;

.field public static final enum FEA_ANALYTICS:Lme/embodied/services/Launcher$FeatureFlags;

.field public static final enum FEA_RESUME_WAIT_NET:Lme/embodied/services/Launcher$FeatureFlags;

.field public static final enum FEA_XMOS_WATCHDOG:Lme/embodied/services/Launcher$FeatureFlags;


# direct methods
.method static constructor <clinit>()V
    .locals 7

    .line 107
    new-instance v0, Lme/embodied/services/Launcher$FeatureFlags;

    const-string v1, "FEA_ALWAYS_ONLINE"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lme/embodied/services/Launcher$FeatureFlags;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lme/embodied/services/Launcher$FeatureFlags;->FEA_ALWAYS_ONLINE:Lme/embodied/services/Launcher$FeatureFlags;

    .line 108
    new-instance v0, Lme/embodied/services/Launcher$FeatureFlags;

    const-string v1, "FEA_ANALYTICS"

    const/4 v3, 0x1

    invoke-direct {v0, v1, v3}, Lme/embodied/services/Launcher$FeatureFlags;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lme/embodied/services/Launcher$FeatureFlags;->FEA_ANALYTICS:Lme/embodied/services/Launcher$FeatureFlags;

    .line 109
    new-instance v0, Lme/embodied/services/Launcher$FeatureFlags;

    const-string v1, "FEA_XMOS_WATCHDOG"

    const/4 v4, 0x2

    invoke-direct {v0, v1, v4}, Lme/embodied/services/Launcher$FeatureFlags;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lme/embodied/services/Launcher$FeatureFlags;->FEA_XMOS_WATCHDOG:Lme/embodied/services/Launcher$FeatureFlags;

    .line 110
    new-instance v0, Lme/embodied/services/Launcher$FeatureFlags;

    const-string v1, "FEA_RESUME_WAIT_NET"

    const/4 v5, 0x3

    invoke-direct {v0, v1, v5}, Lme/embodied/services/Launcher$FeatureFlags;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lme/embodied/services/Launcher$FeatureFlags;->FEA_RESUME_WAIT_NET:Lme/embodied/services/Launcher$FeatureFlags;

    .line 106
    const/4 v1, 0x4

    new-array v1, v1, [Lme/embodied/services/Launcher$FeatureFlags;

    sget-object v6, Lme/embodied/services/Launcher$FeatureFlags;->FEA_ALWAYS_ONLINE:Lme/embodied/services/Launcher$FeatureFlags;

    aput-object v6, v1, v2

    sget-object v2, Lme/embodied/services/Launcher$FeatureFlags;->FEA_ANALYTICS:Lme/embodied/services/Launcher$FeatureFlags;

    aput-object v2, v1, v3

    sget-object v2, Lme/embodied/services/Launcher$FeatureFlags;->FEA_XMOS_WATCHDOG:Lme/embodied/services/Launcher$FeatureFlags;

    aput-object v2, v1, v4

    aput-object v0, v1, v5

    sput-object v1, Lme/embodied/services/Launcher$FeatureFlags;->$VALUES:[Lme/embodied/services/Launcher$FeatureFlags;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 106
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lme/embodied/services/Launcher$FeatureFlags;
    .locals 1

    .line 106
    const-class v0, Lme/embodied/services/Launcher$FeatureFlags;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lme/embodied/services/Launcher$FeatureFlags;

    return-object p0
.end method

.method public static values()[Lme/embodied/services/Launcher$FeatureFlags;
    .locals 1

    .line 106
    sget-object v0, Lme/embodied/services/Launcher$FeatureFlags;->$VALUES:[Lme/embodied/services/Launcher$FeatureFlags;

    invoke-virtual {v0}, [Lme/embodied/services/Launcher$FeatureFlags;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lme/embodied/services/Launcher$FeatureFlags;

    return-object v0
.end method
