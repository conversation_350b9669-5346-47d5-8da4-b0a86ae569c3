.class Lme/embodied/services/Launcher$LightSleepFilter;
.super Ljava/lang/Object;
.source "Launcher.java"

# interfaces
.implements Lme/embodied/services/Launcher$ComponentFilter;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lme/embodied/services/Launcher;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x8
    name = "LightSleepFilter"
.end annotation


# direct methods
.method constructor <init>()V
    .locals 0

    .line 299
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public ComponentAllowed(Lme/embodied/services/Launcher$BOComponent;Z)Z
    .locals 2

    .line 302
    sget-object v0, Lme/embodied/services/Launcher$BOComponent;->BO_ANALYTICS:Lme/embodied/services/Launcher$BOComponent;

    const/4 v1, 0x0

    if-ne p1, v0, :cond_0

    sget-object v0, Lme/embodied/services/Launcher$FeatureFlags;->FEA_ANALYTICS:Lme/embodied/services/Launcher$FeatureFlags;

    invoke-static {v0}, Lme/embodied/services/Launcher;->access$000(Lme/embodied/services/Launcher$FeatureFlags;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 304
    return v1

    .line 306
    :cond_0
    sget-object v0, Lme/embodied/services/Launcher$BOComponent;->BO_XMOS_WD:Lme/embodied/services/Launcher$BOComponent;

    if-ne p1, v0, :cond_1

    sget-object p1, Lme/embodied/services/Launcher$FeatureFlags;->FEA_XMOS_WATCHDOG:Lme/embodied/services/Launcher$FeatureFlags;

    invoke-static {p1}, Lme/embodied/services/Launcher;->access$000(Lme/embodied/services/Launcher$FeatureFlags;)Z

    move-result p1

    if-nez p1, :cond_1

    .line 308
    return v1

    .line 310
    :cond_1
    return p2
.end method
