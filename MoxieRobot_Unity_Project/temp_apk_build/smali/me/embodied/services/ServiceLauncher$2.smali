.class Lme/embodied/services/ServiceLauncher$2;
.super Landroid/content/BroadcastReceiver;
.source "ServiceLauncher.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lme/embodied/services/ServiceLauncher;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lme/embodied/services/ServiceLauncher;


# direct methods
.method constructor <init>(Lme/embodied/services/ServiceLauncher;)V
    .locals 0

    .line 212
    iput-object p1, p0, Lme/embodied/services/ServiceLauncher$2;->this$0:Lme/embodied/services/ServiceLauncher;

    invoke-direct {p0}, Landroid/content/BroadcastReceiver;-><init>()V

    return-void
.end method


# virtual methods
.method public onReceive(Landroid/content/Context;Landroid/content/Intent;)V
    .locals 6

    .line 215
    invoke-virtual {p2}, Landroid/content/Intent;->getAction()Ljava/lang/String;

    move-result-object v0

    const-string v1, "ACTION_EMBODIED_SHUTDOWN_LAUNCHER"

    invoke-virtual {v0, v1}, Ljava/lang/String;->equalsIgnoreCase(Ljava/lang/String;)Z

    move-result v0

    const-string v1, "bo-launcher-s"

    if-eqz v0, :cond_0

    .line 216
    const-string p2, "Received shutdown request from action."

    invoke-static {v1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 217
    new-instance p2, Landroid/content/Intent;

    const-class v0, Lme/embodied/services/ServiceLauncher;

    invoke-direct {p2, p1, v0}, Landroid/content/Intent;-><init>(Landroid/content/Context;Ljava/lang/Class;)V

    invoke-virtual {p1, p2}, Landroid/content/Context;->stopService(Landroid/content/Intent;)Z

    goto/16 :goto_2

    .line 218
    :cond_0
    invoke-virtual {p2}, Landroid/content/Intent;->getAction()Ljava/lang/String;

    move-result-object p1

    const-string v0, "com.embodied.wakeword_detect"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equalsIgnoreCase(Ljava/lang/String;)Z

    move-result p1

    const/4 v0, 0x1

    if-eqz p1, :cond_1

    .line 219
    const-string p1, "Received WAKE TRIGGER"

    invoke-static {v1, p1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 220
    iget-object p1, p0, Lme/embodied/services/ServiceLauncher$2;->this$0:Lme/embodied/services/ServiceLauncher;

    invoke-static {p1}, Lme/embodied/services/ServiceLauncher;->access$300(Lme/embodied/services/ServiceLauncher;)Lme/embodied/services/Launcher;

    move-result-object p1

    invoke-virtual {p1, v0}, Lme/embodied/services/Launcher;->OnWakeSignal(Z)V

    goto :goto_2

    .line 221
    :cond_1
    invoke-virtual {p2}, Landroid/content/Intent;->getAction()Ljava/lang/String;

    move-result-object p1

    const-string v1, "com.embodied.service_report"

    invoke-virtual {p1, v1}, Ljava/lang/String;->equalsIgnoreCase(Ljava/lang/String;)Z

    move-result p1

    if-eqz p1, :cond_2

    .line 222
    const-string p1, "service"

    invoke-virtual {p2, p1}, Landroid/content/Intent;->getStringExtra(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    .line 223
    iget-object p2, p0, Lme/embodied/services/ServiceLauncher$2;->this$0:Lme/embodied/services/ServiceLauncher;

    invoke-static {p2}, Lme/embodied/services/ServiceLauncher;->access$300(Lme/embodied/services/ServiceLauncher;)Lme/embodied/services/Launcher;

    move-result-object p2

    invoke-virtual {p2, p1}, Lme/embodied/services/Launcher;->OnServiceStartup(Ljava/lang/String;)V

    .line 224
    goto :goto_2

    :cond_2
    invoke-virtual {p2}, Landroid/content/Intent;->getAction()Ljava/lang/String;

    move-result-object p1

    const-string v1, "com.embodied.heartbeat"

    invoke-virtual {p1, v1}, Ljava/lang/String;->equalsIgnoreCase(Ljava/lang/String;)Z

    move-result p1

    if-eqz p1, :cond_3

    .line 225
    iget-object p1, p0, Lme/embodied/services/ServiceLauncher$2;->this$0:Lme/embodied/services/ServiceLauncher;

    invoke-static {p1}, Lme/embodied/services/ServiceLauncher;->access$300(Lme/embodied/services/ServiceLauncher;)Lme/embodied/services/Launcher;

    move-result-object p1

    invoke-virtual {p1}, Lme/embodied/services/Launcher;->OnAlarmPing()V

    goto :goto_2

    .line 226
    :cond_3
    invoke-virtual {p2}, Landroid/content/Intent;->getAction()Ljava/lang/String;

    move-result-object p1

    const-string v1, "com.embodied.osctrl.ota_status"

    invoke-virtual {p1, v1}, Ljava/lang/String;->equalsIgnoreCase(Ljava/lang/String;)Z

    move-result p1

    if-eqz p1, :cond_6

    .line 229
    const-string p1, "payload_complete"

    const/high16 v1, -0x80000000

    invoke-virtual {p2, p1, v1}, Landroid/content/Intent;->getIntExtra(Ljava/lang/String;I)I

    move-result p1

    .line 230
    const/4 v2, 0x0

    if-eq p1, v1, :cond_4

    goto :goto_0

    :cond_4
    move v0, v2

    .line 231
    :goto_0
    iget-object v1, p0, Lme/embodied/services/ServiceLauncher$2;->this$0:Lme/embodied/services/ServiceLauncher;

    invoke-static {v1}, Lme/embodied/services/ServiceLauncher;->access$300(Lme/embodied/services/ServiceLauncher;)Lme/embodied/services/Launcher;

    move-result-object v1

    const-string v3, "update_status"

    invoke-virtual {p2, v3, v2}, Landroid/content/Intent;->getIntExtra(Ljava/lang/String;I)I

    move-result v3

    const/4 v4, 0x0

    .line 232
    const-string v5, "update_percent"

    invoke-virtual {p2, v5, v4}, Landroid/content/Intent;->getFloatExtra(Ljava/lang/String;F)F

    move-result p2

    if-eqz v0, :cond_5

    goto :goto_1

    :cond_5
    move p1, v2

    .line 231
    :goto_1
    invoke-virtual {v1, v3, p2, v0, p1}, Lme/embodied/services/Launcher;->OnOTAStatus(IFZI)V

    .line 235
    :cond_6
    :goto_2
    return-void
.end method
