.class Lme/embodied/services/Launcher$SuspendFilter;
.super Ljava/lang/Object;
.source "Launcher.java"

# interfaces
.implements Lme/embodied/services/Launcher$ComponentFilter;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lme/embodied/services/Launcher;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x8
    name = "SuspendFilter"
.end annotation


# direct methods
.method constructor <init>()V
    .locals 0

    .line 282
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public ComponentAllowed(Lme/embodied/services/Launcher$BOComponent;Z)Z
    .locals 3

    .line 285
    sget-object v0, Lme/embodied/services/Launcher$FeatureFlags;->FEA_ALWAYS_ONLINE:Lme/embodied/services/Launcher$FeatureFlags;

    invoke-static {v0}, Lme/embodied/services/Launcher;->access$000(Lme/embodied/services/Launcher$FeatureFlags;)Z

    move-result v0

    .line 286
    sget-object v1, Lme/embodied/services/Launcher$BOComponent;->BO_LOGGER:Lme/embodied/services/Launcher$BOComponent;

    const/4 v2, 0x1

    if-ne p1, v1, :cond_0

    if-eqz v0, :cond_0

    .line 288
    return v2

    .line 290
    :cond_0
    sget-object v1, Lme/embodied/services/Launcher$BOComponent;->BO_XMOS_WD:Lme/embodied/services/Launcher$BOComponent;

    if-ne p1, v1, :cond_1

    if-eqz v0, :cond_1

    sget-object p1, Lme/embodied/services/Launcher$FeatureFlags;->FEA_XMOS_WATCHDOG:Lme/embodied/services/Launcher$FeatureFlags;

    invoke-static {p1}, Lme/embodied/services/Launcher;->access$000(Lme/embodied/services/Launcher$FeatureFlags;)Z

    move-result p1

    if-eqz p1, :cond_1

    .line 293
    return v2

    .line 295
    :cond_1
    return p2
.end method
