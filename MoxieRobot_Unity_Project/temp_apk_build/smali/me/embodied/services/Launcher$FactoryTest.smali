.class final enum Lme/embodied/services/Launcher$FactoryTest;
.super Ljava/lang/Enum;
.source "Launcher.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lme/embodied/services/Launcher;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4018
    name = "FactoryTest"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lme/embodied/services/Launcher$FactoryTest;",
        ">;"
    }
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lme/embodied/services/Launcher$FactoryTest;

.field public static final enum BURN_IN:Lme/embodied/services/Launcher$FactoryTest;

.field public static final enum DEMO_MODE:Lme/embodied/services/Launcher$FactoryTest;

.field public static final enum FINAL_TEST:Lme/embodied/services/Launcher$FactoryTest;

.field public static final enum INTERNAL_ASSEMBLY:Lme/embodied/services/Launcher$FactoryTest;

.field public static final enum LIFE_TEST:Lme/embodied/services/Launcher$FactoryTest;

.field public static final enum TUMMY_BACK:Lme/embodied/services/Launcher$FactoryTest;


# instance fields
.field component_:Landroid/content/ComponentName;


# direct methods
.method static constructor <clinit>()V
    .locals 10

    .line 252
    new-instance v0, Lme/embodied/services/Launcher$FactoryTest;

    const-string v1, "BURN_IN"

    const/4 v2, 0x0

    const-string v3, "me.embodied.productiontesting.burnintest"

    const-string v4, "me.embodied.productiontesting.burnintest.ActivityBurnInTest"

    invoke-direct {v0, v1, v2, v3, v4}, Lme/embodied/services/Launcher$FactoryTest;-><init>(Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;)V

    sput-object v0, Lme/embodied/services/Launcher$FactoryTest;->BURN_IN:Lme/embodied/services/Launcher$FactoryTest;

    .line 253
    new-instance v0, Lme/embodied/services/Launcher$FactoryTest;

    const-string v1, "TUMMY_BACK"

    const/4 v3, 0x1

    const-string v4, "me.embodied.productiontesting.qc"

    const-string v5, "me.embodied.productiontesting.qc.ActivityQC"

    invoke-direct {v0, v1, v3, v4, v5}, Lme/embodied/services/Launcher$FactoryTest;-><init>(Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;)V

    sput-object v0, Lme/embodied/services/Launcher$FactoryTest;->TUMMY_BACK:Lme/embodied/services/Launcher$FactoryTest;

    .line 254
    new-instance v0, Lme/embodied/services/Launcher$FactoryTest;

    const-string v1, "DEMO_MODE"

    const/4 v4, 0x2

    const-string v5, ""

    invoke-direct {v0, v1, v4, v5, v5}, Lme/embodied/services/Launcher$FactoryTest;-><init>(Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;)V

    sput-object v0, Lme/embodied/services/Launcher$FactoryTest;->DEMO_MODE:Lme/embodied/services/Launcher$FactoryTest;

    .line 255
    new-instance v0, Lme/embodied/services/Launcher$FactoryTest;

    const-string v1, "LIFE_TEST"

    const/4 v5, 0x3

    const-string v6, "me.embodied.productiontesting.lifetest"

    const-string v7, "me.embodied.productiontesting.lifetest.ActivityLifeTest"

    invoke-direct {v0, v1, v5, v6, v7}, Lme/embodied/services/Launcher$FactoryTest;-><init>(Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;)V

    sput-object v0, Lme/embodied/services/Launcher$FactoryTest;->LIFE_TEST:Lme/embodied/services/Launcher$FactoryTest;

    .line 256
    new-instance v0, Lme/embodied/services/Launcher$FactoryTest;

    const-string v1, "INTERNAL_ASSEMBLY"

    const/4 v6, 0x4

    const-string v7, "me.embodied.productiontesting.internalassytest"

    const-string v8, "me.embodied.productiontesting.internalassytest.ActivityInternalAssyTest"

    invoke-direct {v0, v1, v6, v7, v8}, Lme/embodied/services/Launcher$FactoryTest;-><init>(Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;)V

    sput-object v0, Lme/embodied/services/Launcher$FactoryTest;->INTERNAL_ASSEMBLY:Lme/embodied/services/Launcher$FactoryTest;

    .line 257
    new-instance v0, Lme/embodied/services/Launcher$FactoryTest;

    const-string v1, "FINAL_TEST"

    const/4 v7, 0x5

    const-string v8, "me.embodied.productiontesting.finaltest"

    const-string v9, "me.embodied.productiontesting.finaltest.ActivityFinalTest"

    invoke-direct {v0, v1, v7, v8, v9}, Lme/embodied/services/Launcher$FactoryTest;-><init>(Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;)V

    sput-object v0, Lme/embodied/services/Launcher$FactoryTest;->FINAL_TEST:Lme/embodied/services/Launcher$FactoryTest;

    .line 251
    const/4 v1, 0x6

    new-array v1, v1, [Lme/embodied/services/Launcher$FactoryTest;

    sget-object v8, Lme/embodied/services/Launcher$FactoryTest;->BURN_IN:Lme/embodied/services/Launcher$FactoryTest;

    aput-object v8, v1, v2

    sget-object v2, Lme/embodied/services/Launcher$FactoryTest;->TUMMY_BACK:Lme/embodied/services/Launcher$FactoryTest;

    aput-object v2, v1, v3

    sget-object v2, Lme/embodied/services/Launcher$FactoryTest;->DEMO_MODE:Lme/embodied/services/Launcher$FactoryTest;

    aput-object v2, v1, v4

    sget-object v2, Lme/embodied/services/Launcher$FactoryTest;->LIFE_TEST:Lme/embodied/services/Launcher$FactoryTest;

    aput-object v2, v1, v5

    sget-object v2, Lme/embodied/services/Launcher$FactoryTest;->INTERNAL_ASSEMBLY:Lme/embodied/services/Launcher$FactoryTest;

    aput-object v2, v1, v6

    aput-object v0, v1, v7

    sput-object v1, Lme/embodied/services/Launcher$FactoryTest;->$VALUES:[Lme/embodied/services/Launcher$FactoryTest;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    .line 260
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 261
    new-instance p1, Landroid/content/ComponentName;

    invoke-direct {p1, p3, p4}, Landroid/content/ComponentName;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    iput-object p1, p0, Lme/embodied/services/Launcher$FactoryTest;->component_:Landroid/content/ComponentName;

    .line 262
    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lme/embodied/services/Launcher$FactoryTest;
    .locals 1

    .line 251
    const-class v0, Lme/embodied/services/Launcher$FactoryTest;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lme/embodied/services/Launcher$FactoryTest;

    return-object p0
.end method

.method public static values()[Lme/embodied/services/Launcher$FactoryTest;
    .locals 1

    .line 251
    sget-object v0, Lme/embodied/services/Launcher$FactoryTest;->$VALUES:[Lme/embodied/services/Launcher$FactoryTest;

    invoke-virtual {v0}, [Lme/embodied/services/Launcher$FactoryTest;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lme/embodied/services/Launcher$FactoryTest;

    return-object v0
.end method


# virtual methods
.method public getComponent()Landroid/content/ComponentName;
    .locals 1

    .line 264
    iget-object v0, p0, Lme/embodied/services/Launcher$FactoryTest;->component_:Landroid/content/ComponentName;

    return-object v0
.end method
