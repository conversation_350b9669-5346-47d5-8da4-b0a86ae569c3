.class Lme/embodied/services/Launcher$ServiceMonitor;
.super Ljava/lang/Thread;
.source "Launcher.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lme/embodied/services/Launcher;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x2
    name = "ServiceMonitor"
.end annotation


# instance fields
.field context_:Landroid/content/Context;

.field running_:Z

.field final synthetic this$0:Lme/embodied/services/Launcher;


# direct methods
.method constructor <init>(Lme/embodied/services/Launcher;Landroid/content/Context;)V
    .locals 0

    .line 2135
    iput-object p1, p0, Lme/embodied/services/Launcher$ServiceMonitor;->this$0:Lme/embodied/services/Launcher;

    .line 2136
    invoke-direct {p0}, Ljava/lang/Thread;-><init>()V

    .line 2131
    const/4 p1, 0x0

    iput-boolean p1, p0, Lme/embodied/services/Launcher$ServiceMonitor;->running_:Z

    .line 2137
    iput-object p2, p0, Lme/embodied/services/Launcher$ServiceMonitor;->context_:Landroid/content/Context;

    .line 2138
    return-void
.end method

.method private IsMyActivityRunning(Landroid/content/ComponentName;)Z
    .locals 4

    .line 2152
    sget-object v0, Lme/embodied/services/Launcher$BOComponent;->BO_CFGAPP:Lme/embodied/services/Launcher$BOComponent;

    invoke-virtual {v0}, Lme/embodied/services/Launcher$BOComponent;->getComponentName()Landroid/content/ComponentName;

    move-result-object v0

    if-ne p1, v0, :cond_0

    .line 2153
    iget-object p1, p0, Lme/embodied/services/Launcher$ServiceMonitor;->this$0:Lme/embodied/services/Launcher;

    invoke-static {p1}, Lme/embodied/services/Launcher;->access$1400(Lme/embodied/services/Launcher;)Z

    move-result p1

    return p1

    .line 2156
    :cond_0
    sget-object v0, Lme/embodied/services/Launcher$BOComponent;->BO_MAINAPP:Lme/embodied/services/Launcher$BOComponent;

    invoke-virtual {v0}, Lme/embodied/services/Launcher$BOComponent;->getComponentName()Landroid/content/ComponentName;

    move-result-object v0

    const/4 v1, 0x1

    if-ne p1, v0, :cond_1

    iget-object v0, p0, Lme/embodied/services/Launcher$ServiceMonitor;->this$0:Lme/embodied/services/Launcher;

    invoke-static {v0}, Lme/embodied/services/Launcher;->access$1500(Lme/embodied/services/Launcher;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 2158
    return v1

    .line 2162
    :cond_1
    iget-object v0, p0, Lme/embodied/services/Launcher$ServiceMonitor;->context_:Landroid/content/Context;

    const-string v2, "activity"

    invoke-virtual {v0, v2}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/app/ActivityManager;

    .line 2163
    invoke-virtual {v0}, Landroid/app/ActivityManager;->getAppTasks()Ljava/util/List;

    move-result-object v0

    .line 2164
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_3

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Landroid/app/ActivityManager$AppTask;

    .line 2166
    invoke-virtual {v2}, Landroid/app/ActivityManager$AppTask;->getTaskInfo()Landroid/app/ActivityManager$RecentTaskInfo;

    move-result-object v2

    iget-object v2, v2, Landroid/app/ActivityManager$RecentTaskInfo;->topActivity:Landroid/content/ComponentName;

    .line 2167
    if-eqz v2, :cond_2

    invoke-virtual {p1}, Landroid/content/ComponentName;->flattenToString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2}, Landroid/content/ComponentName;->flattenToString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v3, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_2

    .line 2168
    return v1

    .line 2170
    :cond_2
    goto :goto_0

    .line 2171
    :cond_3
    const/4 p1, 0x0

    return p1
.end method

.method private IsMyServiceRunning(Landroid/content/ComponentName;)Z
    .locals 4

    .line 2141
    iget-object v0, p0, Lme/embodied/services/Launcher$ServiceMonitor;->context_:Landroid/content/Context;

    const-string v1, "activity"

    invoke-virtual {v0, v1}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/app/ActivityManager;

    .line 2142
    const v1, 0x7fffffff

    invoke-virtual {v0, v1}, Landroid/app/ActivityManager;->getRunningServices(I)Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroid/app/ActivityManager$RunningServiceInfo;

    .line 2143
    invoke-virtual {p1}, Landroid/content/ComponentName;->getClassName()Ljava/lang/String;

    move-result-object v2

    iget-object v3, v1, Landroid/app/ActivityManager$RunningServiceInfo;->service:Landroid/content/ComponentName;

    invoke-virtual {v3}, Landroid/content/ComponentName;->getClassName()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-virtual {p1}, Landroid/content/ComponentName;->getPackageName()Ljava/lang/String;

    move-result-object v2

    iget-object v1, v1, Landroid/app/ActivityManager$RunningServiceInfo;->service:Landroid/content/ComponentName;

    invoke-virtual {v1}, Landroid/content/ComponentName;->getPackageName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v2, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_0

    .line 2144
    const/4 p1, 0x1

    return p1

    .line 2146
    :cond_0
    goto :goto_0

    .line 2147
    :cond_1
    const/4 p1, 0x0

    return p1
.end method

.method private getFGPackageName()Ljava/lang/String;
    .locals 10

    .line 2178
    const-string v0, ""

    :try_start_0
    iget-object v1, p0, Lme/embodied/services/Launcher$ServiceMonitor;->context_:Landroid/content/Context;

    const-string v2, "usagestats"

    invoke-virtual {v1, v2}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v1

    move-object v2, v1

    check-cast v2, Landroid/app/usage/UsageStatsManager;

    .line 2179
    const-wide/32 v3, 0xea60

    .line 2180
    new-instance v1, Ljava/util/Date;

    invoke-direct {v1}, Ljava/util/Date;-><init>()V

    .line 2181
    const/4 v5, 0x0

    invoke-virtual {v1}, Ljava/util/Date;->getTime()J

    move-result-wide v6

    sub-long/2addr v6, v3

    invoke-virtual {v1}, Ljava/util/Date;->getTime()J

    move-result-wide v8

    move v3, v5

    move-wide v4, v6

    move-wide v6, v8

    invoke-virtual/range {v2 .. v7}, Landroid/app/usage/UsageStatsManager;->queryUsageStats(IJJ)Ljava/util/List;

    move-result-object v1

    .line 2182
    const-wide/16 v2, 0x0

    .line 2183
    nop

    .line 2184
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    move-object v4, v0

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v5

    if-eqz v5, :cond_1

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Landroid/app/usage/UsageStats;

    .line 2185
    invoke-virtual {v5}, Landroid/app/usage/UsageStats;->getLastTimeStamp()J

    move-result-wide v6

    cmp-long v6, v6, v2

    if-lez v6, :cond_0

    .line 2186
    invoke-virtual {v5}, Landroid/app/usage/UsageStats;->getLastTimeStamp()J

    move-result-wide v2

    .line 2187
    invoke-virtual {v5}, Landroid/app/usage/UsageStats;->getPackageName()Ljava/lang/String;

    move-result-object v4
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 2189
    :cond_0
    goto :goto_0

    .line 2190
    :cond_1
    return-object v4

    .line 2191
    :catch_0
    move-exception v1

    .line 2192
    const-string v2, "bo-launcher-j"

    const-string v3, "Failed to get foreground activity package."

    invoke-static {v2, v3}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 2193
    invoke-virtual {v1}, Ljava/lang/Exception;->printStackTrace()V

    .line 2195
    return-object v0
.end method


# virtual methods
.method LogState(Landroid/content/ComponentName;ZLjava/lang/StringBuilder;)V
    .locals 1

    .line 2200
    invoke-virtual {p1}, Landroid/content/ComponentName;->flattenToString()Ljava/lang/String;

    move-result-object v0

    .line 2201
    invoke-static {v0, p2}, Lme/embodied/services/Launcher;->access$1600(Ljava/lang/String;I)V

    .line 2202
    invoke-virtual {p3}, Ljava/lang/StringBuilder;->length()I

    move-result p2

    if-lez p2, :cond_0

    .line 2203
    const-string p2, ","

    invoke-virtual {p3, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 2207
    :cond_0
    invoke-static {}, Lme/embodied/services/Launcher;->access$1700()Landroid/content/ComponentName;

    move-result-object p2

    if-eq p1, p2, :cond_2

    invoke-static {}, Lme/embodied/services/Launcher;->access$1800()Landroid/content/ComponentName;

    move-result-object p2

    if-ne p1, p2, :cond_1

    goto :goto_0

    :cond_1
    invoke-virtual {p1}, Landroid/content/ComponentName;->getClassName()Ljava/lang/String;

    move-result-object p1

    goto :goto_1

    :cond_2
    :goto_0
    invoke-virtual {p1}, Landroid/content/ComponentName;->getPackageName()Ljava/lang/String;

    move-result-object p1

    .line 2208
    :goto_1
    const/16 p2, 0x2e

    invoke-virtual {p1, p2}, Ljava/lang/String;->lastIndexOf(I)I

    move-result p2

    .line 2209
    if-ltz p2, :cond_3

    add-int/lit8 p2, p2, 0x1

    invoke-virtual {p1, p2}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object p1

    :cond_3
    invoke-virtual {p3, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 2210
    return-void
.end method

.method Stop()V
    .locals 1

    .line 2213
    const/4 v0, 0x0

    iput-boolean v0, p0, Lme/embodied/services/Launcher$ServiceMonitor;->running_:Z

    .line 2214
    return-void
.end method

.method public run()V
    .locals 10

    .line 2219
    const-string v0, "bo-launcher-j"

    new-instance v1, Ljava/util/HashSet;

    invoke-direct {v1}, Ljava/util/HashSet;-><init>()V

    .line 2221
    const/4 v2, 0x1

    iput-boolean v2, p0, Lme/embodied/services/Launcher$ServiceMonitor;->running_:Z

    .line 2222
    :goto_0
    iget-boolean v3, p0, Lme/embodied/services/Launcher$ServiceMonitor;->running_:Z

    if-eqz v3, :cond_14

    .line 2224
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    .line 2225
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    .line 2226
    iget-object v5, p0, Lme/embodied/services/Launcher$ServiceMonitor;->this$0:Lme/embodied/services/Launcher;

    invoke-static {v5}, Lme/embodied/services/Launcher;->access$1900(Lme/embodied/services/Launcher;)Ljava/util/Vector;

    move-result-object v5

    invoke-virtual {v5}, Ljava/util/Vector;->listIterator()Ljava/util/ListIterator;

    move-result-object v5

    .line 2228
    :goto_1
    const/4 v6, 0x0

    :try_start_0
    invoke-interface {v5}, Ljava/util/ListIterator;->hasNext()Z

    move-result v7

    if-eqz v7, :cond_a

    .line 2230
    invoke-interface {v5}, Ljava/util/ListIterator;->next()Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Landroid/content/ComponentName;

    .line 2231
    invoke-direct {p0, v7}, Lme/embodied/services/Launcher$ServiceMonitor;->IsMyServiceRunning(Landroid/content/ComponentName;)Z

    move-result v8

    if-nez v8, :cond_1

    invoke-direct {p0, v7}, Lme/embodied/services/Launcher$ServiceMonitor;->IsMyActivityRunning(Landroid/content/ComponentName;)Z

    move-result v8

    if-eqz v8, :cond_0

    goto :goto_2

    :cond_0
    move v8, v6

    goto :goto_3

    :cond_1
    :goto_2
    move v8, v2

    .line 2232
    :goto_3
    if-eqz v8, :cond_2

    move-object v9, v3

    goto :goto_4

    :cond_2
    move-object v9, v4

    :goto_4
    invoke-virtual {p0, v7, v8, v9}, Lme/embodied/services/Launcher$ServiceMonitor;->LogState(Landroid/content/ComponentName;ZLjava/lang/StringBuilder;)V

    .line 2234
    if-nez v8, :cond_8

    .line 2236
    invoke-static {}, Lme/embodied/services/Launcher;->access$1700()Landroid/content/ComponentName;

    move-result-object v8

    if-ne v7, v8, :cond_3

    .line 2237
    const-string v8, "BO#8002 Wifi App dead.  Restarting."

    invoke-static {v0, v8}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 2238
    const-string v8, "BO#8020 Application had to be restarted: Wifi App"

    invoke-static {v0, v8}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 2239
    iget-object v8, p0, Lme/embodied/services/Launcher$ServiceMonitor;->this$0:Lme/embodied/services/Launcher;

    invoke-static {v8, v7}, Lme/embodied/services/Launcher;->access$2000(Lme/embodied/services/Launcher;Landroid/content/ComponentName;)V

    goto/16 :goto_5

    .line 2240
    :cond_3
    invoke-static {}, Lme/embodied/services/Launcher;->access$1800()Landroid/content/ComponentName;

    move-result-object v8

    if-ne v7, v8, :cond_6

    .line 2241
    iget-object v8, p0, Lme/embodied/services/Launcher$ServiceMonitor;->this$0:Lme/embodied/services/Launcher;

    invoke-static {v8}, Lme/embodied/services/Launcher;->access$2100(Lme/embodied/services/Launcher;)Z

    move-result v8

    if-nez v8, :cond_4

    .line 2242
    const-string v3, "Unity App dead.  Not self-managed, exiting all."

    invoke-static {v0, v3}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 2244
    iget-object v3, p0, Lme/embodied/services/Launcher$ServiceMonitor;->context_:Landroid/content/Context;

    new-instance v4, Landroid/content/Intent;

    iget-object v5, p0, Lme/embodied/services/Launcher$ServiceMonitor;->context_:Landroid/content/Context;

    const-class v7, Lme/embodied/services/ServiceLauncher;

    invoke-direct {v4, v5, v7}, Landroid/content/Intent;-><init>(Landroid/content/Context;Ljava/lang/Class;)V

    invoke-virtual {v3, v4}, Landroid/content/Context;->stopService(Landroid/content/Intent;)Z

    .line 2245
    return-void

    .line 2247
    :cond_4
    iget-object v8, p0, Lme/embodied/services/Launcher$ServiceMonitor;->this$0:Lme/embodied/services/Launcher;

    invoke-static {v8}, Lme/embodied/services/Launcher;->access$2200(Lme/embodied/services/Launcher;)Z

    move-result v8

    if-eqz v8, :cond_5

    .line 2248
    const-string v5, "BO#8002 Unity App dead.  Restarting Wifi"

    invoke-static {v0, v5}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 2249
    const-string v5, "BO#8021 Application had to be restarted: Unity Main"

    invoke-static {v0, v5}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 2250
    iget-object v5, p0, Lme/embodied/services/Launcher$ServiceMonitor;->this$0:Lme/embodied/services/Launcher;

    invoke-static {v5}, Lme/embodied/services/Launcher;->access$2300(Lme/embodied/services/Launcher;)V

    .line 2251
    goto :goto_6

    .line 2253
    :cond_5
    const-string v8, "BO#8002 Unity App dead.  Restarting."

    invoke-static {v0, v8}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 2254
    iget-object v8, p0, Lme/embodied/services/Launcher$ServiceMonitor;->this$0:Lme/embodied/services/Launcher;

    invoke-static {v8, v7}, Lme/embodied/services/Launcher;->access$2000(Lme/embodied/services/Launcher;Landroid/content/ComponentName;)V

    goto :goto_5

    .line 2256
    :cond_6
    iget-object v8, p0, Lme/embodied/services/Launcher$ServiceMonitor;->this$0:Lme/embodied/services/Launcher;

    invoke-static {v8}, Lme/embodied/services/Launcher;->access$2400(Lme/embodied/services/Launcher;)Z

    move-result v8

    if-nez v8, :cond_9

    .line 2259
    invoke-virtual {v1, v7}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    move-result v8

    if-nez v8, :cond_7

    .line 2260
    invoke-virtual {v1, v7}, Ljava/util/HashSet;->remove(Ljava/lang/Object;)Z

    .line 2261
    iget-object v8, p0, Lme/embodied/services/Launcher$ServiceMonitor;->this$0:Lme/embodied/services/Launcher;

    invoke-static {v8, v7}, Lme/embodied/services/Launcher;->access$2500(Lme/embodied/services/Launcher;Landroid/content/ComponentName;)V

    goto :goto_5

    .line 2263
    :cond_7
    new-instance v8, Ljava/lang/StringBuilder;

    invoke-direct {v8}, Ljava/lang/StringBuilder;-><init>()V

    const-string v9, "Detected dead service, will restart next cycle: "

    invoke-virtual {v8, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v8, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v8}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    invoke-static {v0, v7}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_5

    .line 2268
    :cond_8
    invoke-virtual {v1, v7}, Ljava/util/HashSet;->remove(Ljava/lang/Object;)Z

    .line 2270
    :cond_9
    :goto_5
    goto/16 :goto_1

    .line 2271
    :cond_a
    :goto_6
    iget-object v5, p0, Lme/embodied/services/Launcher$ServiceMonitor;->this$0:Lme/embodied/services/Launcher;

    invoke-static {v5}, Lme/embodied/services/Launcher;->access$1900(Lme/embodied/services/Launcher;)Ljava/util/Vector;

    move-result-object v5

    invoke-virtual {v5}, Ljava/util/Vector;->size()I

    move-result v5

    if-lez v5, :cond_b

    .line 2272
    const-string v5, "Monitor: State(%s) Up(%s) Missing(%s)"

    const/4 v7, 0x3

    new-array v7, v7, [Ljava/lang/Object;

    iget-object v8, p0, Lme/embodied/services/Launcher$ServiceMonitor;->this$0:Lme/embodied/services/Launcher;

    iget-object v8, v8, Lme/embodied/services/Launcher;->state_:Lme/embodied/services/Launcher$LauncherState;

    aput-object v8, v7, v6

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    aput-object v3, v7, v2

    const/4 v3, 0x2

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    aput-object v4, v7, v3

    invoke-static {v5, v7}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v3

    invoke-static {v0, v3}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I
    :try_end_0
    .catch Ljava/util/ConcurrentModificationException; {:try_start_0 .. :try_end_0} :catch_0

    .line 2276
    :cond_b
    goto :goto_7

    .line 2274
    :catch_0
    move-exception v3

    .line 2275
    const-string v3, "Monitored components changed during monitoring.  Resuming monitoring next cycle."

    invoke-static {v0, v3}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 2278
    :goto_7
    iget-object v3, p0, Lme/embodied/services/Launcher$ServiceMonitor;->this$0:Lme/embodied/services/Launcher;

    invoke-static {v3}, Lme/embodied/services/Launcher;->access$1900(Lme/embodied/services/Launcher;)Ljava/util/Vector;

    move-result-object v3

    invoke-virtual {v3}, Ljava/util/Vector;->size()I

    move-result v3

    if-nez v3, :cond_c

    .line 2279
    const-string v3, "Nothing to monitor."

    invoke-static {v0, v3}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 2285
    :cond_c
    :try_start_1
    iget-object v3, p0, Lme/embodied/services/Launcher$ServiceMonitor;->this$0:Lme/embodied/services/Launcher;

    iget-object v3, v3, Lme/embodied/services/Launcher;->state_:Lme/embodied/services/Launcher$LauncherState;

    sget-object v4, Lme/embodied/services/Launcher$LauncherState;->STATE_LIGHT_SLEEP:Lme/embodied/services/Launcher$LauncherState;

    if-ne v3, v4, :cond_10

    .line 2286
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v3

    iget-object v5, p0, Lme/embodied/services/Launcher$ServiceMonitor;->this$0:Lme/embodied/services/Launcher;

    invoke-static {v5}, Lme/embodied/services/Launcher;->access$2600(Lme/embodied/services/Launcher;)J

    move-result-wide v7

    sub-long/2addr v3, v7

    .line 2287
    iget-object v5, p0, Lme/embodied/services/Launcher$ServiceMonitor;->this$0:Lme/embodied/services/Launcher;

    invoke-virtual {v5}, Lme/embodied/services/Launcher;->isOTAActive()Z

    move-result v5
    :try_end_1
    .catch Ljava/lang/InterruptedException; {:try_start_1 .. :try_end_1} :catch_1

    .line 2288
    const-string v7, " ms ago."

    if-nez v5, :cond_e

    const-wide/16 v8, 0x4e20

    cmp-long v8, v3, v8

    if-lez v8, :cond_e

    .line 2289
    :try_start_2
    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    const-string v8, "Requesting suspend due to background idle detected. Last active data "

    invoke-virtual {v5, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v3, v4}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-static {v0, v3}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 2290
    iget-object v3, p0, Lme/embodied/services/Launcher$ServiceMonitor;->this$0:Lme/embodied/services/Launcher;

    invoke-static {v3, v6, v6}, Lme/embodied/services/Launcher;->access$2700(Lme/embodied/services/Launcher;ZZ)V

    .line 2291
    iget-object v3, p0, Lme/embodied/services/Launcher$ServiceMonitor;->this$0:Lme/embodied/services/Launcher;

    invoke-static {}, Lme/embodied/services/Launcher;->access$2800()Z

    move-result v4

    if-eqz v4, :cond_d

    sget-object v4, Lme/embodied/services/Launcher$LauncherState;->STATE_SILENT_REBOOT:Lme/embodied/services/Launcher$LauncherState;

    goto :goto_8

    :cond_d
    sget-object v4, Lme/embodied/services/Launcher$LauncherState;->STATE_SUSPEND:Lme/embodied/services/Launcher$LauncherState;

    :goto_8
    invoke-static {v3, v4}, Lme/embodied/services/Launcher;->access$500(Lme/embodied/services/Launcher;Lme/embodied/services/Launcher$LauncherState;)V

    .line 2292
    goto/16 :goto_0

    .line 2294
    :cond_e
    new-instance v8, Ljava/lang/StringBuilder;

    invoke-direct {v8}, Ljava/lang/StringBuilder;-><init>()V

    const-string v9, "Continuing to sleep.  Last active data "

    invoke-virtual {v8, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v8, v3, v4}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v8, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    if-eqz v5, :cond_f

    const-string v3, " (OTA ACTIVE)"

    goto :goto_9

    :cond_f
    const-string v3, ""

    :goto_9
    invoke-virtual {v8, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v8}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-static {v0, v3}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 2295
    iget-object v3, p0, Lme/embodied/services/Launcher$ServiceMonitor;->this$0:Lme/embodied/services/Launcher;

    invoke-static {v3, v2, v6}, Lme/embodied/services/Launcher;->access$2700(Lme/embodied/services/Launcher;ZZ)V

    .line 2298
    :cond_10
    iget-object v3, p0, Lme/embodied/services/Launcher$ServiceMonitor;->this$0:Lme/embodied/services/Launcher;

    iget-object v3, v3, Lme/embodied/services/Launcher;->state_:Lme/embodied/services/Launcher$LauncherState;

    invoke-virtual {v3}, Lme/embodied/services/Launcher$LauncherState;->ordinal()I

    move-result v3

    iget-object v4, p0, Lme/embodied/services/Launcher$ServiceMonitor;->this$0:Lme/embodied/services/Launcher;

    iget-object v4, v4, Lme/embodied/services/Launcher;->prev_state_:Lme/embodied/services/Launcher$LauncherState;

    invoke-virtual {v4}, Lme/embodied/services/Launcher$LauncherState;->ordinal()I

    move-result v4

    invoke-static {v3, v4}, Lme/embodied/services/Launcher;->access$2900(II)V

    .line 2301
    iget-object v3, p0, Lme/embodied/services/Launcher$ServiceMonitor;->this$0:Lme/embodied/services/Launcher;

    invoke-static {v3}, Lme/embodied/services/Launcher;->access$800(Lme/embodied/services/Launcher;)Lme/embodied/ServiceMetrics;

    move-result-object v3

    invoke-virtual {v3}, Lme/embodied/ServiceMetrics;->recoveryCheck()I

    move-result v3

    if-eqz v3, :cond_13

    .line 2302
    iget-object v4, p0, Lme/embodied/services/Launcher$ServiceMonitor;->this$0:Lme/embodied/services/Launcher;

    invoke-static {v4}, Lme/embodied/services/Launcher;->access$800(Lme/embodied/services/Launcher;)Lme/embodied/ServiceMetrics;

    move-result-object v4

    invoke-virtual {v4}, Lme/embodied/ServiceMetrics;->updateSecondsRemaining()I

    move-result v4

    .line 2303
    if-nez v4, :cond_11

    .line 2304
    iget-object v3, p0, Lme/embodied/services/Launcher$ServiceMonitor;->this$0:Lme/embodied/services/Launcher;

    const-string v4, "Watchdog service failures"

    invoke-static {v3, v4}, Lme/embodied/services/Launcher;->access$3000(Lme/embodied/services/Launcher;Ljava/lang/String;)V

    goto :goto_a

    .line 2306
    :cond_11
    const/16 v5, 0xf

    if-ne v4, v5, :cond_12

    .line 2308
    iget-object v5, p0, Lme/embodied/services/Launcher$ServiceMonitor;->context_:Landroid/content/Context;

    invoke-virtual {v5}, Landroid/content/Context;->getContentResolver()Landroid/content/ContentResolver;

    move-result-object v5

    invoke-static {v3, v5}, Lme/embodied/RebootCause;->recordCause(ILandroid/content/ContentResolver;)V

    .line 2310
    :cond_12
    iget-object v5, p0, Lme/embodied/services/Launcher$ServiceMonitor;->this$0:Lme/embodied/services/Launcher;

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v6

    invoke-static {v5, v6, v7}, Lme/embodied/services/Launcher;->access$2602(Lme/embodied/services/Launcher;J)J

    .line 2311
    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, "System unstable.  Will recover in "

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v6, " seconds!"

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-static {v0, v5}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 2312
    invoke-static {v3, v4}, Lme/embodied/services/Launcher;->access$3100(II)V

    .line 2315
    :cond_13
    :goto_a
    const-wide/16 v3, 0x3e8

    invoke-static {v3, v4}, Ljava/lang/Thread;->sleep(J)V
    :try_end_2
    .catch Ljava/lang/InterruptedException; {:try_start_2 .. :try_end_2} :catch_1

    .line 2321
    nop

    .line 2322
    goto/16 :goto_0

    .line 2317
    :catch_1
    move-exception v1

    .line 2319
    const-string v1, "Service monitor interrupted. Exiting"

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 2323
    :cond_14
    return-void
.end method
