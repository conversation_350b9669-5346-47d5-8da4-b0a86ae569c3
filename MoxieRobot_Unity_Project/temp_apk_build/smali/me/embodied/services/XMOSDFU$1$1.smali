.class Lme/embodied/services/XMOSDFU$1$1;
.super Ljava/util/TimerTask;
.source "XMOSDFU.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lme/embodied/services/XMOSDFU$1;->onServiceConnected(Landroid/content/ComponentName;Landroid/os/IBinder;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$1:Lme/embodied/services/XMOSDFU$1;


# direct methods
.method constructor <init>(Lme/embodied/services/XMOSDFU$1;)V
    .locals 0

    .line 232
    iput-object p1, p0, Lme/embodied/services/XMOSDFU$1$1;->this$1:Lme/embodied/services/XMOSDFU$1;

    invoke-direct {p0}, Ljava/util/TimerTask;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 3

    .line 235
    const/4 v0, 0x0

    const/4 v1, 0x2

    invoke-static {v0, v1}, Landroid/os/Message;->obtain(Landroid/os/Handler;I)Landroid/os/Message;

    move-result-object v0

    .line 236
    iget-object v1, p0, Lme/embodied/services/XMOSDFU$1$1;->this$1:Lme/embodied/services/XMOSDFU$1;

    iget-object v1, v1, Lme/embodied/services/XMOSDFU$1;->this$0:Lme/embodied/services/XMOSDFU;

    invoke-static {v1}, Lme/embodied/services/XMOSDFU;->access$400(Lme/embodied/services/XMOSDFU;)Landroid/os/Messenger;

    move-result-object v1

    iput-object v1, v0, Landroid/os/Message;->replyTo:Landroid/os/Messenger;

    .line 238
    :try_start_0
    iget-object v1, p0, Lme/embodied/services/XMOSDFU$1$1;->this$1:Lme/embodied/services/XMOSDFU$1;

    iget-object v1, v1, Lme/embodied/services/XMOSDFU$1;->this$0:Lme/embodied/services/XMOSDFU;

    invoke-static {v1}, Lme/embodied/services/XMOSDFU;->access$100(Lme/embodied/services/XMOSDFU;)Landroid/os/Messenger;

    move-result-object v1

    invoke-virtual {v1, v0}, Landroid/os/Messenger;->send(Landroid/os/Message;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 241
    goto :goto_0

    .line 239
    :catch_0
    move-exception v0

    .line 240
    const/4 v1, 0x1

    new-array v1, v1, [Ljava/lang/Object;

    const/4 v2, 0x0

    invoke-virtual {v0}, Ljava/lang/Exception;->getMessage()Ljava/lang/String;

    move-result-object v0

    aput-object v0, v1, v2

    const-string v0, "Failed to send message to service: %s"

    invoke-static {v0, v1}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    const-string v1, "XMOSDFU"

    invoke-static {v1, v0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 242
    :goto_0
    return-void
.end method
