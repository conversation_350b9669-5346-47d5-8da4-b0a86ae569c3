.class Lme/embodied/services/BoSystemMonitor$3;
.super Landroid/content/BroadcastReceiver;
.source "BoSystemMonitor.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lme/embodied/services/BoSystemMonitor;->initStateListeners()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lme/embodied/services/BoSystemMonitor;


# direct methods
.method constructor <init>(Lme/embodied/services/BoSystemMonitor;)V
    .locals 0

    .line 249
    iput-object p1, p0, Lme/embodied/services/BoSystemMonitor$3;->this$0:Lme/embodied/services/BoSystemMonitor;

    invoke-direct {p0}, Landroid/content/BroadcastReceiver;-><init>()V

    return-void
.end method


# virtual methods
.method public onReceive(Landroid/content/Context;Landroid/content/Intent;)V
    .locals 3

    .line 253
    invoke-virtual {p2}, Landroid/content/Intent;->getAction()Ljava/lang/String;

    move-result-object p1

    const-string v0, "android.media.VOLUME_CHANGED_ACTION"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p1

    const-string v0, "bo-system-monitor"

    if-eqz p1, :cond_0

    .line 254
    const-string p1, "Got Volume Change Notification!"

    invoke-static {v0, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 255
    iget-object p1, p0, Lme/embodied/services/BoSystemMonitor$3;->this$0:Lme/embodied/services/BoSystemMonitor;

    invoke-static {p1}, Lme/embodied/services/BoSystemMonitor;->access$000(Lme/embodied/services/BoSystemMonitor;)V

    goto/16 :goto_1

    .line 256
    :cond_0
    invoke-virtual {p2}, Landroid/content/Intent;->getAction()Ljava/lang/String;

    move-result-object p1

    const-string v1, "android.net.wifi.STATE_CHANGE"

    invoke-virtual {p1, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p1

    const-string v1, "networkInfo"

    if-eqz p1, :cond_2

    .line 257
    invoke-virtual {p2, v1}, Landroid/content/Intent;->getParcelableExtra(Ljava/lang/String;)Landroid/os/Parcelable;

    move-result-object p1

    check-cast p1, Landroid/net/NetworkInfo;

    .line 258
    invoke-virtual {p1}, Landroid/net/NetworkInfo;->isConnected()Z

    move-result p1

    if-eqz p1, :cond_1

    .line 260
    iget-object p1, p0, Lme/embodied/services/BoSystemMonitor$3;->this$0:Lme/embodied/services/BoSystemMonitor;

    const-string p2, "wifi"

    invoke-virtual {p1, p2}, Lme/embodied/services/BoSystemMonitor;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroid/net/wifi/WifiManager;

    .line 261
    invoke-virtual {p1}, Landroid/net/wifi/WifiManager;->getConnectionInfo()Landroid/net/wifi/WifiInfo;

    move-result-object p1

    .line 262
    iget-object p2, p0, Lme/embodied/services/BoSystemMonitor$3;->this$0:Lme/embodied/services/BoSystemMonitor;

    const/4 v0, 0x1

    invoke-virtual {p1}, Landroid/net/wifi/WifiInfo;->getSSID()Ljava/lang/String;

    move-result-object p1

    invoke-static {p2, v0, p1}, Lme/embodied/services/BoSystemMonitor;->access$100(Lme/embodied/services/BoSystemMonitor;ZLjava/lang/String;)V

    .line 264
    :cond_1
    goto :goto_1

    :cond_2
    invoke-virtual {p2}, Landroid/content/Intent;->getAction()Ljava/lang/String;

    move-result-object p1

    const-string v2, "android.net.conn.CONNECTIVITY_CHANGE"

    invoke-virtual {p1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p1

    const/4 v2, 0x0

    if-eqz p1, :cond_3

    .line 265
    invoke-virtual {p2, v1}, Landroid/content/Intent;->getParcelableExtra(Ljava/lang/String;)Landroid/os/Parcelable;

    move-result-object p1

    check-cast p1, Landroid/net/NetworkInfo;

    .line 266
    invoke-virtual {p1}, Landroid/net/NetworkInfo;->getDetailedState()Landroid/net/NetworkInfo$DetailedState;

    move-result-object p1

    sget-object p2, Landroid/net/NetworkInfo$DetailedState;->DISCONNECTED:Landroid/net/NetworkInfo$DetailedState;

    if-ne p1, p2, :cond_4

    .line 268
    iget-object p1, p0, Lme/embodied/services/BoSystemMonitor$3;->this$0:Lme/embodied/services/BoSystemMonitor;

    const/4 p2, 0x0

    invoke-static {p1, v2, p2}, Lme/embodied/services/BoSystemMonitor;->access$100(Lme/embodied/services/BoSystemMonitor;ZLjava/lang/String;)V

    goto :goto_0

    .line 270
    :cond_3
    invoke-virtual {p2}, Landroid/content/Intent;->getAction()Ljava/lang/String;

    move-result-object p1

    const-string v1, "android.net.wifi.RSSI_CHANGED"

    invoke-virtual {p1, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_4

    .line 271
    const-string p1, "newRssi"

    invoke-virtual {p2, p1, v2}, Landroid/content/Intent;->getIntExtra(Ljava/lang/String;I)I

    move-result p1

    .line 272
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Wifi RSSI Updated to "

    invoke-virtual {p2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-static {v0, p2}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 273
    iget-object p2, p0, Lme/embodied/services/BoSystemMonitor$3;->this$0:Lme/embodied/services/BoSystemMonitor;

    iget-wide v0, p2, Lme/embodied/services/BoSystemMonitor;->ptr:J

    invoke-static {p2, v0, v1, p1}, Lme/embodied/services/BoSystemMonitor;->access$200(Lme/embodied/services/BoSystemMonitor;JI)V

    goto :goto_1

    .line 270
    :cond_4
    :goto_0
    nop

    .line 275
    :goto_1
    return-void
.end method
