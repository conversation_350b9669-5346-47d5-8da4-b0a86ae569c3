.class Lme/embodied/services/XMOSDFU$1;
.super Ljava/lang/Object;
.source "XMOSDFU.java"

# interfaces
.implements Landroid/content/ServiceConnection;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lme/embodied/services/XMOSDFU;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lme/embodied/services/XMOSDFU;


# direct methods
.method constructor <init>(Lme/embodied/services/XMOSDFU;)V
    .locals 0

    .line 213
    iput-object p1, p0, Lme/embodied/services/XMOSDFU$1;->this$0:Lme/embodied/services/XMOSDFU;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onBindingDied(Landroid/content/ComponentName;)V
    .locals 2

    .line 253
    const/4 v0, 0x1

    new-array v0, v0, [Ljava/lang/Object;

    invoke-virtual {p1}, Landroid/content/ComponentName;->flattenToString()Ljava/lang/String;

    move-result-object p1

    const/4 v1, 0x0

    aput-object p1, v0, v1

    const-string p1, "Disconnected from %s"

    invoke-static {p1, v0}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    const-string v0, "XMOSDFU"

    invoke-static {v0, p1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 255
    iget-object p1, p0, Lme/embodied/services/XMOSDFU$1;->this$0:Lme/embodied/services/XMOSDFU;

    const/4 v0, 0x0

    invoke-static {p1, v0}, Lme/embodied/services/XMOSDFU;->access$102(Lme/embodied/services/XMOSDFU;Landroid/os/Messenger;)Landroid/os/Messenger;

    .line 256
    iget-object p1, p0, Lme/embodied/services/XMOSDFU$1;->this$0:Lme/embodied/services/XMOSDFU;

    invoke-static {p1, v1}, Lme/embodied/services/XMOSDFU;->access$202(Lme/embodied/services/XMOSDFU;Z)Z

    .line 259
    iget-object p1, p0, Lme/embodied/services/XMOSDFU$1;->this$0:Lme/embodied/services/XMOSDFU;

    invoke-static {p1}, Lme/embodied/services/XMOSDFU;->access$500(Lme/embodied/services/XMOSDFU;)V

    .line 260
    return-void
.end method

.method public onServiceConnected(Landroid/content/ComponentName;Landroid/os/IBinder;)V
    .locals 6

    .line 215
    iget-object v0, p0, Lme/embodied/services/XMOSDFU$1;->this$0:Lme/embodied/services/XMOSDFU;

    new-instance v1, Landroid/os/Messenger;

    invoke-direct {v1, p2}, Landroid/os/Messenger;-><init>(Landroid/os/IBinder;)V

    invoke-static {v0, v1}, Lme/embodied/services/XMOSDFU;->access$102(Lme/embodied/services/XMOSDFU;Landroid/os/Messenger;)Landroid/os/Messenger;

    .line 216
    iget-object p2, p0, Lme/embodied/services/XMOSDFU$1;->this$0:Lme/embodied/services/XMOSDFU;

    const/4 v0, 0x1

    invoke-static {p2, v0}, Lme/embodied/services/XMOSDFU;->access$202(Lme/embodied/services/XMOSDFU;Z)Z

    .line 218
    new-array p2, v0, [Ljava/lang/Object;

    invoke-virtual {p1}, Landroid/content/ComponentName;->flattenToString()Ljava/lang/String;

    move-result-object p1

    const/4 v1, 0x0

    aput-object p1, p2, v1

    const-string p1, "Connected to %s"

    invoke-static {p1, p2}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    const-string p2, "XMOSDFU"

    invoke-static {p2, p1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 221
    new-instance p1, Landroid/os/Bundle;

    invoke-direct {p1}, Landroid/os/Bundle;-><init>()V

    .line 222
    iget-object v2, p0, Lme/embodied/services/XMOSDFU$1;->this$0:Lme/embodied/services/XMOSDFU;

    iget-object v2, v2, Lme/embodied/services/XMOSDFU;->sources_:Lme/embodied/services/XMOSDFU$XMOSVariants;

    iget-object v2, v2, Lme/embodied/services/XMOSDFU$XMOSVariants;->binary_path_:Ljava/io/File;

    invoke-virtual {v2}, Ljava/io/File;->getAbsolutePath()Ljava/lang/String;

    move-result-object v2

    const-string v3, "fwpath"

    invoke-virtual {p1, v3, v2}, Landroid/os/Bundle;->putString(Ljava/lang/String;Ljava/lang/String;)V

    .line 223
    const/4 v2, 0x0

    invoke-static {v2, v0}, Landroid/os/Message;->obtain(Landroid/os/Handler;I)Landroid/os/Message;

    move-result-object v2

    .line 224
    iput-object p1, v2, Landroid/os/Message;->obj:Ljava/lang/Object;

    .line 226
    :try_start_0
    iget-object p1, p0, Lme/embodied/services/XMOSDFU$1;->this$0:Lme/embodied/services/XMOSDFU;

    invoke-static {p1}, Lme/embodied/services/XMOSDFU;->access$100(Lme/embodied/services/XMOSDFU;)Landroid/os/Messenger;

    move-result-object p1

    invoke-virtual {p1, v2}, Landroid/os/Messenger;->send(Landroid/os/Message;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    .line 229
    goto :goto_0

    .line 227
    :catch_0
    move-exception p1

    .line 228
    new-array v0, v0, [Ljava/lang/Object;

    invoke-virtual {p1}, Landroid/os/RemoteException;->getMessage()Ljava/lang/String;

    move-result-object p1

    aput-object p1, v0, v1

    const-string p1, "Failed to send message to service: %s"

    invoke-static {p1, v0}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    invoke-static {p2, p1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 231
    :goto_0
    iget-object p1, p0, Lme/embodied/services/XMOSDFU$1;->this$0:Lme/embodied/services/XMOSDFU;

    new-instance p2, Ljava/util/Timer;

    invoke-direct {p2}, Ljava/util/Timer;-><init>()V

    invoke-static {p1, p2}, Lme/embodied/services/XMOSDFU;->access$302(Lme/embodied/services/XMOSDFU;Ljava/util/Timer;)Ljava/util/Timer;

    .line 232
    iget-object p1, p0, Lme/embodied/services/XMOSDFU$1;->this$0:Lme/embodied/services/XMOSDFU;

    invoke-static {p1}, Lme/embodied/services/XMOSDFU;->access$300(Lme/embodied/services/XMOSDFU;)Ljava/util/Timer;

    move-result-object v0

    new-instance v1, Lme/embodied/services/XMOSDFU$1$1;

    invoke-direct {v1, p0}, Lme/embodied/services/XMOSDFU$1$1;-><init>(Lme/embodied/services/XMOSDFU$1;)V

    const-wide/16 v2, 0x0

    const-wide/16 v4, 0x3e8

    invoke-virtual/range {v0 .. v5}, Ljava/util/Timer;->schedule(Ljava/util/TimerTask;JJ)V

    .line 244
    return-void
.end method

.method public onServiceDisconnected(Landroid/content/ComponentName;)V
    .locals 3

    .line 247
    const/4 v0, 0x1

    new-array v0, v0, [Ljava/lang/Object;

    invoke-virtual {p1}, Landroid/content/ComponentName;->flattenToString()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x0

    aput-object v1, v0, v2

    const-string v1, "Abruptly disconnected from %s"

    invoke-static {v1, v0}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    const-string v1, "XMOSDFU"

    invoke-static {v1, v0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 249
    invoke-virtual {p0, p1}, Lme/embodied/services/XMOSDFU$1;->onBindingDied(Landroid/content/ComponentName;)V

    .line 250
    return-void
.end method
