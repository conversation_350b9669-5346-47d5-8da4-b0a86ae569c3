.class Lme/embodied/services/BoSystemMonitor$2;
.super Ljava/lang/Thread;
.source "BoSystemMonitor.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lme/embodied/services/BoSystemMonitor;->OnInternetRecoveryRequest()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lme/embodied/services/BoSystemMonitor;


# direct methods
.method constructor <init>(Lme/embodied/services/BoSystemMonitor;)V
    .locals 0

    .line 232
    iput-object p1, p0, Lme/embodied/services/BoSystemMonitor$2;->this$0:Lme/embodied/services/BoSystemMonitor;

    invoke-direct {p0}, Ljava/lang/Thread;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 3

    .line 236
    const-string v0, "bo-system-monitor"

    :try_start_0
    iget-object v1, p0, Lme/embodied/services/BoSystemMonitor$2;->this$0:Lme/embodied/services/BoSystemMonitor;

    const/4 v2, 0x0

    iput-boolean v2, v1, Lme/embodied/services/BoSystemMonitor;->last_wifi_state_:Z

    .line 237
    const-string v1, "BO#8003 Attempting to recover network.  Restarting wifi."

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 238
    iget-object v1, p0, Lme/embodied/services/BoSystemMonitor$2;->this$0:Lme/embodied/services/BoSystemMonitor;

    invoke-static {v1, v2}, Lme/embodied/NetworkUtil;->enableWifi(Landroid/content/Context;Z)V

    .line 239
    const-wide/16 v1, 0x3e8

    invoke-static {v1, v2}, Ljava/lang/Thread;->sleep(J)V

    .line 240
    iget-object v1, p0, Lme/embodied/services/BoSystemMonitor$2;->this$0:Lme/embodied/services/BoSystemMonitor;

    const/4 v2, 0x1

    invoke-static {v1, v2}, Lme/embodied/NetworkUtil;->enableWifi(Landroid/content/Context;Z)V
    :try_end_0
    .catch Ljava/lang/InterruptedException; {:try_start_0 .. :try_end_0} :catch_0

    .line 243
    goto :goto_0

    .line 241
    :catch_0
    move-exception v1

    .line 242
    const-string v2, "Background wifi recovery aborted!"

    invoke-static {v0, v2, v1}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)I

    .line 244
    :goto_0
    return-void
.end method
