.class synthetic Lme/embodied/services/Launcher$7;
.super Ljava/lang/Object;
.source "Launcher.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lme/embodied/services/Launcher;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1008
    name = null
.end annotation


# static fields
.field static final synthetic $SwitchMap$me$embodied$services$Launcher$AnimType:[I


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 609
    invoke-static {}, Lme/embodied/services/Launcher$AnimType;->values()[Lme/embodied/services/Launcher$AnimType;

    move-result-object v0

    array-length v0, v0

    new-array v0, v0, [I

    sput-object v0, Lme/embodied/services/Launcher$7;->$SwitchMap$me$embodied$services$Launcher$AnimType:[I

    :try_start_0
    sget-object v1, Lme/embodied/services/Launcher$AnimType;->ANIM_NONE:Lme/embodied/services/Launcher$AnimType;

    invoke-virtual {v1}, Lme/embodied/services/Launcher$AnimType;->ordinal()I

    move-result v1

    const/4 v2, 0x1

    aput v2, v0, v1
    :try_end_0
    .catch Ljava/lang/NoSuchFieldError; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    :goto_0
    :try_start_1
    sget-object v0, Lme/embodied/services/Launcher$7;->$SwitchMap$me$embodied$services$Launcher$AnimType:[I

    sget-object v1, Lme/embodied/services/Launcher$AnimType;->ANIM_START:Lme/embodied/services/Launcher$AnimType;

    invoke-virtual {v1}, Lme/embodied/services/Launcher$AnimType;->ordinal()I

    move-result v1

    const/4 v2, 0x2

    aput v2, v0, v1
    :try_end_1
    .catch Ljava/lang/NoSuchFieldError; {:try_start_1 .. :try_end_1} :catch_1

    goto :goto_1

    :catch_1
    move-exception v0

    :goto_1
    return-void
.end method
