.class Lme/embodied/services/BoUpdater$1;
.super Ljava/lang/Thread;
.source "BoUpdater.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lme/embodied/services/BoUpdater;->onStartCommand(Landroid/content/Intent;II)I
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lme/embodied/services/BoUpdater;


# direct methods
.method constructor <init>(Lme/embodied/services/BoUpdater;)V
    .locals 0

    .line 89
    iput-object p1, p0, Lme/embodied/services/BoUpdater$1;->this$0:Lme/embodied/services/BoUpdater;

    invoke-direct {p0}, Ljava/lang/Thread;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 1

    .line 92
    iget-object v0, p0, Lme/embodied/services/BoUpdater$1;->this$0:Lme/embodied/services/BoUpdater;

    invoke-virtual {v0}, Lme/embodied/services/BoUpdater;->CheckForUpdatesUntilStopped()V

    .line 93
    return-void
.end method
