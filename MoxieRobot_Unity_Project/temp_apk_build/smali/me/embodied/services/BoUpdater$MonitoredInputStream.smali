.class public Lme/embodied/services/BoUpdater$MonitoredInputStream;
.super Ljava/io/InputStream;
.source "BoUpdater.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lme/embodied/services/BoUpdater;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "MonitoredInputStream"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lme/embodied/services/BoUpdater$MonitoredInputStream$Monitor;
    }
.end annotation


# instance fields
.field private current:J

.field private monitor:Lme/embodied/services/BoUpdater$MonitoredInputStream$Monitor;

.field private sub:Ljava/io/InputStream;

.field private total:J


# direct methods
.method public constructor <init>(Ljava/io/InputStream;JLme/embodied/services/BoUpdater$MonitoredInputStream$Monitor;)V
    .locals 2

    .line 538
    invoke-direct {p0}, Ljava/io/InputStream;-><init>()V

    .line 539
    iput-object p1, p0, Lme/embodied/services/BoUpdater$MonitoredInputStream;->sub:Ljava/io/InputStream;

    .line 540
    const-wide/16 v0, 0x0

    iput-wide v0, p0, Lme/embodied/services/BoUpdater$MonitoredInputStream;->current:J

    .line 541
    iput-wide p2, p0, Lme/embodied/services/BoUpdater$MonitoredInputStream;->total:J

    .line 542
    iput-object p4, p0, Lme/embodied/services/BoUpdater$MonitoredInputStream;->monitor:Lme/embodied/services/BoUpdater$MonitoredInputStream$Monitor;

    .line 543
    invoke-direct {p0, v0, v1}, Lme/embodied/services/BoUpdater$MonitoredInputStream;->note(J)V

    .line 544
    return-void
.end method

.method private note(J)V
    .locals 4

    .line 581
    iget-wide v0, p0, Lme/embodied/services/BoUpdater$MonitoredInputStream;->current:J

    add-long/2addr v0, p1

    iput-wide v0, p0, Lme/embodied/services/BoUpdater$MonitoredInputStream;->current:J

    .line 582
    iget-object p1, p0, Lme/embodied/services/BoUpdater$MonitoredInputStream;->monitor:Lme/embodied/services/BoUpdater$MonitoredInputStream$Monitor;

    iget-wide v2, p0, Lme/embodied/services/BoUpdater$MonitoredInputStream;->total:J

    invoke-interface {p1, v0, v1, v2, v3}, Lme/embodied/services/BoUpdater$MonitoredInputStream$Monitor;->notify(JJ)V

    .line 583
    return-void
.end method


# virtual methods
.method public available()I
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 545
    iget-object v0, p0, Lme/embodied/services/BoUpdater$MonitoredInputStream;->sub:Ljava/io/InputStream;

    invoke-virtual {v0}, Ljava/io/InputStream;->available()I

    move-result v0

    return v0
.end method

.method public close()V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 546
    iget-object v0, p0, Lme/embodied/services/BoUpdater$MonitoredInputStream;->sub:Ljava/io/InputStream;

    invoke-virtual {v0}, Ljava/io/InputStream;->close()V

    return-void
.end method

.method public mark(I)V
    .locals 1

    .line 547
    iget-object v0, p0, Lme/embodied/services/BoUpdater$MonitoredInputStream;->sub:Ljava/io/InputStream;

    invoke-virtual {v0, p1}, Ljava/io/InputStream;->mark(I)V

    return-void
.end method

.method public markSupported()Z
    .locals 1

    .line 549
    iget-object v0, p0, Lme/embodied/services/BoUpdater$MonitoredInputStream;->sub:Ljava/io/InputStream;

    invoke-virtual {v0}, Ljava/io/InputStream;->markSupported()Z

    move-result v0

    return v0
.end method

.method public read()I
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 552
    iget-object v0, p0, Lme/embodied/services/BoUpdater$MonitoredInputStream;->sub:Ljava/io/InputStream;

    invoke-virtual {v0}, Ljava/io/InputStream;->read()I

    move-result v0

    .line 553
    if-ltz v0, :cond_0

    .line 554
    const-wide/16 v1, 0x1

    invoke-direct {p0, v1, v2}, Lme/embodied/services/BoUpdater$MonitoredInputStream;->note(J)V

    .line 555
    :cond_0
    return v0
.end method

.method public read([B)I
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 558
    iget-object v0, p0, Lme/embodied/services/BoUpdater$MonitoredInputStream;->sub:Ljava/io/InputStream;

    invoke-virtual {v0, p1}, Ljava/io/InputStream;->read([B)I

    move-result p1

    .line 559
    int-to-long v0, p1

    invoke-direct {p0, v0, v1}, Lme/embodied/services/BoUpdater$MonitoredInputStream;->note(J)V

    .line 560
    return p1
.end method

.method public read([BII)I
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 563
    iget-object v0, p0, Lme/embodied/services/BoUpdater$MonitoredInputStream;->sub:Ljava/io/InputStream;

    invoke-virtual {v0, p1, p2, p3}, Ljava/io/InputStream;->read([BII)I

    move-result p1

    .line 564
    int-to-long p2, p1

    invoke-direct {p0, p2, p3}, Lme/embodied/services/BoUpdater$MonitoredInputStream;->note(J)V

    .line 565
    return p1
.end method

.method public reset()V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 568
    iget-object v0, p0, Lme/embodied/services/BoUpdater$MonitoredInputStream;->sub:Ljava/io/InputStream;

    invoke-virtual {v0}, Ljava/io/InputStream;->reset()V

    .line 569
    return-void
.end method

.method public skip(J)J
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 571
    iget-object v0, p0, Lme/embodied/services/BoUpdater$MonitoredInputStream;->sub:Ljava/io/InputStream;

    invoke-virtual {v0, p1, p2}, Ljava/io/InputStream;->skip(J)J

    move-result-wide p1

    .line 572
    invoke-direct {p0, p1, p2}, Lme/embodied/services/BoUpdater$MonitoredInputStream;->note(J)V

    .line 573
    return-wide p1
.end method
