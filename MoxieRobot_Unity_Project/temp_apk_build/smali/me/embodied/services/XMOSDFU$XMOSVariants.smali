.class Lme/embodied/services/XMOSDFU$XMOSVariants;
.super Ljava/lang/Object;
.source "XMOSDFU.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lme/embodied/services/XMOSDFU;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x2
    name = "XMOSVariants"
.end annotation


# instance fields
.field public binary_path_:Ljava/io/File;

.field final synthetic this$0:Lme/embodied/services/XMOSDFU;

.field public version_:Ljava/lang/String;


# direct methods
.method public constructor <init>(Lme/embodied/services/XMOSDFU;)V
    .locals 8

    .line 58
    iput-object p1, p0, Lme/embodied/services/XMOSDFU$XMOSVariants;->this$0:Lme/embodied/services/XMOSDFU;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 59
    const-string v0, "xmos_variant"

    invoke-static {v0}, Lme/embodied/DeviceSettings;->getStringS(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    .line 60
    nop

    .line 61
    nop

    .line 62
    const/4 v1, 0x0

    const/4 v2, 0x0

    const-string v3, ""

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Ljava/lang/String;->isEmpty()Z

    move-result v4

    if-nez v4, :cond_1

    .line 63
    new-instance v4, Ljava/io/File;

    const/4 v5, 0x1

    new-array v6, v5, [Ljava/lang/Object;

    aput-object v0, v6, v2

    const-string v7, "/vendor/etc/firmware/xmosdfu-%s_version.txt"

    invoke-static {v7, v6}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v6

    invoke-direct {v4, v6}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    .line 64
    new-instance v6, Ljava/io/File;

    new-array v5, v5, [Ljava/lang/Object;

    aput-object v0, v5, v2

    const-string v7, "/vendor/etc/firmware/xmosdfu-%s.bin"

    invoke-static {v7, v5}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v5

    invoke-direct {v6, v5}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    iput-object v6, p0, Lme/embodied/services/XMOSDFU$XMOSVariants;->binary_path_:Ljava/io/File;

    .line 65
    invoke-virtual {v4}, Ljava/io/File;->exists()Z

    move-result v5

    const-string v6, "XMOSDFU"

    if-eqz v5, :cond_0

    iget-object v5, p0, Lme/embodied/services/XMOSDFU$XMOSVariants;->binary_path_:Ljava/io/File;

    invoke-virtual {v5}, Ljava/io/File;->exists()Z

    move-result v5

    if-eqz v5, :cond_0

    .line 66
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "XMOS using alternate xmosdfu-"

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v6, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 67
    move-object v1, v4

    goto :goto_0

    .line 70
    :cond_0
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "XMOS could not locate alternate xmosdfu-"

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v6, v0}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 71
    move-object v0, v3

    goto :goto_0

    .line 74
    :cond_1
    move-object v0, v3

    :goto_0
    if-nez v1, :cond_2

    .line 75
    new-instance v1, Ljava/io/File;

    const-string v3, "/vendor/etc/firmware/xmosdfu_version.txt"

    invoke-direct {v1, v3}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    .line 76
    new-instance v3, Ljava/io/File;

    const-string v4, "/vendor/etc/firmware/xmosdfu.bin"

    invoke-direct {v3, v4}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    iput-object v3, p0, Lme/embodied/services/XMOSDFU$XMOSVariants;->binary_path_:Ljava/io/File;

    .line 78
    :cond_2
    invoke-static {p1, v1}, Lme/embodied/services/XMOSDFU;->access$000(Lme/embodied/services/XMOSDFU;Ljava/io/File;)I

    move-result p1

    .line 79
    if-lez p1, :cond_4

    .line 82
    invoke-virtual {v0}, Ljava/lang/String;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_3

    .line 84
    invoke-static {p1}, Ljava/lang/Integer;->toString(I)Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lme/embodied/services/XMOSDFU$XMOSVariants;->version_:Ljava/lang/String;

    goto :goto_1

    .line 90
    :cond_3
    invoke-virtual {v0, v2}, Ljava/lang/String;->charAt(I)C

    move-result v0

    mul-int/lit8 v0, v0, 0x64

    add-int/2addr v0, p1

    invoke-static {v0}, Ljava/lang/Integer;->toString(I)Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lme/embodied/services/XMOSDFU$XMOSVariants;->version_:Ljava/lang/String;

    .line 93
    :cond_4
    :goto_1
    return-void
.end method
