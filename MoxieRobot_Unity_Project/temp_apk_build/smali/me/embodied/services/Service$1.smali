.class Lme/embodied/services/Service$1;
.super Ljava/lang/Object;
.source "Service.java"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lme/embodied/services/Service;->onStartCommand(Landroid/content/Intent;II)I
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lme/embodied/services/Service;


# direct methods
.method constructor <init>(Lme/embodied/services/Service;)V
    .locals 0

    .line 47
    iput-object p1, p0, Lme/embodied/services/Service$1;->this$0:Lme/embodied/services/Service;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 1

    .line 49
    iget-object v0, p0, Lme/embodied/services/Service$1;->this$0:Lme/embodied/services/Service;

    invoke-virtual {v0}, Lme/embodied/services/Service;->Start()V

    .line 50
    return-void
.end method
