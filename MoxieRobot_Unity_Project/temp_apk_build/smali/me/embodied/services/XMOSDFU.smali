.class public Lme/embodied/services/XMOSDFU;
.super Landroid/app/Service;
.source "XMOSDFU.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lme/embodied/services/XMOSDFU$IncomingHandler;,
        Lme/embodied/services/XMOSDFU$XmosState;,
        Lme/embodied/services/XMOSDFU$XMOSVariants;
    }
.end annotation


# static fields
.field private static final KEY_XMOS_VARIANT:Ljava/lang/String; = "xmos_variant"

.field private static final TAG:Ljava/lang/String; = "XMOSDFU"


# instance fields
.field private bound:Z

.field private context_:Landroid/content/Context;

.field private isXmosUpdating:Z

.field private final mConnection:Landroid/content/ServiceConnection;

.field private replyMsgr:Landroid/os/Messenger;

.field private serviceMsgr:Landroid/os/Messenger;

.field sources_:Lme/embodied/services/XMOSDFU$XMOSVariants;

.field private updateTimer_:Ljava/util/Timer;


# direct methods
.method public constructor <init>()V
    .locals 2

    .line 45
    invoke-direct {p0}, Landroid/app/Service;-><init>()V

    .line 34
    const/4 v0, 0x0

    iput-boolean v0, p0, Lme/embodied/services/XMOSDFU;->isXmosUpdating:Z

    .line 38
    const/4 v0, 0x0

    iput-object v0, p0, Lme/embodied/services/XMOSDFU;->serviceMsgr:Landroid/os/Messenger;

    .line 39
    iput-object v0, p0, Lme/embodied/services/XMOSDFU;->replyMsgr:Landroid/os/Messenger;

    .line 213
    new-instance v0, Lme/embodied/services/XMOSDFU$1;

    invoke-direct {v0, p0}, Lme/embodied/services/XMOSDFU$1;-><init>(Lme/embodied/services/XMOSDFU;)V

    iput-object v0, p0, Lme/embodied/services/XMOSDFU;->mConnection:Landroid/content/ServiceConnection;

    .line 47
    iput-object p0, p0, Lme/embodied/services/XMOSDFU;->context_:Landroid/content/Context;

    .line 48
    new-instance v0, Lme/embodied/services/XMOSDFU$XMOSVariants;

    invoke-direct {v0, p0}, Lme/embodied/services/XMOSDFU$XMOSVariants;-><init>(Lme/embodied/services/XMOSDFU;)V

    iput-object v0, p0, Lme/embodied/services/XMOSDFU;->sources_:Lme/embodied/services/XMOSDFU$XMOSVariants;

    .line 49
    const-string v0, "XMOSDFU"

    const-string v1, "XMOSDFU init"

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 50
    return-void
.end method

.method private StopUpdater()V
    .locals 1

    .line 264
    iget-object v0, p0, Lme/embodied/services/XMOSDFU;->updateTimer_:Ljava/util/Timer;

    if-eqz v0, :cond_0

    .line 265
    invoke-virtual {v0}, Ljava/util/Timer;->cancel()V

    .line 266
    iget-object v0, p0, Lme/embodied/services/XMOSDFU;->updateTimer_:Ljava/util/Timer;

    invoke-virtual {v0}, Ljava/util/Timer;->purge()I

    .line 267
    const/4 v0, 0x0

    iput-object v0, p0, Lme/embodied/services/XMOSDFU;->updateTimer_:Ljava/util/Timer;

    .line 269
    :cond_0
    return-void
.end method

.method private XmosUpdateCleanup()V
    .locals 3

    .line 272
    invoke-direct {p0}, Lme/embodied/services/XMOSDFU;->StopUpdater()V

    .line 275
    iget-boolean v0, p0, Lme/embodied/services/XMOSDFU;->bound:Z

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    .line 276
    iget-object v0, p0, Lme/embodied/services/XMOSDFU;->context_:Landroid/content/Context;

    iget-object v2, p0, Lme/embodied/services/XMOSDFU;->mConnection:Landroid/content/ServiceConnection;

    invoke-virtual {v0, v2}, Landroid/content/Context;->unbindService(Landroid/content/ServiceConnection;)V

    .line 277
    iput-boolean v1, p0, Lme/embodied/services/XMOSDFU;->bound:Z

    .line 281
    :cond_0
    iput-boolean v1, p0, Lme/embodied/services/XMOSDFU;->isXmosUpdating:Z

    .line 282
    return-void
.end method

.method static synthetic access$000(Lme/embodied/services/XMOSDFU;Ljava/io/File;)I
    .locals 0

    .line 30
    invoke-direct {p0, p1}, Lme/embodied/services/XMOSDFU;->readIntegerFile(Ljava/io/File;)I

    move-result p0

    return p0
.end method

.method static synthetic access$100(Lme/embodied/services/XMOSDFU;)Landroid/os/Messenger;
    .locals 0

    .line 30
    iget-object p0, p0, Lme/embodied/services/XMOSDFU;->serviceMsgr:Landroid/os/Messenger;

    return-object p0
.end method

.method static synthetic access$102(Lme/embodied/services/XMOSDFU;Landroid/os/Messenger;)Landroid/os/Messenger;
    .locals 0

    .line 30
    iput-object p1, p0, Lme/embodied/services/XMOSDFU;->serviceMsgr:Landroid/os/Messenger;

    return-object p1
.end method

.method static synthetic access$202(Lme/embodied/services/XMOSDFU;Z)Z
    .locals 0

    .line 30
    iput-boolean p1, p0, Lme/embodied/services/XMOSDFU;->bound:Z

    return p1
.end method

.method static synthetic access$300(Lme/embodied/services/XMOSDFU;)Ljava/util/Timer;
    .locals 0

    .line 30
    iget-object p0, p0, Lme/embodied/services/XMOSDFU;->updateTimer_:Ljava/util/Timer;

    return-object p0
.end method

.method static synthetic access$302(Lme/embodied/services/XMOSDFU;Ljava/util/Timer;)Ljava/util/Timer;
    .locals 0

    .line 30
    iput-object p1, p0, Lme/embodied/services/XMOSDFU;->updateTimer_:Ljava/util/Timer;

    return-object p1
.end method

.method static synthetic access$400(Lme/embodied/services/XMOSDFU;)Landroid/os/Messenger;
    .locals 0

    .line 30
    iget-object p0, p0, Lme/embodied/services/XMOSDFU;->replyMsgr:Landroid/os/Messenger;

    return-object p0
.end method

.method static synthetic access$500(Lme/embodied/services/XMOSDFU;)V
    .locals 0

    .line 30
    invoke-direct {p0}, Lme/embodied/services/XMOSDFU;->StopUpdater()V

    return-void
.end method

.method static synthetic access$600(Lme/embodied/services/XMOSDFU;)V
    .locals 0

    .line 30
    invoke-direct {p0}, Lme/embodied/services/XMOSDFU;->XmosUpdateCleanup()V

    return-void
.end method

.method static synthetic access$700(Lme/embodied/services/XMOSDFU;)V
    .locals 0

    .line 30
    invoke-direct {p0}, Lme/embodied/services/XMOSDFU;->postOtaCheckComplete()V

    return-void
.end method

.method private postOtaCheckComplete()V
    .locals 2

    .line 123
    const-string v0, "XMOSDFU"

    const-string v1, "Sending ACTION_OTA_CHECK_COMPLETE"

    invoke-static {v0, v1}, Landroid/util/Log;->v(Ljava/lang/String;Ljava/lang/String;)I

    .line 124
    new-instance v0, Landroid/content/Intent;

    const-string v1, "com.embodied.ota_check_complete"

    invoke-direct {v0, v1}, Landroid/content/Intent;-><init>(Ljava/lang/String;)V

    invoke-virtual {p0, v0}, Lme/embodied/services/XMOSDFU;->sendBroadcast(Landroid/content/Intent;)V

    .line 125
    return-void
.end method

.method private readIntegerFile(Ljava/io/File;)I
    .locals 4

    .line 128
    const-string v0, "XMOSDFU"

    .line 129
    nop

    .line 132
    const/4 v1, -0x1

    :try_start_0
    new-instance v2, Ljava/io/BufferedReader;

    new-instance v3, Ljava/io/FileReader;

    invoke-direct {v3, p1}, Ljava/io/FileReader;-><init>(Ljava/io/File;)V

    invoke-direct {v2, v3}, Ljava/io/BufferedReader;-><init>(Ljava/io/Reader;)V

    .line 133
    invoke-virtual {v2}, Ljava/io/BufferedReader;->readLine()Ljava/lang/String;

    move-result-object p1
    :try_end_0
    .catch Ljava/io/FileNotFoundException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    .line 140
    goto :goto_0

    .line 136
    :catch_0
    move-exception p1

    .line 137
    const-string v2, "readLong IO error"

    invoke-static {v0, v2}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 138
    invoke-virtual {p1}, Ljava/io/IOException;->printStackTrace()V

    .line 139
    return v1

    .line 134
    :catch_1
    move-exception v2

    .line 135
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "File not found: "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v0, p1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 140
    const/4 p1, 0x0

    .line 142
    :goto_0
    if-eqz p1, :cond_0

    .line 144
    :try_start_1
    invoke-static {p1}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v1
    :try_end_1
    .catch Ljava/lang/NumberFormatException; {:try_start_1 .. :try_end_1} :catch_2

    .line 148
    goto :goto_1

    .line 145
    :catch_2
    move-exception p1

    .line 146
    const-string p1, "Failed to parse long"

    invoke-static {v0, p1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 147
    return v1

    .line 151
    :cond_0
    :goto_1
    return v1
.end method


# virtual methods
.method public RequestUpdateXMOS()V
    .locals 6

    .line 187
    iget-boolean v0, p0, Lme/embodied/services/XMOSDFU;->isXmosUpdating:Z

    const/4 v1, 0x1

    const-string v2, "XMOSDFU"

    if-ne v0, v1, :cond_0

    .line 188
    const-string v0, "XMOS Update in progress, not running."

    invoke-static {v2, v0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 189
    return-void

    .line 191
    :cond_0
    const-string v0, "XMOS FW Update started."

    invoke-static {v2, v0}, Landroid/util/Log;->v(Ljava/lang/String;Ljava/lang/String;)I

    .line 192
    iput-boolean v1, p0, Lme/embodied/services/XMOSDFU;->isXmosUpdating:Z

    .line 195
    const-string v0, "XMOS: Binding"

    invoke-static {v2, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 196
    new-instance v0, Landroid/os/Messenger;

    new-instance v3, Lme/embodied/services/XMOSDFU$IncomingHandler;

    new-instance v4, Ljava/lang/ref/WeakReference;

    invoke-direct {v4, p0}, Ljava/lang/ref/WeakReference;-><init>(Ljava/lang/Object;)V

    invoke-direct {v3, p0, v4}, Lme/embodied/services/XMOSDFU$IncomingHandler;-><init>(Lme/embodied/services/XMOSDFU;Ljava/lang/ref/WeakReference;)V

    invoke-direct {v0, v3}, Landroid/os/Messenger;-><init>(Landroid/os/Handler;)V

    iput-object v0, p0, Lme/embodied/services/XMOSDFU;->replyMsgr:Landroid/os/Messenger;

    .line 198
    :try_start_0
    new-instance v0, Landroid/content/Intent;

    invoke-direct {v0}, Landroid/content/Intent;-><init>()V

    .line 199
    new-instance v3, Landroid/content/ComponentName;

    const-string v4, "me.embodied.xmosdfu"

    const-string v5, "me.embodied.xmosdfu.ServiceDFU"

    invoke-direct {v3, v4, v5}, Landroid/content/ComponentName;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    invoke-virtual {v0, v3}, Landroid/content/Intent;->setComponent(Landroid/content/ComponentName;)Landroid/content/Intent;

    .line 201
    const-string v3, "EXTRA_FW_PATH"

    iget-object v4, p0, Lme/embodied/services/XMOSDFU;->sources_:Lme/embodied/services/XMOSDFU$XMOSVariants;

    iget-object v4, v4, Lme/embodied/services/XMOSDFU$XMOSVariants;->binary_path_:Ljava/io/File;

    invoke-virtual {v4}, Ljava/io/File;->getAbsolutePath()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v0, v3, v4}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Ljava/lang/String;)Landroid/content/Intent;

    .line 202
    iget-object v3, p0, Lme/embodied/services/XMOSDFU;->context_:Landroid/content/Context;

    iget-object v4, p0, Lme/embodied/services/XMOSDFU;->mConnection:Landroid/content/ServiceConnection;

    invoke-virtual {v3, v0, v4, v1}, Landroid/content/Context;->bindService(Landroid/content/Intent;Landroid/content/ServiceConnection;I)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 203
    const-string v0, "bindService returned true"

    invoke-static {v2, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_0

    .line 205
    :cond_1
    const-string v0, "bindService returned false"

    invoke-static {v2, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I
    :try_end_0
    .catch Ljava/lang/SecurityException; {:try_start_0 .. :try_end_0} :catch_0

    .line 210
    :goto_0
    goto :goto_1

    .line 207
    :catch_0
    move-exception v0

    .line 208
    const-string v1, "Failed to create reply Messenger for XMOS update"

    invoke-static {v2, v1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 209
    invoke-virtual {v0}, Ljava/lang/SecurityException;->printStackTrace()V

    .line 211
    :goto_1
    return-void
.end method

.method public XmosUpdateSharedPref()V
    .locals 3

    .line 174
    iget-object v0, p0, Lme/embodied/services/XMOSDFU;->sources_:Lme/embodied/services/XMOSDFU$XMOSVariants;

    iget-object v0, v0, Lme/embodied/services/XMOSDFU$XMOSVariants;->version_:Ljava/lang/String;

    if-nez v0, :cond_0

    .line 175
    const-string v0, "XMOSDFU"

    const-string v1, "XMOS OS version unknown, not updating SharedPreferences."

    invoke-static {v0, v1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 176
    return-void

    .line 180
    :cond_0
    invoke-static {p0}, Landroid/preference/PreferenceManager;->getDefaultSharedPreferences(Landroid/content/Context;)Landroid/content/SharedPreferences;

    move-result-object v0

    .line 181
    invoke-interface {v0}, Landroid/content/SharedPreferences;->edit()Landroid/content/SharedPreferences$Editor;

    move-result-object v0

    .line 182
    iget-object v1, p0, Lme/embodied/services/XMOSDFU;->sources_:Lme/embodied/services/XMOSDFU$XMOSVariants;

    iget-object v1, v1, Lme/embodied/services/XMOSDFU$XMOSVariants;->version_:Ljava/lang/String;

    invoke-static {v1}, Ljava/lang/String;->valueOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    const-string v2, "xmos_version"

    invoke-interface {v0, v2, v1}, Landroid/content/SharedPreferences$Editor;->putString(Ljava/lang/String;Ljava/lang/String;)Landroid/content/SharedPreferences$Editor;

    .line 183
    invoke-interface {v0}, Landroid/content/SharedPreferences$Editor;->apply()V

    .line 184
    return-void
.end method

.method public isXmosUpdateRequired()Z
    .locals 5

    .line 155
    iget-object v0, p0, Lme/embodied/services/XMOSDFU;->sources_:Lme/embodied/services/XMOSDFU$XMOSVariants;

    iget-object v0, v0, Lme/embodied/services/XMOSDFU$XMOSVariants;->version_:Ljava/lang/String;

    const/4 v1, 0x0

    const-string v2, "XMOSDFU"

    if-nez v0, :cond_0

    .line 156
    const-string v0, "XMOS OS version unknown, not updating."

    invoke-static {v2, v0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 157
    return v1

    .line 159
    :cond_0
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "XMOS Version (OS): "

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v3, p0, Lme/embodied/services/XMOSDFU;->sources_:Lme/embodied/services/XMOSDFU$XMOSVariants;

    iget-object v3, v3, Lme/embodied/services/XMOSDFU$XMOSVariants;->version_:Ljava/lang/String;

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v2, v0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 162
    invoke-static {p0}, Landroid/preference/PreferenceManager;->getDefaultSharedPreferences(Landroid/content/Context;)Landroid/content/SharedPreferences;

    move-result-object v0

    .line 163
    const-string v3, "xmos_version"

    const-string v4, ""

    invoke-interface {v0, v3, v4}, Landroid/content/SharedPreferences;->getString(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    .line 164
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "XMOS Version (installed): "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-static {v2, v3}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 166
    iget-object v2, p0, Lme/embodied/services/XMOSDFU;->sources_:Lme/embodied/services/XMOSDFU$XMOSVariants;

    iget-object v2, v2, Lme/embodied/services/XMOSDFU$XMOSVariants;->version_:Ljava/lang/String;

    invoke-virtual {v0, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_1

    .line 167
    const/4 v0, 0x1

    return v0

    .line 170
    :cond_1
    return v1
.end method

.method public onBind(Landroid/content/Intent;)Landroid/os/IBinder;
    .locals 1

    .line 98
    const-string p1, "XMOSDFU"

    const-string v0, "onBind"

    invoke-static {p1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 99
    const/4 p1, 0x0

    return-object p1
.end method

.method public onDestroy()V
    .locals 2

    .line 118
    const-string v0, "XMOSDFU"

    const-string v1, "XMOSDFU exit"

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 119
    invoke-super {p0}, Landroid/app/Service;->onDestroy()V

    .line 120
    return-void
.end method

.method public onStartCommand(Landroid/content/Intent;II)I
    .locals 0

    .line 104
    const-string p1, "XMOSDFU"

    const-string p2, "Checking for XMOS Update"

    invoke-static {p1, p2}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 105
    invoke-virtual {p0}, Lme/embodied/services/XMOSDFU;->isXmosUpdateRequired()Z

    move-result p2

    if-eqz p2, :cond_0

    .line 106
    const-string p2, "Requesting XMOS Update"

    invoke-static {p1, p2}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 107
    invoke-virtual {p0}, Lme/embodied/services/XMOSDFU;->RequestUpdateXMOS()V

    goto :goto_0

    .line 109
    :cond_0
    const-string p2, "XMOS Update not required"

    invoke-static {p1, p2}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 110
    invoke-direct {p0}, Lme/embodied/services/XMOSDFU;->postOtaCheckComplete()V

    .line 113
    :goto_0
    const/4 p1, 0x1

    return p1
.end method
