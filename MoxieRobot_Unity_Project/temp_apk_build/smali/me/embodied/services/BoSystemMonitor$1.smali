.class Lme/embodied/services/BoSystemMonitor$1;
.super Ljava/lang/Thread;
.source "BoSystemMonitor.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lme/embodied/services/BoSystemMonitor;->handleWifiState(Z)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lme/embodied/services/BoSystemMonitor;


# direct methods
.method constructor <init>(Lme/embodied/services/BoSystemMonitor;)V
    .locals 0

    .line 174
    iput-object p1, p0, Lme/embodied/services/BoSystemMonitor$1;->this$0:Lme/embodied/services/BoSystemMonitor;

    invoke-direct {p0}, Ljava/lang/Thread;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 3

    .line 177
    const-wide/16 v0, 0x1f4

    :try_start_0
    invoke-static {v0, v1}, Ljava/lang/Thread;->sleep(J)V

    .line 178
    iget-object v0, p0, Lme/embodied/services/BoSystemMonitor$1;->this$0:Lme/embodied/services/BoSystemMonitor;

    invoke-static {v0}, Lme/embodied/NetworkUtil;->attemptWifiRecovery(Landroid/content/Context;)V
    :try_end_0
    .catch Ljava/lang/InterruptedException; {:try_start_0 .. :try_end_0} :catch_0

    .line 181
    goto :goto_0

    .line 179
    :catch_0
    move-exception v0

    .line 180
    const-string v1, "bo-system-monitor"

    const-string v2, "Background wifi reconnect aborted!"

    invoke-static {v1, v2, v0}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)I

    .line 182
    :goto_0
    iget-object v0, p0, Lme/embodied/services/BoSystemMonitor$1;->this$0:Lme/embodied/services/BoSystemMonitor;

    const/4 v1, 0x0

    iput-object v1, v0, Lme/embodied/services/BoSystemMonitor;->wifi_recovery_thread_:Ljava/lang/Thread;

    .line 183
    return-void
.end method
