.class Lme/embodied/services/Launcher$4;
.super Ljava/lang/Thread;
.source "Launcher.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lme/embodied/services/Launcher;->cleanLegacyFilesRestart()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lme/embodied/services/Launcher;


# direct methods
.method constructor <init>(Lme/embodied/services/Launcher;)V
    .locals 0

    .line 1113
    iput-object p1, p0, Lme/embodied/services/Launcher$4;->this$0:Lme/embodied/services/Launcher;

    invoke-direct {p0}, Ljava/lang/Thread;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 5

    .line 1115
    invoke-static {}, Lme/embodied/services/Launcher;->access$700()Ljava/io/File;

    move-result-object v0

    const/4 v1, 0x0

    invoke-static {v0, v1}, Lme/embodied/DiskCleanup;->recursivePurge(Ljava/io/File;Z)J

    move-result-wide v0

    .line 1116
    const-wide/16 v2, 0x0

    cmp-long v2, v0, v2

    const-string v3, "bo-launcher-j"

    if-lez v2, :cond_0

    .line 1118
    :try_start_0
    invoke-static {}, Ljava/lang/Runtime;->getRuntime()Ljava/lang/Runtime;

    move-result-object v2

    const-string v4, "sync"

    invoke-virtual {v2, v4}, Ljava/lang/Runtime;->exec(Ljava/lang/String;)Ljava/lang/Process;
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    .line 1121
    goto :goto_0

    .line 1119
    :catch_0
    move-exception v2

    .line 1120
    const-string v4, "Failed to execute \'sync\' operation"

    invoke-static {v3, v4, v2}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)I

    .line 1122
    :goto_0
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "BO#8012 Purged legacy files ("

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v0, v1}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    const-string v0, " bytes)."

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v3, v0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_1

    .line 1124
    :cond_0
    const-string v0, "BO#8012 No legacy files found to purge.  Rebooting anyway."

    invoke-static {v3, v0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1126
    :goto_1
    iget-object v0, p0, Lme/embodied/services/Launcher$4;->this$0:Lme/embodied/services/Launcher;

    invoke-static {v0}, Lme/embodied/services/Launcher;->access$800(Lme/embodied/services/Launcher;)Lme/embodied/ServiceMetrics;

    move-result-object v0

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Lme/embodied/ServiceMetrics;->onExternalError(I)V

    .line 1127
    return-void
.end method
