.class Lme/embodied/services/Launcher$3;
.super Ljava/lang/Thread;
.source "Launcher.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lme/embodied/services/Launcher;->startDemoMode()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lme/embodied/services/Launcher;


# direct methods
.method constructor <init>(Lme/embodied/services/Launcher;)V
    .locals 0

    .line 893
    iput-object p1, p0, Lme/embodied/services/Launcher$3;->this$0:Lme/embodied/services/Launcher;

    invoke-direct {p0}, Ljava/lang/Thread;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 2

    .line 895
    const-string v0, "bo-launcher-j"

    const-string v1, "Beginning demo sequence!"

    invoke-static {v0, v1}, Landroid/util/Log;->i(<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;)I

    .line 896
    invoke-static {}, Lme/embodied/services/Launcher;->access$600()V

    .line 897
    return-void
.end method
