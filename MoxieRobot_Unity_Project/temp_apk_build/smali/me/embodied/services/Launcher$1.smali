.class Lme/embodied/services/Launcher$1;
.super Ljava/lang/Object;
.source "Launcher.java"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lme/embodied/services/Launcher;->setAnimationState(Z)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lme/embodied/services/Launcher;

.field final synthetic val$state:Z


# direct methods
.method constructor <init>(Lme/embodied/services/Launcher;Z)V
    .locals 0

    .line 495
    iput-object p1, p0, Lme/embodied/services/Launcher$1;->this$0:Lme/embodied/services/Launcher;

    iput-boolean p2, p0, Lme/embodied/services/Launcher$1;->val$state:Z

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 2

    .line 497
    iget-object v0, p0, Lme/embodied/services/Launcher$1;->this$0:Lme/embodied/services/Launcher;

    iget-object v0, v0, Lme/embodied/services/Launcher;->animation_:Lme/embodied/TransitionAnimation;

    iget-boolean v1, p0, Lme/embodied/services/Launcher$1;->val$state:Z

    invoke-virtual {v0, v1}, Lme/embodied/TransitionAnimation;->setVisible(Z)V

    .line 498
    return-void
.end method
