.class final enum Lme/embodied/services/Launcher$BOComponent;
.super Ljava/lang/Enum;
.source "Launcher.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lme/embodied/services/Launcher;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4018
    name = "BOComponent"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lme/embodied/services/Launcher$BOComponent;",
        ">;"
    }
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lme/embodied/services/Launcher$BOComponent;

.field public static final enum BO_ANALYTICS:Lme/embodied/services/Launcher$BOComponent;

.field public static final enum BO_AUDIO:Lme/embodied/services/Launcher$BOComponent;

.field public static final enum BO_BRAIN:Lme/embodied/services/Launcher$BOComponent;

.field public static final enum BO_CFGAPP:Lme/embodied/services/Launcher$BOComponent;

.field public static final enum BO_DISPATCH:Lme/embodied/services/Launcher$BOComponent;

.field public static final enum BO_FUSION:Lme/embodied/services/Launcher$BOComponent;

.field public static final enum BO_LOGGER:Lme/embodied/services/Launcher$BOComponent;

.field public static final enum BO_MAINAPP:Lme/embodied/services/Launcher$BOComponent;

.field public static final enum BO_SYSMON:Lme/embodied/services/Launcher$BOComponent;

.field public static final enum BO_UPDATER:Lme/embodied/services/Launcher$BOComponent;

.field public static final enum BO_VISION:Lme/embodied/services/Launcher$BOComponent;

.field public static final enum BO_XMOS_WD:Lme/embodied/services/Launcher$BOComponent;


# instance fields
.field component_:Landroid/content/ComponentName;

.field delayAfterStop_:J

.field error_uid_:I

.field isService_:Z


# direct methods
.method static constructor <clinit>()V
    .locals 17

    .line 202
    new-instance v0, Lme/embodied/services/Launcher$BOComponent;

    const-class v1, Lme/embodied/services/BoDispatch;

    const-string v2, "BO_DISPATCH"

    const/4 v3, 0x0

    invoke-direct {v0, v2, v3, v1}, Lme/embodied/services/Launcher$BOComponent;-><init>(Ljava/lang/String;ILjava/lang/Class;)V

    sput-object v0, Lme/embodied/services/Launcher$BOComponent;->BO_DISPATCH:Lme/embodied/services/Launcher$BOComponent;

    .line 203
    new-instance v0, Lme/embodied/services/Launcher$BOComponent;

    const-class v1, Lme/embodied/services/BoLogger;

    const-string v2, "BO_LOGGER"

    const/4 v4, 0x1

    invoke-direct {v0, v2, v4, v1}, Lme/embodied/services/Launcher$BOComponent;-><init>(Ljava/lang/String;ILjava/lang/Class;)V

    sput-object v0, Lme/embodied/services/Launcher$BOComponent;->BO_LOGGER:Lme/embodied/services/Launcher$BOComponent;

    .line 204
    new-instance v0, Lme/embodied/services/Launcher$BOComponent;

    const-class v1, Lme/embodied/services/BoSystemMonitor;

    const-string v2, "BO_SYSMON"

    const/4 v5, 0x2

    invoke-direct {v0, v2, v5, v1}, Lme/embodied/services/Launcher$BOComponent;-><init>(Ljava/lang/String;ILjava/lang/Class;)V

    sput-object v0, Lme/embodied/services/Launcher$BOComponent;->BO_SYSMON:Lme/embodied/services/Launcher$BOComponent;

    .line 205
    new-instance v0, Lme/embodied/services/Launcher$BOComponent;

    const-class v1, Lme/embodied/services/BoBrain;

    const-string v2, "BO_BRAIN"

    const/4 v6, 0x3

    invoke-direct {v0, v2, v6, v1}, Lme/embodied/services/Launcher$BOComponent;-><init>(Ljava/lang/String;ILjava/lang/Class;)V

    sput-object v0, Lme/embodied/services/Launcher$BOComponent;->BO_BRAIN:Lme/embodied/services/Launcher$BOComponent;

    .line 206
    new-instance v0, Lme/embodied/services/Launcher$BOComponent;

    const-class v1, Lme/embodied/services/BoAudio;

    const-string v2, "BO_AUDIO"

    const/4 v7, 0x4

    invoke-direct {v0, v2, v7, v1}, Lme/embodied/services/Launcher$BOComponent;-><init>(Ljava/lang/String;ILjava/lang/Class;)V

    sput-object v0, Lme/embodied/services/Launcher$BOComponent;->BO_AUDIO:Lme/embodied/services/Launcher$BOComponent;

    .line 207
    new-instance v0, Lme/embodied/services/Launcher$BOComponent;

    const-class v1, Lme/embodied/services/BoVision;

    const-string v2, "BO_VISION"

    const/4 v8, 0x5

    invoke-direct {v0, v2, v8, v1}, Lme/embodied/services/Launcher$BOComponent;-><init>(Ljava/lang/String;ILjava/lang/Class;)V

    sput-object v0, Lme/embodied/services/Launcher$BOComponent;->BO_VISION:Lme/embodied/services/Launcher$BOComponent;

    .line 208
    new-instance v0, Lme/embodied/services/Launcher$BOComponent;

    const-class v1, Lme/embodied/services/BoFusion;

    const-string v2, "BO_FUSION"

    const/4 v9, 0x6

    invoke-direct {v0, v2, v9, v1}, Lme/embodied/services/Launcher$BOComponent;-><init>(Ljava/lang/String;ILjava/lang/Class;)V

    sput-object v0, Lme/embodied/services/Launcher$BOComponent;->BO_FUSION:Lme/embodied/services/Launcher$BOComponent;

    .line 209
    new-instance v0, Lme/embodied/services/Launcher$BOComponent;

    const-class v1, Lme/embodied/services/BoUpdater;

    const-string v2, "BO_UPDATER"

    const/4 v10, 0x7

    invoke-direct {v0, v2, v10, v1}, Lme/embodied/services/Launcher$BOComponent;-><init>(Ljava/lang/String;ILjava/lang/Class;)V

    sput-object v0, Lme/embodied/services/Launcher$BOComponent;->BO_UPDATER:Lme/embodied/services/Launcher$BOComponent;

    .line 210
    new-instance v0, Lme/embodied/services/Launcher$BOComponent;

    const-class v14, Lme/embodied/services/BoAnalytics;

    const-string v12, "BO_ANALYTICS"

    const/16 v13, 0x8

    const-wide/16 v15, 0xfa

    move-object v11, v0

    invoke-direct/range {v11 .. v16}, Lme/embodied/services/Launcher$BOComponent;-><init>(Ljava/lang/String;ILjava/lang/Class;J)V

    sput-object v0, Lme/embodied/services/Launcher$BOComponent;->BO_ANALYTICS:Lme/embodied/services/Launcher$BOComponent;

    .line 211
    new-instance v0, Lme/embodied/services/Launcher$BOComponent;

    const-class v1, Lme/embodied/services/BoXmosWatchdog;

    const-string v2, "BO_XMOS_WD"

    const/16 v11, 0x9

    invoke-direct {v0, v2, v11, v1}, Lme/embodied/services/Launcher$BOComponent;-><init>(Ljava/lang/String;ILjava/lang/Class;)V

    sput-object v0, Lme/embodied/services/Launcher$BOComponent;->BO_XMOS_WD:Lme/embodied/services/Launcher$BOComponent;

    .line 213
    new-instance v0, Lme/embodied/services/Launcher$BOComponent;

    const-string v1, "BO_MAINAPP"

    const/16 v2, 0xa

    const-string v12, "com.embodied.bo_unity"

    invoke-direct {v0, v1, v2, v12}, Lme/embodied/services/Launcher$BOComponent;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v0, Lme/embodied/services/Launcher$BOComponent;->BO_MAINAPP:Lme/embodied/services/Launcher$BOComponent;

    .line 214
    new-instance v0, Lme/embodied/services/Launcher$BOComponent;

    const-string v1, "BO_CFGAPP"

    const/16 v12, 0xb

    const-string v13, "com.embodied.bo_unity_wifi"

    invoke-direct {v0, v1, v12, v13}, Lme/embodied/services/Launcher$BOComponent;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    sput-object v0, Lme/embodied/services/Launcher$BOComponent;->BO_CFGAPP:Lme/embodied/services/Launcher$BOComponent;

    .line 200
    const/16 v1, 0xc

    new-array v1, v1, [Lme/embodied/services/Launcher$BOComponent;

    sget-object v13, Lme/embodied/services/Launcher$BOComponent;->BO_DISPATCH:Lme/embodied/services/Launcher$BOComponent;

    aput-object v13, v1, v3

    sget-object v3, Lme/embodied/services/Launcher$BOComponent;->BO_LOGGER:Lme/embodied/services/Launcher$BOComponent;

    aput-object v3, v1, v4

    sget-object v3, Lme/embodied/services/Launcher$BOComponent;->BO_SYSMON:Lme/embodied/services/Launcher$BOComponent;

    aput-object v3, v1, v5

    sget-object v3, Lme/embodied/services/Launcher$BOComponent;->BO_BRAIN:Lme/embodied/services/Launcher$BOComponent;

    aput-object v3, v1, v6

    sget-object v3, Lme/embodied/services/Launcher$BOComponent;->BO_AUDIO:Lme/embodied/services/Launcher$BOComponent;

    aput-object v3, v1, v7

    sget-object v3, Lme/embodied/services/Launcher$BOComponent;->BO_VISION:Lme/embodied/services/Launcher$BOComponent;

    aput-object v3, v1, v8

    sget-object v3, Lme/embodied/services/Launcher$BOComponent;->BO_FUSION:Lme/embodied/services/Launcher$BOComponent;

    aput-object v3, v1, v9

    sget-object v3, Lme/embodied/services/Launcher$BOComponent;->BO_UPDATER:Lme/embodied/services/Launcher$BOComponent;

    aput-object v3, v1, v10

    sget-object v3, Lme/embodied/services/Launcher$BOComponent;->BO_ANALYTICS:Lme/embodied/services/Launcher$BOComponent;

    const/16 v4, 0x8

    aput-object v3, v1, v4

    sget-object v3, Lme/embodied/services/Launcher$BOComponent;->BO_XMOS_WD:Lme/embodied/services/Launcher$BOComponent;

    aput-object v3, v1, v11

    sget-object v3, Lme/embodied/services/Launcher$BOComponent;->BO_MAINAPP:Lme/embodied/services/Launcher$BOComponent;

    aput-object v3, v1, v2

    aput-object v0, v1, v12

    sput-object v1, Lme/embodied/services/Launcher$BOComponent;->$VALUES:[Lme/embodied/services/Launcher$BOComponent;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;ILjava/lang/Class;)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Class;",
            ")V"
        }
    .end annotation

    .line 226
    const-wide/16 v4, 0x7d0

    move-object v0, p0

    move-object v1, p1

    move v2, p2

    move-object v3, p3

    invoke-direct/range {v0 .. v5}, Lme/embodied/services/Launcher$BOComponent;-><init>(Ljava/lang/String;ILjava/lang/Class;J)V

    .line 227
    return-void
.end method

.method private constructor <init>(Ljava/lang/String;ILjava/lang/Class;J)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Class;",
            "J)V"
        }
    .end annotation

    .line 228
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 229
    new-instance p1, Landroid/content/ComponentName;

    invoke-virtual {p3}, Ljava/lang/Class;->getCanonicalName()Ljava/lang/String;

    move-result-object p2

    const-string p3, "com.embodied.bo_unity"

    invoke-direct {p1, p3, p2}, Landroid/content/ComponentName;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    iput-object p1, p0, Lme/embodied/services/Launcher$BOComponent;->component_:Landroid/content/ComponentName;

    .line 230
    const/4 p1, 0x1

    iput-boolean p1, p0, Lme/embodied/services/Launcher$BOComponent;->isService_:Z

    .line 231
    iput-wide p4, p0, Lme/embodied/services/Launcher$BOComponent;->delayAfterStop_:J

    .line 232
    return-void
.end method

.method private constructor <init>(Ljava/lang/String;ILjava/lang/String;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    .line 220
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 221
    new-instance p1, Landroid/content/ComponentName;

    const-string p2, "com.unity3d.player.UnityPlayerActivity"

    invoke-direct {p1, p3, p2}, Landroid/content/ComponentName;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    iput-object p1, p0, Lme/embodied/services/Launcher$BOComponent;->component_:Landroid/content/ComponentName;

    .line 222
    const/4 p1, 0x0

    iput-boolean p1, p0, Lme/embodied/services/Launcher$BOComponent;->isService_:Z

    .line 223
    const-wide/16 p1, 0x7d0

    iput-wide p1, p0, Lme/embodied/services/Launcher$BOComponent;->delayAfterStop_:J

    .line 224
    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lme/embodied/services/Launcher$BOComponent;
    .locals 1

    .line 200
    const-class v0, Lme/embodied/services/Launcher$BOComponent;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lme/embodied/services/Launcher$BOComponent;

    return-object p0
.end method

.method public static values()[Lme/embodied/services/Launcher$BOComponent;
    .locals 1

    .line 200
    sget-object v0, Lme/embodied/services/Launcher$BOComponent;->$VALUES:[Lme/embodied/services/Launcher$BOComponent;

    invoke-virtual {v0}, [Lme/embodied/services/Launcher$BOComponent;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lme/embodied/services/Launcher$BOComponent;

    return-object v0
.end method


# virtual methods
.method public delayAfterStop()J
    .locals 2

    .line 240
    iget-wide v0, p0, Lme/embodied/services/Launcher$BOComponent;->delayAfterStop_:J

    return-wide v0
.end method

.method public getComponentName()Landroid/content/ComponentName;
    .locals 1

    .line 234
    iget-object v0, p0, Lme/embodied/services/Launcher$BOComponent;->component_:Landroid/content/ComponentName;

    return-object v0
.end method

.method public isService()Z
    .locals 1

    .line 237
    iget-boolean v0, p0, Lme/embodied/services/Launcher$BOComponent;->isService_:Z

    return v0
.end method
