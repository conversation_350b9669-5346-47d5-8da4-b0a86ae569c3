.class final enum Lme/embodied/services/XMOSDFU$XmosState;
.super Ljava/lang/Enum;
.source "XMOSDFU.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lme/embodied/services/XMOSDFU;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4018
    name = "XmosState"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lme/embodied/services/XMOSDFU$XmosState;",
        ">;"
    }
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lme/embodied/services/XMOSDFU$XmosState;

.field public static final enum XMOS_FAIL:Lme/embodied/services/XMOSDFU$XmosState;

.field public static final enum XMOS_IDLE:Lme/embodied/services/XMOSDFU$XmosState;

.field public static final enum XMOS_PASS:Lme/embodied/services/XMOSDFU$XmosState;

.field public static final enum XMOS_RUNNING:Lme/embodied/services/XMOSDFU$XmosState;


# direct methods
.method static constructor <clinit>()V
    .locals 7

    .line 286
    new-instance v0, Lme/embodied/services/XMOSDFU$XmosState;

    const-string v1, "XMOS_IDLE"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lme/embodied/services/XMOSDFU$XmosState;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lme/embodied/services/XMOSDFU$XmosState;->XMOS_IDLE:Lme/embodied/services/XMOSDFU$XmosState;

    .line 287
    new-instance v0, Lme/embodied/services/XMOSDFU$XmosState;

    const-string v1, "XMOS_RUNNING"

    const/4 v3, 0x1

    invoke-direct {v0, v1, v3}, Lme/embodied/services/XMOSDFU$XmosState;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lme/embodied/services/XMOSDFU$XmosState;->XMOS_RUNNING:Lme/embodied/services/XMOSDFU$XmosState;

    .line 288
    new-instance v0, Lme/embodied/services/XMOSDFU$XmosState;

    const-string v1, "XMOS_PASS"

    const/4 v4, 0x2

    invoke-direct {v0, v1, v4}, Lme/embodied/services/XMOSDFU$XmosState;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lme/embodied/services/XMOSDFU$XmosState;->XMOS_PASS:Lme/embodied/services/XMOSDFU$XmosState;

    .line 289
    new-instance v0, Lme/embodied/services/XMOSDFU$XmosState;

    const-string v1, "XMOS_FAIL"

    const/4 v5, 0x3

    invoke-direct {v0, v1, v5}, Lme/embodied/services/XMOSDFU$XmosState;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lme/embodied/services/XMOSDFU$XmosState;->XMOS_FAIL:Lme/embodied/services/XMOSDFU$XmosState;

    .line 285
    const/4 v1, 0x4

    new-array v1, v1, [Lme/embodied/services/XMOSDFU$XmosState;

    sget-object v6, Lme/embodied/services/XMOSDFU$XmosState;->XMOS_IDLE:Lme/embodied/services/XMOSDFU$XmosState;

    aput-object v6, v1, v2

    sget-object v2, Lme/embodied/services/XMOSDFU$XmosState;->XMOS_RUNNING:Lme/embodied/services/XMOSDFU$XmosState;

    aput-object v2, v1, v3

    sget-object v2, Lme/embodied/services/XMOSDFU$XmosState;->XMOS_PASS:Lme/embodied/services/XMOSDFU$XmosState;

    aput-object v2, v1, v4

    aput-object v0, v1, v5

    sput-object v1, Lme/embodied/services/XMOSDFU$XmosState;->$VALUES:[Lme/embodied/services/XMOSDFU$XmosState;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 285
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lme/embodied/services/XMOSDFU$XmosState;
    .locals 1

    .line 285
    const-class v0, Lme/embodied/services/XMOSDFU$XmosState;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lme/embodied/services/XMOSDFU$XmosState;

    return-object p0
.end method

.method public static values()[Lme/embodied/services/XMOSDFU$XmosState;
    .locals 1

    .line 285
    sget-object v0, Lme/embodied/services/XMOSDFU$XmosState;->$VALUES:[Lme/embodied/services/XMOSDFU$XmosState;

    invoke-virtual {v0}, [Lme/embodied/services/XMOSDFU$XmosState;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lme/embodied/services/XMOSDFU$XmosState;

    return-object v0
.end method
