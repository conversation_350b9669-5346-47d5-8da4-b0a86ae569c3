.class public Lme/embodied/services/BoXmosWatchdog;
.super Lme/embodied/services/Service;
.source "BoXmosWatchdog.java"


# direct methods
.method public constructor <init>()V
    .locals 4

    .line 8
    const-string v0, "bo-xmos-wd"

    const-wide/16 v1, 0x0

    const-string v3, "bo-audio"

    invoke-direct {p0, v0, v1, v2, v3}, Lme/embodied/services/Service;-><init>(Ljava/lang/String;JLjava/lang/String;)V

    .line 9
    return-void
.end method
