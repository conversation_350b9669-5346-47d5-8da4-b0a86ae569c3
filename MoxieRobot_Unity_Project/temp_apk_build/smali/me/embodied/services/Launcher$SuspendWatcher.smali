.class Lme/embodied/services/Launcher$SuspendWatcher;
.super Ljava/lang/Object;
.source "Launcher.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lme/embodied/services/Launcher;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = "SuspendWatcher"
.end annotation


# instance fields
.field runner_:Ljava/lang/Thread;

.field final synthetic this$0:Lme/embodied/services/Launcher;

.field wakeLock_:Landroid/os/PowerManager$WakeLock;

.field wifiLock_:Landroid/net/wifi/WifiManager$WifiLock;


# direct methods
.method constructor <init>(Lme/embodied/services/Launcher;)V
    .locals 0

    .line 1154
    iput-object p1, p0, Lme/embodied/services/Launcher$SuspendWatcher;->this$0:Lme/embodied/services/Launcher;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method private wakeDetected()V
    .locals 2

    .line 1211
    monitor-enter p0

    .line 1212
    const/4 v0, 0x0

    :try_start_0
    iput-object v0, p0, Lme/embodied/services/Launcher$SuspendWatcher;->runner_:Ljava/lang/Thread;

    .line 1213
    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 1214
    iget-object v0, p0, Lme/embodied/services/Launcher$SuspendWatcher;->this$0:Lme/embodied/services/Launcher;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Lme/embodied/services/Launcher;->setAlarmWakeup(Z)V

    .line 1216
    iget-object v0, p0, Lme/embodied/services/Launcher$SuspendWatcher;->this$0:Lme/embodied/services/Launcher;

    invoke-virtual {v0, v1}, Lme/embodied/services/Launcher;->OnWakeSignal(Z)V

    .line 1217
    return-void

    .line 1213
    :catchall_0
    move-exception v0

    :try_start_1
    monitor-exit p0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw v0
.end method


# virtual methods
.method public declared-synchronized cancelWatch()V
    .locals 1

    monitor-enter p0

    .line 1203
    :try_start_0
    iget-object v0, p0, Lme/embodied/services/Launcher$SuspendWatcher;->runner_:Ljava/lang/Thread;

    if-eqz v0, :cond_0

    .line 1204
    iget-object v0, p0, Lme/embodied/services/Launcher$SuspendWatcher;->runner_:Ljava/lang/Thread;

    invoke-virtual {v0}, Ljava/lang/Thread;->interrupt()V

    .line 1205
    const/4 v0, 0x0

    iput-object v0, p0, Lme/embodied/services/Launcher$SuspendWatcher;->runner_:Ljava/lang/Thread;

    .line 1207
    :cond_0
    const/4 v0, 0x0

    invoke-virtual {p0, v0}, Lme/embodied/services/Launcher$SuspendWatcher;->keepAwake(Z)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 1208
    monitor-exit p0

    return-void

    .line 1202
    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method public keepAwake(Z)V
    .locals 4

    .line 1172
    iget-object v0, p0, Lme/embodied/services/Launcher$SuspendWatcher;->this$0:Lme/embodied/services/Launcher;

    invoke-virtual {v0}, Lme/embodied/services/Launcher;->SupportsLightSleep()Z

    move-result v0

    const-string v1, "bo-launcher-j"

    if-nez v0, :cond_0

    .line 1174
    const-string p1, "Ignoring Wakelock change.  Light sleep is not supported."

    invoke-static {v1, p1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 1175
    return-void

    .line 1178
    :cond_0
    const/4 v0, 0x1

    if-eqz p1, :cond_1

    iget-object v2, p0, Lme/embodied/services/Launcher$SuspendWatcher;->wakeLock_:Landroid/os/PowerManager$WakeLock;

    if-nez v2, :cond_1

    .line 1179
    const-string p1, "Acquiring Wakelock and WifiLock to prevent suspend and keep data alive."

    invoke-static {v1, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1180
    iget-object p1, p0, Lme/embodied/services/Launcher$SuspendWatcher;->this$0:Lme/embodied/services/Launcher;

    invoke-static {p1}, Lme/embodied/services/Launcher;->access$900(Lme/embodied/services/Launcher;)Landroid/content/Context;

    move-result-object p1

    iget-object v2, p0, Lme/embodied/services/Launcher$SuspendWatcher;->this$0:Lme/embodied/services/Launcher;

    invoke-static {v2}, Lme/embodied/services/Launcher;->access$900(Lme/embodied/services/Launcher;)Landroid/content/Context;

    const-string v2, "power"

    invoke-virtual {p1, v2}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroid/os/PowerManager;

    .line 1181
    const-string v2, "BOLauncher::LightSleep"

    invoke-virtual {p1, v0, v2}, Landroid/os/PowerManager;->newWakeLock(ILjava/lang/String;)Landroid/os/PowerManager$WakeLock;

    move-result-object p1

    iput-object p1, p0, Lme/embodied/services/Launcher$SuspendWatcher;->wakeLock_:Landroid/os/PowerManager$WakeLock;

    .line 1182
    invoke-virtual {p1}, Landroid/os/PowerManager$WakeLock;->acquire()V

    .line 1184
    iget-object p1, p0, Lme/embodied/services/Launcher$SuspendWatcher;->this$0:Lme/embodied/services/Launcher;

    invoke-static {p1}, Lme/embodied/services/Launcher;->access$900(Lme/embodied/services/Launcher;)Landroid/content/Context;

    move-result-object p1

    iget-object v3, p0, Lme/embodied/services/Launcher$SuspendWatcher;->this$0:Lme/embodied/services/Launcher;

    invoke-static {v3}, Lme/embodied/services/Launcher;->access$900(Lme/embodied/services/Launcher;)Landroid/content/Context;

    const-string v3, "wifi"

    invoke-virtual {p1, v3}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroid/net/wifi/WifiManager;

    .line 1185
    invoke-virtual {p1, v0, v2}, Landroid/net/wifi/WifiManager;->createWifiLock(ILjava/lang/String;)Landroid/net/wifi/WifiManager$WifiLock;

    move-result-object p1

    iput-object p1, p0, Lme/embodied/services/Launcher$SuspendWatcher;->wifiLock_:Landroid/net/wifi/WifiManager$WifiLock;

    .line 1186
    invoke-virtual {p1}, Landroid/net/wifi/WifiManager$WifiLock;->acquire()V

    .line 1187
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "Locks held: Wake="

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v0, p0, Lme/embodied/services/Launcher$SuspendWatcher;->wakeLock_:Landroid/os/PowerManager$WakeLock;

    invoke-virtual {v0}, Landroid/os/PowerManager$WakeLock;->isHeld()Z

    move-result v0

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    const-string v0, ", Wifi="

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v0, p0, Lme/embodied/services/Launcher$SuspendWatcher;->wifiLock_:Landroid/net/wifi/WifiManager$WifiLock;

    invoke-virtual {v0}, Landroid/net/wifi/WifiManager$WifiLock;->isHeld()Z

    move-result v0

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v1, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1188
    goto :goto_2

    :cond_1
    if-nez p1, :cond_2

    iget-object v2, p0, Lme/embodied/services/Launcher$SuspendWatcher;->wakeLock_:Landroid/os/PowerManager$WakeLock;

    if-eqz v2, :cond_2

    .line 1189
    iget-object p1, p0, Lme/embodied/services/Launcher$SuspendWatcher;->this$0:Lme/embodied/services/Launcher;

    invoke-virtual {p1, v0}, Lme/embodied/services/Launcher;->setAlarmWakeup(Z)V

    .line 1191
    iget-object p1, p0, Lme/embodied/services/Launcher$SuspendWatcher;->wakeLock_:Landroid/os/PowerManager$WakeLock;

    invoke-virtual {p1}, Landroid/os/PowerManager$WakeLock;->release()V

    .line 1192
    const/4 p1, 0x0

    iput-object p1, p0, Lme/embodied/services/Launcher$SuspendWatcher;->wakeLock_:Landroid/os/PowerManager$WakeLock;

    .line 1194
    iget-object v0, p0, Lme/embodied/services/Launcher$SuspendWatcher;->wifiLock_:Landroid/net/wifi/WifiManager$WifiLock;

    invoke-virtual {v0}, Landroid/net/wifi/WifiManager$WifiLock;->release()V

    .line 1195
    iput-object p1, p0, Lme/embodied/services/Launcher$SuspendWatcher;->wifiLock_:Landroid/net/wifi/WifiManager$WifiLock;

    .line 1196
    const-string p1, "Released Wakelock and WifiLock!"

    invoke-static {v1, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_2

    .line 1198
    :cond_2
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "WakeLock - No Change - KeepAwake: "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    if-eqz p1, :cond_3

    const-string p1, "YES"

    goto :goto_0

    :cond_3
    const-string p1, "NO"

    :goto_0
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p1, " - WakeLock: "

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object p1, p0, Lme/embodied/services/Launcher$SuspendWatcher;->wakeLock_:Landroid/os/PowerManager$WakeLock;

    if-nez p1, :cond_4

    const-string p1, "null"

    goto :goto_1

    :cond_4
    const-string p1, "valid"

    :goto_1
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v1, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1200
    :goto_2
    return-void
.end method

.method public run_loop()V
    .locals 13

    .line 1220
    const-string v0, "Signaling system resume."

    const-string v1, "bo-launcher-j"

    const-string v2, "Entering suspend loop."

    invoke-static {v1, v2}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1221
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v2

    .line 1222
    iget-object v4, p0, Lme/embodied/services/Launcher$SuspendWatcher;->this$0:Lme/embodied/services/Launcher;

    invoke-static {v4}, Lme/embodied/services/Launcher;->access$1000(Lme/embodied/services/Launcher;)V

    .line 1233
    :try_start_0
    iget-object v4, p0, Lme/embodied/services/Launcher$SuspendWatcher;->this$0:Lme/embodied/services/Launcher;

    const/16 v5, 0x14

    const-wide/16 v6, 0x64

    const/4 v8, 0x0

    invoke-static {v4, v8, v5, v6, v7}, Lme/embodied/services/Launcher;->access$1100(Lme/embodied/services/Launcher;ZIJ)Z

    move-result v4

    .line 1234
    if-nez v4, :cond_0

    .line 1235
    const-string v2, "BO#8006 Display never turned off.  Aborting suspend attempt."

    invoke-static {v1, v2}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 1236
    invoke-direct {p0}, Lme/embodied/services/Launcher$SuspendWatcher;->wakeDetected()V
    :try_end_0
    .catch Ljava/lang/InterruptedException; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 1265
    invoke-static {v1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 1266
    iget-object v0, p0, Lme/embodied/services/Launcher$SuspendWatcher;->this$0:Lme/embodied/services/Launcher;

    invoke-static {v0}, Lme/embodied/services/Launcher;->access$1200(Lme/embodied/services/Launcher;)V

    .line 1237
    return-void

    .line 1239
    :cond_0
    :try_start_1
    const-string v4, "Display has turned off."

    invoke-static {v1, v4}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1242
    const/4 v4, 0x1

    move v5, v4

    .line 1245
    :goto_0
    iget-object v6, p0, Lme/embodied/services/Launcher$SuspendWatcher;->this$0:Lme/embodied/services/Launcher;

    const-wide/16 v9, 0x3e8

    const/16 v7, 0x2d

    invoke-static {v6, v4, v7, v9, v10}, Lme/embodied/services/Launcher;->access$1100(Lme/embodied/services/Launcher;ZIJ)Z

    move-result v6

    .line 1246
    if-nez v6, :cond_1

    .line 1247
    const-string v6, "Still running but display OFF.  Attempting to suspend again."

    invoke-static {v1, v6}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1248
    add-int/lit8 v5, v5, 0x1

    .line 1249
    iget-object v6, p0, Lme/embodied/services/Launcher$SuspendWatcher;->this$0:Lme/embodied/services/Launcher;

    invoke-static {v6}, Lme/embodied/services/Launcher;->access$1000(Lme/embodied/services/Launcher;)V

    .line 1257
    goto :goto_0

    .line 1252
    :cond_1
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v11

    sub-long/2addr v11, v2

    .line 1253
    mul-int/2addr v5, v7

    int-to-long v2, v5

    mul-long/2addr v2, v9

    .line 1254
    const-string v5, "Resumed from suspend.  Slept %d ms, was awake for at most %d ms."

    const/4 v6, 0x2

    new-array v6, v6, [Ljava/lang/Object;

    invoke-static {v11, v12}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v7

    aput-object v7, v6, v8

    invoke-static {v2, v3}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v2

    aput-object v2, v6, v4

    invoke-static {v5, v6}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1255
    nop

    .line 1260
    invoke-direct {p0}, Lme/embodied/services/Launcher$SuspendWatcher;->wakeDetected()V
    :try_end_1
    .catch Ljava/lang/InterruptedException; {:try_start_1 .. :try_end_1} :catch_0
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    goto :goto_1

    .line 1265
    :catchall_0
    move-exception v2

    goto :goto_2

    .line 1261
    :catch_0
    move-exception v2

    .line 1263
    :try_start_2
    const-string v2, "Suspend loop interrupted"

    invoke-static {v1, v2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    .line 1265
    :goto_1
    invoke-static {v1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 1266
    iget-object v0, p0, Lme/embodied/services/Launcher$SuspendWatcher;->this$0:Lme/embodied/services/Launcher;

    invoke-static {v0}, Lme/embodied/services/Launcher;->access$1200(Lme/embodied/services/Launcher;)V

    .line 1267
    nop

    .line 1268
    return-void

    .line 1265
    :goto_2
    invoke-static {v1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 1266
    iget-object v0, p0, Lme/embodied/services/Launcher$SuspendWatcher;->this$0:Lme/embodied/services/Launcher;

    invoke-static {v0}, Lme/embodied/services/Launcher;->access$1200(Lme/embodied/services/Launcher;)V

    .line 1267
    throw v2
.end method

.method public declared-synchronized startSuspendWatch(Z)V
    .locals 0

    monitor-enter p0

    .line 1160
    if-nez p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    :try_start_0
    invoke-virtual {p0, p1}, Lme/embodied/services/Launcher$SuspendWatcher;->keepAwake(Z)V

    .line 1161
    iget-object p1, p0, Lme/embodied/services/Launcher$SuspendWatcher;->runner_:Ljava/lang/Thread;

    if-nez p1, :cond_1

    .line 1162
    new-instance p1, Lme/embodied/services/Launcher$SuspendWatcher$1;

    invoke-direct {p1, p0}, Lme/embodied/services/Launcher$SuspendWatcher$1;-><init>(Lme/embodied/services/Launcher$SuspendWatcher;)V

    iput-object p1, p0, Lme/embodied/services/Launcher$SuspendWatcher;->runner_:Ljava/lang/Thread;

    .line 1167
    invoke-virtual {p1}, Ljava/lang/Thread;->start()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 1169
    :cond_1
    monitor-exit p0

    return-void

    .line 1159
    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method
