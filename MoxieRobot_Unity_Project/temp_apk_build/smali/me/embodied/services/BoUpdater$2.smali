.class Lme/embodied/services/BoUpdater$2;
.super Ljava/lang/Object;
.source "BoUpdater.java"

# interfaces
.implements Lme/embodied/services/BoUpdater$MonitoredInputStream$Monitor;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lme/embodied/services/BoUpdater;->CheckForUpdates()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field public hasStarted:Z

.field public lastNotify:F

.field final synthetic this$0:Lme/embodied/services/BoUpdater;

.field final synthetic val$finalFile:Ljava/io/File;


# direct methods
.method constructor <init>(Lme/embodied/services/BoUpdater;Ljava/io/File;)V
    .locals 0

    .line 329
    iput-object p1, p0, Lme/embodied/services/BoUpdater$2;->this$0:Lme/embodied/services/BoUpdater;

    iput-object p2, p0, Lme/embodied/services/BoUpdater$2;->val$finalFile:Ljava/io/File;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 330
    const/high16 p1, -0x40800000    # -1.0f

    iput p1, p0, Lme/embodied/services/BoUpdater$2;->lastNotify:F

    .line 331
    const/4 p1, 0x0

    iput-boolean p1, p0, Lme/embodied/services/BoUpdater$2;->hasStarted:Z

    return-void
.end method


# virtual methods
.method public notify(JJ)V
    .locals 0

    .line 333
    long-to-float p1, p1

    long-to-float p2, p3

    div-float/2addr p1, p2

    .line 334
    iget p2, p0, Lme/embodied/services/BoUpdater$2;->lastNotify:F

    sub-float p2, p1, p2

    const p3, 0x3c23d70a    # 0.01f

    cmpl-float p2, p2, p3

    if-ltz p2, :cond_0

    .line 335
    iget-object p2, p0, Lme/embodied/services/BoUpdater$2;->this$0:Lme/embodied/services/BoUpdater;

    invoke-static {p2, p1}, Lme/embodied/services/BoUpdater;->access$000(Lme/embodied/services/BoUpdater;F)V

    .line 336
    iput p1, p0, Lme/embodied/services/BoUpdater$2;->lastNotify:F

    .line 338
    :cond_0
    iget-boolean p1, p0, Lme/embodied/services/BoUpdater$2;->hasStarted:Z

    if-nez p1, :cond_1

    .line 339
    const/4 p1, 0x1

    iput-boolean p1, p0, Lme/embodied/services/BoUpdater$2;->hasStarted:Z

    .line 340
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string p2, "BO#3023 OTA download started for "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object p2, p0, Lme/embodied/services/BoUpdater$2;->val$finalFile:Ljava/io/File;

    invoke-virtual {p2}, Ljava/io/File;->getName()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const-string p2, "BoUpdater"

    invoke-static {p2, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 342
    :cond_1
    return-void
.end method
