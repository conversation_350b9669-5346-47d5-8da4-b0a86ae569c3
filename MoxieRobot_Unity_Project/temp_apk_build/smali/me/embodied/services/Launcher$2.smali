.class Lme/embodied/services/Launcher$2;
.super Ljava/lang/Object;
.source "Launcher.java"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lme/embodied/services/Launcher;->RequestState(Lme/embodied/services/Launcher$LauncherState;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lme/embodied/services/Launcher;

.field final synthetic val$prevState:Lme/embodied/services/Launcher$LauncherState;

.field final synthetic val$targetState:Lme/embodied/services/Launcher$LauncherState;


# direct methods
.method constructor <init>(Lme/embodied/services/Launcher;Lme/embodied/services/Launcher$LauncherState;Lme/embodied/services/Launcher$LauncherState;)V
    .locals 0

    .line 562
    iput-object p1, p0, Lme/embodied/services/Launcher$2;->this$0:Lme/embodied/services/Launcher;

    iput-object p2, p0, Lme/embodied/services/Launcher$2;->val$prevState:Lme/embodied/services/Launcher$LauncherState;

    iput-object p3, p0, Lme/embodied/services/Launcher$2;->val$targetState:Lme/embodied/services/Launcher$LauncherState;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 4

    .line 565
    const-string v0, "bo-launcher-j"

    :try_start_0
    iget-object v1, p0, Lme/embodied/services/Launcher$2;->this$0:Lme/embodied/services/Launcher;

    iget-object v2, p0, Lme/embodied/services/Launcher$2;->val$prevState:Lme/embodied/services/Launcher$LauncherState;

    iget-object v3, p0, Lme/embodied/services/Launcher$2;->val$targetState:Lme/embodied/services/Launcher$LauncherState;

    invoke-static {v1, v2, v3}, Lme/embodied/services/Launcher;->access$400(Lme/embodied/services/Launcher;Lme/embodied/services/Launcher$LauncherState;Lme/embodied/services/Launcher$LauncherState;)V
    :try_end_0
    .catch Ljava/lang/InterruptedException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Landroid/content/ActivityNotFoundException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    .line 568
    :catch_0
    move-exception v1

    .line 570
    iget-object v1, p0, Lme/embodied/services/Launcher$2;->val$targetState:Lme/embodied/services/Launcher$LauncherState;

    sget-object v2, Lme/embodied/services/Launcher$LauncherState;->STATE_CONFIG:Lme/embodied/services/Launcher$LauncherState;

    if-ne v1, v2, :cond_0

    .line 571
    const-string v1, "BO#8000 Unable to locate config activity:  Simulating Config Complete to Launch Main app."

    invoke-static {v0, v1}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 572
    iget-object v0, p0, Lme/embodied/services/Launcher$2;->this$0:Lme/embodied/services/Launcher;

    sget-object v1, Lme/embodied/services/Launcher$LauncherState;->STATE_STARTUP:Lme/embodied/services/Launcher$LauncherState;

    invoke-static {v0, v1}, Lme/embodied/services/Launcher;->access$500(Lme/embodied/services/Launcher;Lme/embodied/services/Launcher$LauncherState;)V

    goto :goto_1

    .line 576
    :cond_0
    const-string v1, "BO#8000 Unable to locate main activity.  Will recheck after checkin expires."

    invoke-static {v0, v1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_1

    .line 566
    :catch_1
    move-exception v1

    .line 567
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Interrupted transition from: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v2, p0, Lme/embodied/services/Launcher$2;->val$prevState:Lme/embodied/services/Launcher$LauncherState;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v2, " to "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v2, p0, Lme/embodied/services/Launcher$2;->val$targetState:Lme/embodied/services/Launcher$LauncherState;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 578
    :goto_0
    nop

    .line 579
    :goto_1
    return-void
.end method
