.class final enum Lme/embodied/services/Launcher$LauncherState;
.super Ljava/lang/Enum;
.source "Launcher.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lme/embodied/services/Launcher;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4018
    name = "LauncherState"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lme/embodied/services/Launcher$LauncherState;",
        ">;"
    }
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lme/embodied/services/Launcher$LauncherState;

.field public static final enum STATE_CONFIG:Lme/embodied/services/Launcher$LauncherState;

.field public static final enum STATE_DEMO:Lme/embodied/services/Launcher$LauncherState;

.field public static final enum STATE_INIT:Lme/embodied/services/Launcher$LauncherState;

.field public static final enum STATE_LIGHT_SLEEP:Lme/embodied/services/Launcher$LauncherState;

.field public static final enum STATE_RECOVERY:Lme/embodied/services/Launcher$LauncherState;

.field public static final enum STATE_RUNNING:Lme/embodied/services/Launcher$LauncherState;

.field public static final enum STATE_SHUTDOWN:Lme/embodied/services/Launcher$LauncherState;

.field public static final enum STATE_SILENT_REBOOT:Lme/embodied/services/Launcher$LauncherState;

.field public static final enum STATE_SILENT_RECOVERY:Lme/embodied/services/Launcher$LauncherState;

.field public static final enum STATE_STARTUP:Lme/embodied/services/Launcher$LauncherState;

.field public static final enum STATE_SUSPEND:Lme/embodied/services/Launcher$LauncherState;

.field public static final enum STATE_TELEBRAIN:Lme/embodied/services/Launcher$LauncherState;


# instance fields
.field active_components_:Ljava/util/HashSet;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/HashSet<",
            "Lme/embodied/services/Launcher$BOComponent;",
            ">;"
        }
    .end annotation
.end field

.field animation_on_start_:Lme/embodied/services/Launcher$AnimType;

.field filter_:Lme/embodied/services/Launcher$ComponentFilter;

.field on_state_final_:Ljava/lang/Runnable;

.field screen_active_:Z

.field suspend_state_:Z

.field terminal_state_:Z


# direct methods
.method static constructor <clinit>()V
    .locals 40

    .line 321
    new-instance v8, Lme/embodied/services/Launcher$LauncherState;

    const/4 v9, 0x0

    new-array v3, v9, [Lme/embodied/services/Launcher$BOComponent;

    sget-object v7, Lme/embodied/services/Launcher$AnimType;->ANIM_NONE:Lme/embodied/services/Launcher$AnimType;

    const-string v1, "STATE_INIT"

    const/4 v2, 0x0

    const/4 v4, 0x1

    const/4 v5, 0x0

    const/4 v6, 0x0

    move-object v0, v8

    invoke-direct/range {v0 .. v7}, Lme/embodied/services/Launcher$LauncherState;-><init>(Ljava/lang/String;I[Lme/embodied/services/Launcher$BOComponent;ZZZLme/embodied/services/Launcher$AnimType;)V

    sput-object v8, Lme/embodied/services/Launcher$LauncherState;->STATE_INIT:Lme/embodied/services/Launcher$LauncherState;

    .line 322
    new-instance v0, Lme/embodied/services/Launcher$LauncherState;

    const/4 v1, 0x5

    new-array v13, v1, [Lme/embodied/services/Launcher$BOComponent;

    sget-object v2, Lme/embodied/services/Launcher$BOComponent;->BO_DISPATCH:Lme/embodied/services/Launcher$BOComponent;

    aput-object v2, v13, v9

    sget-object v2, Lme/embodied/services/Launcher$BOComponent;->BO_LOGGER:Lme/embodied/services/Launcher$BOComponent;

    const/4 v3, 0x1

    aput-object v2, v13, v3

    sget-object v2, Lme/embodied/services/Launcher$BOComponent;->BO_SYSMON:Lme/embodied/services/Launcher$BOComponent;

    const/4 v4, 0x2

    aput-object v2, v13, v4

    sget-object v2, Lme/embodied/services/Launcher$BOComponent;->BO_UPDATER:Lme/embodied/services/Launcher$BOComponent;

    const/4 v5, 0x3

    aput-object v2, v13, v5

    sget-object v2, Lme/embodied/services/Launcher$BOComponent;->BO_CFGAPP:Lme/embodied/services/Launcher$BOComponent;

    const/4 v6, 0x4

    aput-object v2, v13, v6

    sget-object v17, Lme/embodied/services/Launcher$AnimType;->ANIM_START:Lme/embodied/services/Launcher$AnimType;

    const-string v11, "STATE_CONFIG"

    const/4 v12, 0x1

    const/4 v14, 0x1

    const/4 v15, 0x0

    const/16 v16, 0x0

    move-object v10, v0

    invoke-direct/range {v10 .. v17}, Lme/embodied/services/Launcher$LauncherState;-><init>(Ljava/lang/String;I[Lme/embodied/services/Launcher$BOComponent;ZZZLme/embodied/services/Launcher$AnimType;)V

    sput-object v0, Lme/embodied/services/Launcher$LauncherState;->STATE_CONFIG:Lme/embodied/services/Launcher$LauncherState;

    .line 327
    new-instance v0, Lme/embodied/services/Launcher$LauncherState;

    const/4 v2, 0x6

    new-array v7, v2, [Lme/embodied/services/Launcher$BOComponent;

    sget-object v8, Lme/embodied/services/Launcher$BOComponent;->BO_DISPATCH:Lme/embodied/services/Launcher$BOComponent;

    aput-object v8, v7, v9

    sget-object v8, Lme/embodied/services/Launcher$BOComponent;->BO_LOGGER:Lme/embodied/services/Launcher$BOComponent;

    aput-object v8, v7, v3

    sget-object v8, Lme/embodied/services/Launcher$BOComponent;->BO_SYSMON:Lme/embodied/services/Launcher$BOComponent;

    aput-object v8, v7, v4

    sget-object v8, Lme/embodied/services/Launcher$BOComponent;->BO_UPDATER:Lme/embodied/services/Launcher$BOComponent;

    aput-object v8, v7, v5

    sget-object v8, Lme/embodied/services/Launcher$BOComponent;->BO_VISION:Lme/embodied/services/Launcher$BOComponent;

    aput-object v8, v7, v6

    sget-object v8, Lme/embodied/services/Launcher$BOComponent;->BO_MAINAPP:Lme/embodied/services/Launcher$BOComponent;

    aput-object v8, v7, v1

    sget-object v25, Lme/embodied/services/Launcher$AnimType;->ANIM_START:Lme/embodied/services/Launcher$AnimType;

    const-string v19, "STATE_STARTUP"

    const/16 v20, 0x2

    const/16 v22, 0x1

    const/16 v23, 0x0

    const/16 v24, 0x0

    move-object/from16 v18, v0

    move-object/from16 v21, v7

    invoke-direct/range {v18 .. v25}, Lme/embodied/services/Launcher$LauncherState;-><init>(Ljava/lang/String;I[Lme/embodied/services/Launcher$BOComponent;ZZZLme/embodied/services/Launcher$AnimType;)V

    sput-object v0, Lme/embodied/services/Launcher$LauncherState;->STATE_STARTUP:Lme/embodied/services/Launcher$LauncherState;

    .line 333
    new-instance v0, Lme/embodied/services/Launcher$LauncherState;

    const/16 v7, 0x9

    new-array v13, v7, [Lme/embodied/services/Launcher$BOComponent;

    sget-object v8, Lme/embodied/services/Launcher$BOComponent;->BO_DISPATCH:Lme/embodied/services/Launcher$BOComponent;

    aput-object v8, v13, v9

    sget-object v8, Lme/embodied/services/Launcher$BOComponent;->BO_LOGGER:Lme/embodied/services/Launcher$BOComponent;

    aput-object v8, v13, v3

    sget-object v8, Lme/embodied/services/Launcher$BOComponent;->BO_SYSMON:Lme/embodied/services/Launcher$BOComponent;

    aput-object v8, v13, v4

    sget-object v8, Lme/embodied/services/Launcher$BOComponent;->BO_UPDATER:Lme/embodied/services/Launcher$BOComponent;

    aput-object v8, v13, v5

    sget-object v8, Lme/embodied/services/Launcher$BOComponent;->BO_BRAIN:Lme/embodied/services/Launcher$BOComponent;

    aput-object v8, v13, v6

    sget-object v8, Lme/embodied/services/Launcher$BOComponent;->BO_FUSION:Lme/embodied/services/Launcher$BOComponent;

    aput-object v8, v13, v1

    sget-object v8, Lme/embodied/services/Launcher$BOComponent;->BO_VISION:Lme/embodied/services/Launcher$BOComponent;

    aput-object v8, v13, v2

    sget-object v8, Lme/embodied/services/Launcher$BOComponent;->BO_AUDIO:Lme/embodied/services/Launcher$BOComponent;

    const/4 v15, 0x7

    aput-object v8, v13, v15

    sget-object v8, Lme/embodied/services/Launcher$BOComponent;->BO_MAINAPP:Lme/embodied/services/Launcher$BOComponent;

    const/16 v14, 0x8

    aput-object v8, v13, v14

    sget-object v17, Lme/embodied/services/Launcher$AnimType;->ANIM_KEEP:Lme/embodied/services/Launcher$AnimType;

    const-string v11, "STATE_RUNNING"

    const/4 v12, 0x3

    const/4 v8, 0x1

    const/16 v18, 0x0

    move-object v10, v0

    move v7, v14

    move v14, v8

    move v8, v15

    move/from16 v15, v16

    move/from16 v16, v18

    invoke-direct/range {v10 .. v17}, Lme/embodied/services/Launcher$LauncherState;-><init>(Ljava/lang/String;I[Lme/embodied/services/Launcher$BOComponent;ZZZLme/embodied/services/Launcher$AnimType;)V

    sput-object v0, Lme/embodied/services/Launcher$LauncherState;->STATE_RUNNING:Lme/embodied/services/Launcher$LauncherState;

    .line 340
    new-instance v0, Lme/embodied/services/Launcher$LauncherState;

    new-array v10, v7, [Lme/embodied/services/Launcher$BOComponent;

    sget-object v11, Lme/embodied/services/Launcher$BOComponent;->BO_DISPATCH:Lme/embodied/services/Launcher$BOComponent;

    aput-object v11, v10, v9

    sget-object v11, Lme/embodied/services/Launcher$BOComponent;->BO_LOGGER:Lme/embodied/services/Launcher$BOComponent;

    aput-object v11, v10, v3

    sget-object v11, Lme/embodied/services/Launcher$BOComponent;->BO_SYSMON:Lme/embodied/services/Launcher$BOComponent;

    aput-object v11, v10, v4

    sget-object v11, Lme/embodied/services/Launcher$BOComponent;->BO_UPDATER:Lme/embodied/services/Launcher$BOComponent;

    aput-object v11, v10, v5

    sget-object v11, Lme/embodied/services/Launcher$BOComponent;->BO_BRAIN:Lme/embodied/services/Launcher$BOComponent;

    aput-object v11, v10, v6

    sget-object v11, Lme/embodied/services/Launcher$BOComponent;->BO_ANALYTICS:Lme/embodied/services/Launcher$BOComponent;

    aput-object v11, v10, v1

    sget-object v11, Lme/embodied/services/Launcher$BOComponent;->BO_XMOS_WD:Lme/embodied/services/Launcher$BOComponent;

    aput-object v11, v10, v2

    sget-object v11, Lme/embodied/services/Launcher$BOComponent;->BO_MAINAPP:Lme/embodied/services/Launcher$BOComponent;

    aput-object v11, v10, v8

    sget-object v27, Lme/embodied/services/Launcher$AnimType;->ANIM_NONE:Lme/embodied/services/Launcher$AnimType;

    new-instance v29, Lme/embodied/services/Launcher$LightSleepFilter;

    invoke-direct/range {v29 .. v29}, Lme/embodied/services/Launcher$LightSleepFilter;-><init>()V

    const-string v21, "STATE_LIGHT_SLEEP"

    const/16 v22, 0x4

    const/16 v25, 0x0

    const/16 v26, 0x0

    const/16 v28, 0x0

    move-object/from16 v20, v0

    move-object/from16 v23, v10

    invoke-direct/range {v20 .. v29}, Lme/embodied/services/Launcher$LauncherState;-><init>(Ljava/lang/String;I[Lme/embodied/services/Launcher$BOComponent;ZZZLme/embodied/services/Launcher$AnimType;Ljava/lang/Runnable;Lme/embodied/services/Launcher$ComponentFilter;)V

    sput-object v0, Lme/embodied/services/Launcher$LauncherState;->STATE_LIGHT_SLEEP:Lme/embodied/services/Launcher$LauncherState;

    .line 347
    new-instance v0, Lme/embodied/services/Launcher$LauncherState;

    new-array v10, v6, [Lme/embodied/services/Launcher$BOComponent;

    sget-object v11, Lme/embodied/services/Launcher$BOComponent;->BO_DISPATCH:Lme/embodied/services/Launcher$BOComponent;

    aput-object v11, v10, v9

    sget-object v11, Lme/embodied/services/Launcher$BOComponent;->BO_SYSMON:Lme/embodied/services/Launcher$BOComponent;

    aput-object v11, v10, v3

    sget-object v11, Lme/embodied/services/Launcher$BOComponent;->BO_BRAIN:Lme/embodied/services/Launcher$BOComponent;

    aput-object v11, v10, v4

    sget-object v11, Lme/embodied/services/Launcher$BOComponent;->BO_MAINAPP:Lme/embodied/services/Launcher$BOComponent;

    aput-object v11, v10, v5

    sget-object v37, Lme/embodied/services/Launcher$AnimType;->ANIM_NONE:Lme/embodied/services/Launcher$AnimType;

    new-instance v39, Lme/embodied/services/Launcher$SuspendFilter;

    invoke-direct/range {v39 .. v39}, Lme/embodied/services/Launcher$SuspendFilter;-><init>()V

    const-string v31, "STATE_SUSPEND"

    const/16 v32, 0x5

    const/16 v34, 0x0

    const/16 v35, 0x1

    const/16 v36, 0x0

    const/16 v38, 0x0

    move-object/from16 v30, v0

    move-object/from16 v33, v10

    invoke-direct/range {v30 .. v39}, Lme/embodied/services/Launcher$LauncherState;-><init>(Ljava/lang/String;I[Lme/embodied/services/Launcher$BOComponent;ZZZLme/embodied/services/Launcher$AnimType;Ljava/lang/Runnable;Lme/embodied/services/Launcher$ComponentFilter;)V

    sput-object v0, Lme/embodied/services/Launcher$LauncherState;->STATE_SUSPEND:Lme/embodied/services/Launcher$LauncherState;

    .line 352
    new-instance v0, Lme/embodied/services/Launcher$LauncherState;

    new-array v14, v8, [Lme/embodied/services/Launcher$BOComponent;

    sget-object v10, Lme/embodied/services/Launcher$BOComponent;->BO_DISPATCH:Lme/embodied/services/Launcher$BOComponent;

    aput-object v10, v14, v9

    sget-object v10, Lme/embodied/services/Launcher$BOComponent;->BO_LOGGER:Lme/embodied/services/Launcher$BOComponent;

    aput-object v10, v14, v3

    sget-object v10, Lme/embodied/services/Launcher$BOComponent;->BO_SYSMON:Lme/embodied/services/Launcher$BOComponent;

    aput-object v10, v14, v4

    sget-object v10, Lme/embodied/services/Launcher$BOComponent;->BO_UPDATER:Lme/embodied/services/Launcher$BOComponent;

    aput-object v10, v14, v5

    sget-object v10, Lme/embodied/services/Launcher$BOComponent;->BO_FUSION:Lme/embodied/services/Launcher$BOComponent;

    aput-object v10, v14, v6

    sget-object v10, Lme/embodied/services/Launcher$BOComponent;->BO_VISION:Lme/embodied/services/Launcher$BOComponent;

    aput-object v10, v14, v1

    sget-object v10, Lme/embodied/services/Launcher$BOComponent;->BO_MAINAPP:Lme/embodied/services/Launcher$BOComponent;

    aput-object v10, v14, v2

    sget-object v18, Lme/embodied/services/Launcher$AnimType;->ANIM_KEEP:Lme/embodied/services/Launcher$AnimType;

    const-string v12, "STATE_DEMO"

    const/4 v13, 0x6

    const/4 v15, 0x1

    const/16 v16, 0x0

    const/16 v17, 0x0

    move-object v11, v0

    invoke-direct/range {v11 .. v18}, Lme/embodied/services/Launcher$LauncherState;-><init>(Ljava/lang/String;I[Lme/embodied/services/Launcher$BOComponent;ZZZLme/embodied/services/Launcher$AnimType;)V

    sput-object v0, Lme/embodied/services/Launcher$LauncherState;->STATE_DEMO:Lme/embodied/services/Launcher$LauncherState;

    .line 357
    new-instance v0, Lme/embodied/services/Launcher$LauncherState;

    new-array v10, v2, [Lme/embodied/services/Launcher$BOComponent;

    sget-object v11, Lme/embodied/services/Launcher$BOComponent;->BO_DISPATCH:Lme/embodied/services/Launcher$BOComponent;

    aput-object v11, v10, v9

    sget-object v11, Lme/embodied/services/Launcher$BOComponent;->BO_LOGGER:Lme/embodied/services/Launcher$BOComponent;

    aput-object v11, v10, v3

    sget-object v11, Lme/embodied/services/Launcher$BOComponent;->BO_SYSMON:Lme/embodied/services/Launcher$BOComponent;

    aput-object v11, v10, v4

    sget-object v11, Lme/embodied/services/Launcher$BOComponent;->BO_UPDATER:Lme/embodied/services/Launcher$BOComponent;

    aput-object v11, v10, v5

    sget-object v11, Lme/embodied/services/Launcher$BOComponent;->BO_BRAIN:Lme/embodied/services/Launcher$BOComponent;

    aput-object v11, v10, v6

    sget-object v11, Lme/embodied/services/Launcher$BOComponent;->BO_MAINAPP:Lme/embodied/services/Launcher$BOComponent;

    aput-object v11, v10, v1

    sget-object v27, Lme/embodied/services/Launcher$AnimType;->ANIM_KEEP:Lme/embodied/services/Launcher$AnimType;

    new-instance v28, Lme/embodied/services/Launcher$LauncherState$1;

    invoke-direct/range {v28 .. v28}, Lme/embodied/services/Launcher$LauncherState$1;-><init>()V

    const-string v21, "STATE_RECOVERY"

    const/16 v22, 0x7

    const/16 v24, 0x1

    move-object/from16 v20, v0

    move-object/from16 v23, v10

    invoke-direct/range {v20 .. v28}, Lme/embodied/services/Launcher$LauncherState;-><init>(Ljava/lang/String;I[Lme/embodied/services/Launcher$BOComponent;ZZZLme/embodied/services/Launcher$AnimType;Ljava/lang/Runnable;)V

    sput-object v0, Lme/embodied/services/Launcher$LauncherState;->STATE_RECOVERY:Lme/embodied/services/Launcher$LauncherState;

    .line 364
    new-instance v0, Lme/embodied/services/Launcher$LauncherState;

    new-array v14, v7, [Lme/embodied/services/Launcher$BOComponent;

    sget-object v10, Lme/embodied/services/Launcher$BOComponent;->BO_DISPATCH:Lme/embodied/services/Launcher$BOComponent;

    aput-object v10, v14, v9

    sget-object v10, Lme/embodied/services/Launcher$BOComponent;->BO_LOGGER:Lme/embodied/services/Launcher$BOComponent;

    aput-object v10, v14, v3

    sget-object v10, Lme/embodied/services/Launcher$BOComponent;->BO_SYSMON:Lme/embodied/services/Launcher$BOComponent;

    aput-object v10, v14, v4

    sget-object v10, Lme/embodied/services/Launcher$BOComponent;->BO_UPDATER:Lme/embodied/services/Launcher$BOComponent;

    aput-object v10, v14, v5

    sget-object v10, Lme/embodied/services/Launcher$BOComponent;->BO_FUSION:Lme/embodied/services/Launcher$BOComponent;

    aput-object v10, v14, v6

    sget-object v10, Lme/embodied/services/Launcher$BOComponent;->BO_VISION:Lme/embodied/services/Launcher$BOComponent;

    aput-object v10, v14, v1

    sget-object v10, Lme/embodied/services/Launcher$BOComponent;->BO_AUDIO:Lme/embodied/services/Launcher$BOComponent;

    aput-object v10, v14, v2

    sget-object v10, Lme/embodied/services/Launcher$BOComponent;->BO_MAINAPP:Lme/embodied/services/Launcher$BOComponent;

    aput-object v10, v14, v8

    sget-object v18, Lme/embodied/services/Launcher$AnimType;->ANIM_KEEP:Lme/embodied/services/Launcher$AnimType;

    const-string v12, "STATE_TELEBRAIN"

    const/16 v13, 0x8

    move-object v11, v0

    invoke-direct/range {v11 .. v18}, Lme/embodied/services/Launcher$LauncherState;-><init>(Ljava/lang/String;I[Lme/embodied/services/Launcher$BOComponent;ZZZLme/embodied/services/Launcher$AnimType;)V

    sput-object v0, Lme/embodied/services/Launcher$LauncherState;->STATE_TELEBRAIN:Lme/embodied/services/Launcher$LauncherState;

    .line 370
    new-instance v0, Lme/embodied/services/Launcher$LauncherState;

    new-array v10, v1, [Lme/embodied/services/Launcher$BOComponent;

    sget-object v11, Lme/embodied/services/Launcher$BOComponent;->BO_DISPATCH:Lme/embodied/services/Launcher$BOComponent;

    aput-object v11, v10, v9

    sget-object v11, Lme/embodied/services/Launcher$BOComponent;->BO_LOGGER:Lme/embodied/services/Launcher$BOComponent;

    aput-object v11, v10, v3

    sget-object v11, Lme/embodied/services/Launcher$BOComponent;->BO_SYSMON:Lme/embodied/services/Launcher$BOComponent;

    aput-object v11, v10, v4

    sget-object v11, Lme/embodied/services/Launcher$BOComponent;->BO_UPDATER:Lme/embodied/services/Launcher$BOComponent;

    aput-object v11, v10, v5

    sget-object v11, Lme/embodied/services/Launcher$BOComponent;->BO_BRAIN:Lme/embodied/services/Launcher$BOComponent;

    aput-object v11, v10, v6

    sget-object v27, Lme/embodied/services/Launcher$AnimType;->ANIM_NONE:Lme/embodied/services/Launcher$AnimType;

    new-instance v28, Lme/embodied/services/Launcher$LauncherState$2;

    invoke-direct/range {v28 .. v28}, Lme/embodied/services/Launcher$LauncherState$2;-><init>()V

    const-string v21, "STATE_SILENT_REBOOT"

    const/16 v22, 0x9

    const/16 v24, 0x0

    const/16 v26, 0x1

    move-object/from16 v20, v0

    move-object/from16 v23, v10

    invoke-direct/range {v20 .. v28}, Lme/embodied/services/Launcher$LauncherState;-><init>(Ljava/lang/String;I[Lme/embodied/services/Launcher$BOComponent;ZZZLme/embodied/services/Launcher$AnimType;Ljava/lang/Runnable;)V

    sput-object v0, Lme/embodied/services/Launcher$LauncherState;->STATE_SILENT_REBOOT:Lme/embodied/services/Launcher$LauncherState;

    .line 376
    new-instance v0, Lme/embodied/services/Launcher$LauncherState;

    new-array v10, v2, [Lme/embodied/services/Launcher$BOComponent;

    sget-object v11, Lme/embodied/services/Launcher$BOComponent;->BO_DISPATCH:Lme/embodied/services/Launcher$BOComponent;

    aput-object v11, v10, v9

    sget-object v11, Lme/embodied/services/Launcher$BOComponent;->BO_LOGGER:Lme/embodied/services/Launcher$BOComponent;

    aput-object v11, v10, v3

    sget-object v11, Lme/embodied/services/Launcher$BOComponent;->BO_SYSMON:Lme/embodied/services/Launcher$BOComponent;

    aput-object v11, v10, v4

    sget-object v11, Lme/embodied/services/Launcher$BOComponent;->BO_UPDATER:Lme/embodied/services/Launcher$BOComponent;

    aput-object v11, v10, v5

    sget-object v11, Lme/embodied/services/Launcher$BOComponent;->BO_BRAIN:Lme/embodied/services/Launcher$BOComponent;

    aput-object v11, v10, v6

    sget-object v11, Lme/embodied/services/Launcher$BOComponent;->BO_MAINAPP:Lme/embodied/services/Launcher$BOComponent;

    aput-object v11, v10, v1

    sget-object v36, Lme/embodied/services/Launcher$AnimType;->ANIM_KEEP:Lme/embodied/services/Launcher$AnimType;

    new-instance v37, Lme/embodied/services/Launcher$LauncherState$3;

    invoke-direct/range {v37 .. v37}, Lme/embodied/services/Launcher$LauncherState$3;-><init>()V

    const-string v30, "STATE_SILENT_RECOVERY"

    const/16 v31, 0xa

    const/16 v33, 0x0

    const/16 v35, 0x0

    move-object/from16 v29, v0

    move-object/from16 v32, v10

    invoke-direct/range {v29 .. v37}, Lme/embodied/services/Launcher$LauncherState;-><init>(Ljava/lang/String;I[Lme/embodied/services/Launcher$BOComponent;ZZZLme/embodied/services/Launcher$AnimType;Ljava/lang/Runnable;)V

    sput-object v0, Lme/embodied/services/Launcher$LauncherState;->STATE_SILENT_RECOVERY:Lme/embodied/services/Launcher$LauncherState;

    .line 383
    new-instance v0, Lme/embodied/services/Launcher$LauncherState;

    new-array v10, v1, [Lme/embodied/services/Launcher$BOComponent;

    sget-object v11, Lme/embodied/services/Launcher$BOComponent;->BO_DISPATCH:Lme/embodied/services/Launcher$BOComponent;

    aput-object v11, v10, v9

    sget-object v11, Lme/embodied/services/Launcher$BOComponent;->BO_LOGGER:Lme/embodied/services/Launcher$BOComponent;

    aput-object v11, v10, v3

    sget-object v11, Lme/embodied/services/Launcher$BOComponent;->BO_SYSMON:Lme/embodied/services/Launcher$BOComponent;

    aput-object v11, v10, v4

    sget-object v11, Lme/embodied/services/Launcher$BOComponent;->BO_UPDATER:Lme/embodied/services/Launcher$BOComponent;

    aput-object v11, v10, v5

    sget-object v11, Lme/embodied/services/Launcher$BOComponent;->BO_BRAIN:Lme/embodied/services/Launcher$BOComponent;

    aput-object v11, v10, v6

    sget-object v27, Lme/embodied/services/Launcher$AnimType;->ANIM_NONE:Lme/embodied/services/Launcher$AnimType;

    new-instance v28, Lme/embodied/services/Launcher$LauncherState$4;

    invoke-direct/range {v28 .. v28}, Lme/embodied/services/Launcher$LauncherState$4;-><init>()V

    const-string v21, "STATE_SHUTDOWN"

    const/16 v22, 0xb

    move-object/from16 v20, v0

    move-object/from16 v23, v10

    invoke-direct/range {v20 .. v28}, Lme/embodied/services/Launcher$LauncherState;-><init>(Ljava/lang/String;I[Lme/embodied/services/Launcher$BOComponent;ZZZLme/embodied/services/Launcher$AnimType;Ljava/lang/Runnable;)V

    sput-object v0, Lme/embodied/services/Launcher$LauncherState;->STATE_SHUTDOWN:Lme/embodied/services/Launcher$LauncherState;

    .line 320
    const/16 v10, 0xc

    new-array v10, v10, [Lme/embodied/services/Launcher$LauncherState;

    sget-object v11, Lme/embodied/services/Launcher$LauncherState;->STATE_INIT:Lme/embodied/services/Launcher$LauncherState;

    aput-object v11, v10, v9

    sget-object v9, Lme/embodied/services/Launcher$LauncherState;->STATE_CONFIG:Lme/embodied/services/Launcher$LauncherState;

    aput-object v9, v10, v3

    sget-object v3, Lme/embodied/services/Launcher$LauncherState;->STATE_STARTUP:Lme/embodied/services/Launcher$LauncherState;

    aput-object v3, v10, v4

    sget-object v3, Lme/embodied/services/Launcher$LauncherState;->STATE_RUNNING:Lme/embodied/services/Launcher$LauncherState;

    aput-object v3, v10, v5

    sget-object v3, Lme/embodied/services/Launcher$LauncherState;->STATE_LIGHT_SLEEP:Lme/embodied/services/Launcher$LauncherState;

    aput-object v3, v10, v6

    sget-object v3, Lme/embodied/services/Launcher$LauncherState;->STATE_SUSPEND:Lme/embodied/services/Launcher$LauncherState;

    aput-object v3, v10, v1

    sget-object v1, Lme/embodied/services/Launcher$LauncherState;->STATE_DEMO:Lme/embodied/services/Launcher$LauncherState;

    aput-object v1, v10, v2

    sget-object v1, Lme/embodied/services/Launcher$LauncherState;->STATE_RECOVERY:Lme/embodied/services/Launcher$LauncherState;

    aput-object v1, v10, v8

    sget-object v1, Lme/embodied/services/Launcher$LauncherState;->STATE_TELEBRAIN:Lme/embodied/services/Launcher$LauncherState;

    aput-object v1, v10, v7

    sget-object v1, Lme/embodied/services/Launcher$LauncherState;->STATE_SILENT_REBOOT:Lme/embodied/services/Launcher$LauncherState;

    const/16 v2, 0x9

    aput-object v1, v10, v2

    sget-object v1, Lme/embodied/services/Launcher$LauncherState;->STATE_SILENT_RECOVERY:Lme/embodied/services/Launcher$LauncherState;

    const/16 v2, 0xa

    aput-object v1, v10, v2

    const/16 v1, 0xb

    aput-object v0, v10, v1

    sput-object v10, Lme/embodied/services/Launcher$LauncherState;->$VALUES:[Lme/embodied/services/Launcher$LauncherState;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 398
    sget-object v7, Lme/embodied/services/Launcher$AnimType;->ANIM_NONE:Lme/embodied/services/Launcher$AnimType;

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    const/4 v6, 0x0

    move-object v0, p0

    move-object v1, p1

    move v2, p2

    invoke-direct/range {v0 .. v7}, Lme/embodied/services/Launcher$LauncherState;-><init>(Ljava/lang/String;I[Lme/embodied/services/Launcher$BOComponent;ZZZLme/embodied/services/Launcher$AnimType;)V

    .line 399
    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I[Lme/embodied/services/Launcher$BOComponent;ZZZLme/embodied/services/Launcher$AnimType;)V
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([",
            "Lme/embodied/services/Launcher$BOComponent;",
            "ZZZ",
            "Lme/embodied/services/Launcher$AnimType;",
            ")V"
        }
    .end annotation

    .line 401
    const/4 v8, 0x0

    move-object v0, p0

    move-object v1, p1

    move v2, p2

    move-object v3, p3

    move v4, p4

    move v5, p5

    move v6, p6

    move-object/from16 v7, p7

    invoke-direct/range {v0 .. v8}, Lme/embodied/services/Launcher$LauncherState;-><init>(Ljava/lang/String;I[Lme/embodied/services/Launcher$BOComponent;ZZZLme/embodied/services/Launcher$AnimType;Ljava/lang/Runnable;)V

    .line 402
    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I[Lme/embodied/services/Launcher$BOComponent;ZZZLme/embodied/services/Launcher$AnimType;Ljava/lang/Runnable;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([",
            "Lme/embodied/services/Launcher$BOComponent;",
            "ZZZ",
            "Lme/embodied/services/Launcher$AnimType;",
            "Ljava/lang/Runnable;",
            ")V"
        }
    .end annotation

    .line 403
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 404
    new-instance p1, Ljava/util/HashSet;

    invoke-direct {p1}, Ljava/util/HashSet;-><init>()V

    iput-object p1, p0, Lme/embodied/services/Launcher$LauncherState;->active_components_:Ljava/util/HashSet;

    .line 405
    if-eqz p3, :cond_0

    .line 406
    array-length p1, p3

    const/4 p2, 0x0

    :goto_0
    if-ge p2, p1, :cond_0

    aget-object v0, p3, p2

    .line 407
    iget-object v1, p0, Lme/embodied/services/Launcher$LauncherState;->active_components_:Ljava/util/HashSet;

    invoke-virtual {v1, v0}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    .line 406
    add-int/lit8 p2, p2, 0x1

    goto :goto_0

    .line 409
    :cond_0
    iput-boolean p4, p0, Lme/embodied/services/Launcher$LauncherState;->screen_active_:Z

    .line 410
    iput-boolean p5, p0, Lme/embodied/services/Launcher$LauncherState;->suspend_state_:Z

    .line 411
    iput-boolean p6, p0, Lme/embodied/services/Launcher$LauncherState;->terminal_state_:Z

    .line 412
    iput-object p7, p0, Lme/embodied/services/Launcher$LauncherState;->animation_on_start_:Lme/embodied/services/Launcher$AnimType;

    .line 413
    iput-object p8, p0, Lme/embodied/services/Launcher$LauncherState;->on_state_final_:Ljava/lang/Runnable;

    .line 414
    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I[Lme/embodied/services/Launcher$BOComponent;ZZZLme/embodied/services/Launcher$AnimType;Ljava/lang/Runnable;Lme/embodied/services/Launcher$ComponentFilter;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([",
            "Lme/embodied/services/Launcher$BOComponent;",
            "ZZZ",
            "Lme/embodied/services/Launcher$AnimType;",
            "Ljava/lang/Runnable;",
            "Lme/embodied/services/Launcher$ComponentFilter;",
            ")V"
        }
    .end annotation

    .line 416
    invoke-direct/range {p0 .. p8}, Lme/embodied/services/Launcher$LauncherState;-><init>(Ljava/lang/String;I[Lme/embodied/services/Launcher$BOComponent;ZZZLme/embodied/services/Launcher$AnimType;Ljava/lang/Runnable;)V

    .line 417
    iput-object p9, p0, Lme/embodied/services/Launcher$LauncherState;->filter_:Lme/embodied/services/Launcher$ComponentFilter;

    .line 418
    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lme/embodied/services/Launcher$LauncherState;
    .locals 1

    .line 320
    const-class v0, Lme/embodied/services/Launcher$LauncherState;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lme/embodied/services/Launcher$LauncherState;

    return-object p0
.end method

.method public static values()[Lme/embodied/services/Launcher$LauncherState;
    .locals 1

    .line 320
    sget-object v0, Lme/embodied/services/Launcher$LauncherState;->$VALUES:[Lme/embodied/services/Launcher$LauncherState;

    invoke-virtual {v0}, [Lme/embodied/services/Launcher$LauncherState;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lme/embodied/services/Launcher$LauncherState;

    return-object v0
.end method


# virtual methods
.method public animationOnStart()Lme/embodied/services/Launcher$AnimType;
    .locals 1

    .line 440
    iget-object v0, p0, Lme/embodied/services/Launcher$LauncherState;->animation_on_start_:Lme/embodied/services/Launcher$AnimType;

    return-object v0
.end method

.method public componentActive(Lme/embodied/services/Launcher$BOComponent;)Z
    .locals 2

    .line 420
    iget-object v0, p0, Lme/embodied/services/Launcher$LauncherState;->filter_:Lme/embodied/services/Launcher$ComponentFilter;

    if-eqz v0, :cond_0

    .line 422
    iget-object v1, p0, Lme/embodied/services/Launcher$LauncherState;->active_components_:Ljava/util/HashSet;

    invoke-virtual {v1, p1}, Ljava/util/HashSet;->contains(Ljava/lang/Object;)Z

    move-result v1

    invoke-interface {v0, p1, v1}, Lme/embodied/services/Launcher$ComponentFilter;->ComponentAllowed(Lme/embodied/services/Launcher$BOComponent;Z)Z

    move-result p1

    return p1

    .line 424
    :cond_0
    iget-object v0, p0, Lme/embodied/services/Launcher$LauncherState;->active_components_:Ljava/util/HashSet;

    invoke-virtual {v0, p1}, Ljava/util/HashSet;->contains(Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method

.method public isFinalState()Z
    .locals 1

    .line 437
    iget-boolean v0, p0, Lme/embodied/services/Launcher$LauncherState;->terminal_state_:Z

    return v0
.end method

.method public isSleepState()Z
    .locals 1

    .line 433
    iget-boolean v0, p0, Lme/embodied/services/Launcher$LauncherState;->suspend_state_:Z

    if-nez v0, :cond_1

    iget-boolean v0, p0, Lme/embodied/services/Launcher$LauncherState;->screen_active_:Z

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v0, 0x1

    :goto_1
    return v0
.end method

.method public needSuspend()Z
    .locals 1

    .line 430
    iget-boolean v0, p0, Lme/embodied/services/Launcher$LauncherState;->suspend_state_:Z

    return v0
.end method

.method public screenActive()Z
    .locals 1

    .line 427
    iget-boolean v0, p0, Lme/embodied/services/Launcher$LauncherState;->screen_active_:Z

    return v0
.end method
