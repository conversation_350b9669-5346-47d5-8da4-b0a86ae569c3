.class final enum Lme/embodied/services/Launcher$AnimType;
.super Ljava/lang/Enum;
.source "Launcher.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lme/embodied/services/Launcher;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4018
    name = "AnimType"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lme/embodied/services/Launcher$AnimType;",
        ">;"
    }
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lme/embodied/services/Launcher$AnimType;

.field public static final enum ANIM_KEEP:Lme/embodied/services/Launcher$AnimType;

.field public static final enum ANIM_NONE:Lme/embodied/services/Launcher$AnimType;

.field public static final enum ANIM_START:Lme/embodied/services/Launcher$AnimType;


# direct methods
.method static constructor <clinit>()V
    .locals 6

    .line 269
    new-instance v0, Lme/embodied/services/Launcher$AnimType;

    const-string v1, "ANIM_NONE"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lme/embodied/services/Launcher$AnimType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lme/embodied/services/Launcher$AnimType;->ANIM_NONE:Lme/embodied/services/Launcher$AnimType;

    .line 270
    new-instance v0, Lme/embodied/services/Launcher$AnimType;

    const-string v1, "ANIM_START"

    const/4 v3, 0x1

    invoke-direct {v0, v1, v3}, Lme/embodied/services/Launcher$AnimType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lme/embodied/services/Launcher$AnimType;->ANIM_START:Lme/embodied/services/Launcher$AnimType;

    .line 271
    new-instance v0, Lme/embodied/services/Launcher$AnimType;

    const-string v1, "ANIM_KEEP"

    const/4 v4, 0x2

    invoke-direct {v0, v1, v4}, Lme/embodied/services/Launcher$AnimType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lme/embodied/services/Launcher$AnimType;->ANIM_KEEP:Lme/embodied/services/Launcher$AnimType;

    .line 268
    const/4 v1, 0x3

    new-array v1, v1, [Lme/embodied/services/Launcher$AnimType;

    sget-object v5, Lme/embodied/services/Launcher$AnimType;->ANIM_NONE:Lme/embodied/services/Launcher$AnimType;

    aput-object v5, v1, v2

    sget-object v2, Lme/embodied/services/Launcher$AnimType;->ANIM_START:Lme/embodied/services/Launcher$AnimType;

    aput-object v2, v1, v3

    aput-object v0, v1, v4

    sput-object v1, Lme/embodied/services/Launcher$AnimType;->$VALUES:[Lme/embodied/services/Launcher$AnimType;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 268
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lme/embodied/services/Launcher$AnimType;
    .locals 1

    .line 268
    const-class v0, Lme/embodied/services/Launcher$AnimType;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lme/embodied/services/Launcher$AnimType;

    return-object p0
.end method

.method public static values()[Lme/embodied/services/Launcher$AnimType;
    .locals 1

    .line 268
    sget-object v0, Lme/embodied/services/Launcher$AnimType;->$VALUES:[Lme/embodied/services/Launcher$AnimType;

    invoke-virtual {v0}, [Lme/embodied/services/Launcher$AnimType;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lme/embodied/services/Launcher$AnimType;

    return-object v0
.end method
