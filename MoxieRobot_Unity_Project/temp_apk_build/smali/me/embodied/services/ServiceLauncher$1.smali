.class Lme/embodied/services/ServiceLauncher$1;
.super Landroid/content/BroadcastReceiver;
.source "ServiceLauncher.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lme/embodied/services/ServiceLauncher;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lme/embodied/services/ServiceLauncher;


# direct methods
.method constructor <init>(Lme/embodied/services/ServiceLauncher;)V
    .locals 0

    .line 138
    iput-object p1, p0, Lme/embodied/services/ServiceLauncher$1;->this$0:Lme/embodied/services/ServiceLauncher;

    invoke-direct {p0}, Landroid/content/BroadcastReceiver;-><init>()V

    return-void
.end method


# virtual methods
.method public onReceive(Landroid/content/Context;Landroid/content/Intent;)V
    .locals 3

    .line 141
    invoke-virtual {p2}, Landroid/content/Intent;->getAction()Ljava/lang/String;

    move-result-object p1

    const-string p2, "com.embodied.ota_check_complete"

    invoke-virtual {p1, p2}, Ljava/lang/String;->equalsIgnoreCase(Ljava/lang/String;)Z

    move-result p1

    if-eqz p1, :cond_0

    .line 142
    const-string p1, "bo-launcher-s"

    const-string p2, "OTA Check complete, setting XMOS readiness"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 143
    new-instance p2, Landroid/content/Intent;

    invoke-direct {p2}, Landroid/content/Intent;-><init>()V

    .line 144
    new-instance v0, Landroid/content/ComponentName;

    const-class v1, Lme/embodied/services/XMOSDFU;

    invoke-virtual {v1}, Ljava/lang/Class;->getCanonicalName()Ljava/lang/String;

    move-result-object v1

    const-string v2, "com.embodied.bo_unity"

    invoke-direct {v0, v2, v1}, Landroid/content/ComponentName;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    invoke-virtual {p2, v0}, Landroid/content/Intent;->setComponent(Landroid/content/ComponentName;)Landroid/content/Intent;

    .line 145
    iget-object v0, p0, Lme/embodied/services/ServiceLauncher$1;->this$0:Lme/embodied/services/ServiceLauncher;

    invoke-virtual {v0, p2}, Lme/embodied/services/ServiceLauncher;->stopService(Landroid/content/Intent;)Z

    .line 146
    iget-object p2, p0, Lme/embodied/services/ServiceLauncher$1;->this$0:Lme/embodied/services/ServiceLauncher;

    invoke-static {p2}, Lme/embodied/services/ServiceLauncher;->access$000(Lme/embodied/services/ServiceLauncher;)Landroid/content/BroadcastReceiver;

    move-result-object v0

    invoke-virtual {p2, v0}, Lme/embodied/services/ServiceLauncher;->unregisterReceiver(Landroid/content/BroadcastReceiver;)V

    .line 147
    iget-object p2, p0, Lme/embodied/services/ServiceLauncher$1;->this$0:Lme/embodied/services/ServiceLauncher;

    const/4 v0, 0x1

    invoke-static {p2, v0}, Lme/embodied/services/ServiceLauncher;->access$102(Lme/embodied/services/ServiceLauncher;Z)Z

    .line 148
    iget-object p2, p0, Lme/embodied/services/ServiceLauncher$1;->this$0:Lme/embodied/services/ServiceLauncher;

    invoke-static {p2}, Lme/embodied/services/ServiceLauncher;->access$200(Lme/embodied/services/ServiceLauncher;)Z

    move-result p2

    if-eqz p2, :cond_0

    .line 149
    const-string p2, "XMOS is ready and launch requested, starting now."

    invoke-static {p1, p2}, Landroid/util/Log;->v(Ljava/lang/String;Ljava/lang/String;)I

    .line 150
    iget-object p1, p0, Lme/embodied/services/ServiceLauncher$1;->this$0:Lme/embodied/services/ServiceLauncher;

    invoke-static {p1}, Lme/embodied/services/ServiceLauncher;->access$300(Lme/embodied/services/ServiceLauncher;)Lme/embodied/services/Launcher;

    move-result-object p1

    invoke-virtual {p1}, Lme/embodied/services/Launcher;->LaunchServices()V

    .line 153
    :cond_0
    return-void
.end method
