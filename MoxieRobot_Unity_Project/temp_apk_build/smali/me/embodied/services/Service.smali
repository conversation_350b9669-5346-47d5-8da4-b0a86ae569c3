.class public Lme/embodied/services/Service;
.super Landroid/app/Service;
.source "Service.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lme/embodied/services/Service$TimedRelease;
    }
.end annotation


# static fields
.field public static final ACTION_SERVICE_REPORT:Ljava/lang/String; = "com.embodied.service_report"

.field public static final NATIVE_RELEASE_MAX_WAIT:J = 0x2710L


# instance fields
.field protected custom_stack_size_:J

.field protected lib_name_:Ljava/lang/String;

.field protected ptr:J

.field thread_:Ljava/lang/Thread;


# direct methods
.method public constructor <init>(Ljava/lang/String;)V
    .locals 2

    .line 20
    const-wide/16 v0, 0x0

    invoke-direct {p0, p1, v0, v1, p1}, Lme/embodied/services/Service;-><init>(Ljava/lang/String;JLjava/lang/String;)V

    .line 21
    return-void
.end method

.method public constructor <init>(Ljava/lang/String;J)V
    .locals 0

    .line 25
    invoke-direct {p0, p1, p2, p3, p1}, Lme/embodied/services/Service;-><init>(Ljava/lang/String;JLjava/lang/String;)V

    .line 26
    return-void
.end method

.method public constructor <init>(Ljava/lang/String;JLjava/lang/String;)V
    .locals 2

    .line 29
    invoke-direct {p0}, Landroid/app/Service;-><init>()V

    .line 13
    const-wide/16 v0, 0x0

    iput-wide v0, p0, Lme/embodied/services/Service;->ptr:J

    .line 30
    iput-wide v0, p0, Lme/embodied/services/Service;->ptr:J

    .line 31
    iput-object p1, p0, Lme/embodied/services/Service;->lib_name_:Ljava/lang/String;

    .line 32
    iput-wide p2, p0, Lme/embodied/services/Service;->custom_stack_size_:J

    .line 33
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string p3, "LOADING NATIVE LIBRARIES from "

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 34
    invoke-static {p4}, Ljava/lang/System;->loadLibrary(Ljava/lang/String;)V

    .line 35
    return-void
.end method

.method private ReportStartup()V
    .locals 3

    .line 142
    new-instance v0, Landroid/content/Intent;

    invoke-direct {v0}, Landroid/content/Intent;-><init>()V

    .line 143
    const-string v1, "com.embodied.service_report"

    invoke-virtual {v0, v1}, Landroid/content/Intent;->setAction(Ljava/lang/String;)Landroid/content/Intent;

    .line 144
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Class;->getCanonicalName()Ljava/lang/String;

    move-result-object v1

    const-string v2, "service"

    invoke-virtual {v0, v2, v1}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Ljava/lang/String;)Landroid/content/Intent;

    .line 145
    invoke-virtual {p0, v0}, Lme/embodied/services/Service;->sendBroadcast(Landroid/content/Intent;)V

    .line 146
    return-void
.end method


# virtual methods
.method protected native CreateService(Ljava/lang/String;)J
.end method

.method protected NativeReady()V
    .locals 0

    .line 79
    return-void
.end method

.method protected native ReleaseService(J)V
.end method

.method protected native RunService(J)V
.end method

.method protected ServiceName()Ljava/lang/String;
    .locals 1

    .line 76
    iget-object v0, p0, Lme/embodied/services/Service;->lib_name_:Ljava/lang/String;

    return-object v0
.end method

.method public final Start()V
    .locals 4

    .line 83
    invoke-virtual {p0}, Lme/embodied/services/Service;->ServiceName()Ljava/lang/String;

    move-result-object v0

    const-string v1, "Creating service"

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 85
    monitor-enter p0

    .line 86
    :try_start_0
    iget-wide v0, p0, Lme/embodied/services/Service;->ptr:J

    const-wide/16 v2, 0x0

    cmp-long v0, v0, v2

    if-nez v0, :cond_1

    .line 87
    invoke-virtual {p0}, Lme/embodied/services/Service;->ServiceName()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lme/embodied/services/Service;->CreateService(Ljava/lang/String;)J

    move-result-wide v0

    iput-wide v0, p0, Lme/embodied/services/Service;->ptr:J

    .line 93
    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 94
    cmp-long v0, v0, v2

    if-eqz v0, :cond_0

    .line 95
    invoke-virtual {p0}, Lme/embodied/services/Service;->NativeReady()V

    goto :goto_0

    .line 97
    :cond_0
    invoke-virtual {p0}, Lme/embodied/services/Service;->ServiceName()Ljava/lang/String;

    move-result-object v0

    const-string v1, "Failed to create native service."

    invoke-static {v0, v1}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 99
    :goto_0
    invoke-virtual {p0}, Lme/embodied/services/Service;->ServiceName()Ljava/lang/String;

    move-result-object v0

    const-string v1, "Starting service"

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 100
    invoke-direct {p0}, Lme/embodied/services/Service;->ReportStartup()V

    .line 101
    iget-wide v0, p0, Lme/embodied/services/Service;->ptr:J

    invoke-virtual {p0, v0, v1}, Lme/embodied/services/Service;->RunService(J)V

    .line 102
    return-void

    .line 90
    :cond_1
    :try_start_1
    invoke-virtual {p0}, Lme/embodied/services/Service;->ServiceName()Ljava/lang/String;

    move-result-object v0

    const-string v1, "Native service already initialized.  Preventing Duplicate native service creation."

    invoke-static {v0, v1}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 91
    monitor-exit p0

    return-void

    .line 93
    :catchall_0
    move-exception v0

    monitor-exit p0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw v0
.end method

.method public final Stop()V
    .locals 3

    .line 132
    invoke-virtual {p0}, Lme/embodied/services/Service;->ServiceName()Ljava/lang/String;

    move-result-object v0

    const-string v1, "Stopping service"

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 133
    new-instance v0, Lme/embodied/services/Service$TimedRelease;

    invoke-direct {v0, p0}, Lme/embodied/services/Service$TimedRelease;-><init>(Lme/embodied/services/Service;)V

    const-wide/16 v1, 0x2710

    invoke-virtual {v0, v1, v2}, Lme/embodied/services/Service$TimedRelease;->timed_release(J)Z

    move-result v0

    .line 134
    if-eqz v0, :cond_0

    .line 135
    invoke-virtual {p0}, Lme/embodied/services/Service;->ServiceName()Ljava/lang/String;

    move-result-object v0

    const-string v1, "Released native service"

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_0

    .line 137
    :cond_0
    invoke-virtual {p0}, Lme/embodied/services/Service;->ServiceName()Ljava/lang/String;

    move-result-object v0

    const-string v1, "Service exit did not complete in time"

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 138
    :goto_0
    const-wide/16 v0, 0x0

    iput-wide v0, p0, Lme/embodied/services/Service;->ptr:J

    .line 139
    return-void
.end method

.method public onBind(Landroid/content/Intent;)Landroid/os/IBinder;
    .locals 0

    .line 39
    const/4 p1, 0x0

    return-object p1
.end method

.method public onDestroy()V
    .locals 2

    .line 63
    invoke-virtual {p0}, Lme/embodied/services/Service;->Stop()V

    .line 64
    invoke-super {p0}, Landroid/app/Service;->onDestroy()V

    .line 71
    invoke-virtual {p0}, Lme/embodied/services/Service;->ServiceName()Ljava/lang/String;

    move-result-object v0

    const-string v1, "Killing service process."

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 72
    invoke-static {}, Landroid/os/Process;->myPid()I

    move-result v0

    invoke-static {v0}, Landroid/os/Process;->killProcess(I)V

    .line 73
    return-void
.end method

.method public onStartCommand(Landroid/content/Intent;II)I
    .locals 8

    .line 46
    iget-wide p1, p0, Lme/embodied/services/Service;->ptr:J

    const-wide/16 v0, 0x0

    cmp-long p1, p1, v0

    if-nez p1, :cond_1

    .line 47
    new-instance v4, Lme/embodied/services/Service$1;

    invoke-direct {v4, p0}, Lme/embodied/services/Service$1;-><init>(Lme/embodied/services/Service;)V

    .line 52
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string p2, "Main-"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object p2, p0, Lme/embodied/services/Service;->lib_name_:Ljava/lang/String;

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    .line 53
    iget-wide p1, p0, Lme/embodied/services/Service;->custom_stack_size_:J

    cmp-long p1, p1, v0

    if-lez p1, :cond_0

    new-instance p1, Ljava/lang/Thread;

    const/4 v3, 0x0

    iget-wide v6, p0, Lme/embodied/services/Service;->custom_stack_size_:J

    move-object v2, p1

    invoke-direct/range {v2 .. v7}, Ljava/lang/Thread;-><init>(Ljava/lang/ThreadGroup;Ljava/lang/Runnable;Ljava/lang/String;J)V

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/Thread;

    const/4 p2, 0x0

    invoke-direct {p1, p2, v4, v5}, Ljava/lang/Thread;-><init>(Ljava/lang/ThreadGroup;Ljava/lang/Runnable;Ljava/lang/String;)V

    :goto_0
    iput-object p1, p0, Lme/embodied/services/Service;->thread_:Ljava/lang/Thread;

    .line 56
    invoke-virtual {p1}, Ljava/lang/Thread;->start()V

    .line 58
    :cond_1
    const/4 p1, 0x1

    return p1
.end method
