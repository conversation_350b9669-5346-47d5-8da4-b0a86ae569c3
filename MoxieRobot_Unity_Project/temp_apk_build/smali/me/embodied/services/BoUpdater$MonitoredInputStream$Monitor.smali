.class public interface abstract Lme/embodied/services/BoUpdater$MonitoredInputStream$Monitor;
.super Ljava/lang/Object;
.source "BoUpdater.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lme/embodied/services/BoUpdater$MonitoredInputStream;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "Monitor"
.end annotation


# virtual methods
.method public abstract notify(JJ)V
.end method
