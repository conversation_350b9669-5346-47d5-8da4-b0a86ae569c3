.class public Lme/embodied/services/BoBrain;
.super Lme/embodied/services/Service;
.source "BoBrain.java"


# static fields
.field static final BRAIN_STACK_SIZE:J = 0x800000L


# direct methods
.method public constructor <init>()V
    .locals 3

    .line 6
    const-string v0, "bo-brain"

    const-wide/32 v1, 0x800000

    invoke-direct {p0, v0, v1, v2}, Lme/embodied/services/Service;-><init>(Ljava/lang/String;J)V

    .line 7
    return-void
.end method
