.class Lme/embodied/services/Launcher$SuspendWatcher$1;
.super Ljava/lang/Thread;
.source "Launcher.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lme/embodied/services/Launcher$SuspendWatcher;->startSuspendWatch(Z)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$1:Lme/embodied/services/Launcher$SuspendWatcher;


# direct methods
.method constructor <init>(Lme/embodied/services/Launcher$SuspendWatcher;)V
    .locals 0

    .line 1162
    iput-object p1, p0, Lme/embodied/services/Launcher$SuspendWatcher$1;->this$1:Lme/embodied/services/Launcher$SuspendWatcher;

    invoke-direct {p0}, Ljava/lang/Thread;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 1

    .line 1164
    iget-object v0, p0, Lme/embodied/services/Launcher$SuspendWatcher$1;->this$1:Lme/embodied/services/Launcher$SuspendWatcher;

    invoke-virtual {v0}, Lme/embodied/services/Launcher$SuspendWatcher;->run_loop()V

    .line 1165
    return-void
.end method
