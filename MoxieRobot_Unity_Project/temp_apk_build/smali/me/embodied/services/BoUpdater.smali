.class public Lme/embodied/services/BoUpdater;
.super Landroid/app/Service;
.source "BoUpdater.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lme/embodied/services/BoUpdater$MonitoredInputStream;
    }
.end annotation


# static fields
.field private static final DATA_PATH:Ljava/lang/String; = "/sdcard/EmbodiedData"

.field private static final DISABLE_OTA_PATH:Ljava/lang/String; = "/sdcard/EmbodiedData/DISABLE_OTA"

.field private static final MIN_DOWNLOAD_PROGRESS_NOTIFY:F = 0.01f

.field private static final MIN_IDLE_FREE_DISK:J = 0x20000000L

.field private static final OTA_DOWNLOAD_DIR:Ljava/lang/String; = "/sdcard/EmbodiedData/otaImages"

.field private static final OTA_HTTP_DL_TIMEOUT:I = 0x7530

.field private static final OTA_INFO_FILE:Ljava/lang/String; = "/sdcard/EmbodiedData/otaInfo.txt"

.field private static final OTA_LOG_MAX_SIZE:J = 0xa00000L

.field private static final OTA_LOG_PATH:Ljava/lang/String; = "/sdcard/EmbodiedData/otaLog.txt"

.field private static final OTA_LOG_ROTATIONS:I = 0x5

.field private static final OTA_OVERAGE_BUDGET:J = 0xc800000L

.field private static final OTA_STATUS_ACTION:Ljava/lang/String; = "com.embodied.osctrl.ota_status"

.field private static final STATIC_DATA_PATH:Ljava/lang/String; = "/sdcard/EmbodiedStaticData"

.field static final TAG:Ljava/lang/String; = "BoUpdater"

.field static final UTC:Ljava/time/ZoneId;

.field private static final WAIT_PERIOD_MILLIS:J = 0x1388L

.field static lastUpdateVersion:Ljava/lang/String;

.field private static final otaLogDateTimeFormatter:Ljava/time/format/DateTimeFormatter;


# instance fields
.field cachedOtaVersion:Ljava/lang/String;

.field private checking_:Z

.field private disk_cleaner_:Lme/embodied/DiskCleanup;

.field private last_ota_warn_version_:Ljava/lang/String;

.field os_props_:Lme/embodied/AndroidSystemProps;

.field private os_revision_:I

.field private thread_:Ljava/lang/Thread;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 66
    const/4 v0, 0x0

    sput-object v0, Lme/embodied/services/BoUpdater;->lastUpdateVersion:Ljava/lang/String;

    .line 154
    const-string v0, "yyyyMMdd\'T\'HHmmss.SSSSSS"

    invoke-static {v0}, Ljava/time/format/DateTimeFormatter;->ofPattern(Ljava/lang/String;)Ljava/time/format/DateTimeFormatter;

    move-result-object v0

    sput-object v0, Lme/embodied/services/BoUpdater;->otaLogDateTimeFormatter:Ljava/time/format/DateTimeFormatter;

    .line 155
    const-string v0, "UTC"

    invoke-static {v0}, Ljava/time/ZoneId;->of(Ljava/lang/String;)Ljava/time/ZoneId;

    move-result-object v0

    sput-object v0, Lme/embodied/services/BoUpdater;->UTC:Ljava/time/ZoneId;

    return-void
.end method

.method public constructor <init>()V
    .locals 3

    .line 68
    invoke-direct {p0}, Landroid/app/Service;-><init>()V

    .line 41
    const/4 v0, 0x0

    iput-object v0, p0, Lme/embodied/services/BoUpdater;->thread_:Ljava/lang/Thread;

    .line 42
    const/4 v1, 0x1

    iput-boolean v1, p0, Lme/embodied/services/BoUpdater;->checking_:Z

    .line 44
    iput-object v0, p0, Lme/embodied/services/BoUpdater;->last_ota_warn_version_:Ljava/lang/String;

    .line 194
    iput-object v0, p0, Lme/embodied/services/BoUpdater;->cachedOtaVersion:Ljava/lang/String;

    .line 69
    new-instance v0, Lme/embodied/DiskCleanup;

    new-instance v1, Ljava/io/File;

    const-string v2, "/sdcard/EmbodiedData"

    invoke-direct {v1, v2}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-direct {v0, v1}, Lme/embodied/DiskCleanup;-><init>(Ljava/io/File;)V

    iput-object v0, p0, Lme/embodied/services/BoUpdater;->disk_cleaner_:Lme/embodied/DiskCleanup;

    .line 70
    new-instance v1, Ljava/io/File;

    const-string v2, "/sdcard/EmbodiedData/DISABLE_OTA"

    invoke-direct {v1, v2}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Lme/embodied/DiskCleanup;->addExemption(Ljava/io/File;)V

    .line 71
    iget-object v0, p0, Lme/embodied/services/BoUpdater;->disk_cleaner_:Lme/embodied/DiskCleanup;

    new-instance v1, Ljava/io/File;

    const-string v2, "/sdcard/EmbodiedData/otaInfo.txt"

    invoke-direct {v1, v2}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Lme/embodied/DiskCleanup;->addExemption(Ljava/io/File;)V

    .line 72
    iget-object v0, p0, Lme/embodied/services/BoUpdater;->disk_cleaner_:Lme/embodied/DiskCleanup;

    new-instance v1, Ljava/io/File;

    const-string v2, "/sdcard/EmbodiedData/otaLog.txt"

    invoke-direct {v1, v2}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Lme/embodied/DiskCleanup;->addExemption(Ljava/io/File;)V

    .line 73
    iget-object v0, p0, Lme/embodied/services/BoUpdater;->disk_cleaner_:Lme/embodied/DiskCleanup;

    new-instance v1, Ljava/io/File;

    const-string v2, "/sdcard/EmbodiedData/otaImages"

    invoke-direct {v1, v2}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Lme/embodied/DiskCleanup;->addExemption(Ljava/io/File;)V

    .line 74
    return-void
.end method

.method private ReportStartup()V
    .locals 3

    .line 107
    new-instance v0, Landroid/content/Intent;

    invoke-direct {v0}, Landroid/content/Intent;-><init>()V

    .line 108
    const-string v1, "com.embodied.service_report"

    invoke-virtual {v0, v1}, Landroid/content/Intent;->setAction(Ljava/lang/String;)Landroid/content/Intent;

    .line 109
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Class;->getCanonicalName()Ljava/lang/String;

    move-result-object v1

    const-string v2, "service"

    invoke-virtual {v0, v2, v1}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Ljava/lang/String;)Landroid/content/Intent;

    .line 110
    invoke-virtual {p0, v0}, Lme/embodied/services/BoUpdater;->sendBroadcast(Landroid/content/Intent;)V

    .line 111
    return-void
.end method

.method static synthetic access$000(Lme/embodied/services/BoUpdater;F)V
    .locals 0

    .line 39
    invoke-direct {p0, p1}, Lme/embodied/services/BoUpdater;->handleDownloadProgress(F)V

    return-void
.end method

.method public static bundleToString(Landroid/os/Bundle;)Ljava/lang/String;
    .locals 4

    .line 488
    new-instance v0, Ljava/lang/StringBuilder;

    const-string v1, "Bundle["

    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 490
    if-nez p0, :cond_0

    .line 491
    const-string p0, "null"

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto/16 :goto_2

    .line 493
    :cond_0
    const/4 v1, 0x1

    .line 494
    invoke-virtual {p0}, Landroid/os/Bundle;->keySet()Ljava/util/Set;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_d

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/String;

    .line 495
    if-nez v1, :cond_1

    .line 496
    const-string v1, ", "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 498
    :cond_1
    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/16 v1, 0x3d

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 499
    invoke-virtual {p0, v3}, Landroid/os/Bundle;->get(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v1

    .line 500
    instance-of v3, v1, [I

    if-eqz v3, :cond_2

    .line 501
    check-cast v1, [I

    check-cast v1, [I

    invoke-static {v1}, Ljava/util/Arrays;->toString([I)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto/16 :goto_1

    .line 502
    :cond_2
    instance-of v3, v1, [B

    if-eqz v3, :cond_3

    .line 503
    check-cast v1, [B

    check-cast v1, [B

    invoke-static {v1}, Ljava/util/Arrays;->toString([B)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto/16 :goto_1

    .line 504
    :cond_3
    instance-of v3, v1, [Z

    if-eqz v3, :cond_4

    .line 505
    check-cast v1, [Z

    check-cast v1, [Z

    invoke-static {v1}, Ljava/util/Arrays;->toString([Z)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto/16 :goto_1

    .line 506
    :cond_4
    instance-of v3, v1, [S

    if-eqz v3, :cond_5

    .line 507
    check-cast v1, [S

    check-cast v1, [S

    invoke-static {v1}, Ljava/util/Arrays;->toString([S)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto/16 :goto_1

    .line 508
    :cond_5
    instance-of v3, v1, [J

    if-eqz v3, :cond_6

    .line 509
    check-cast v1, [J

    check-cast v1, [J

    invoke-static {v1}, Ljava/util/Arrays;->toString([J)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_1

    .line 510
    :cond_6
    instance-of v3, v1, [F

    if-eqz v3, :cond_7

    .line 511
    check-cast v1, [F

    check-cast v1, [F

    invoke-static {v1}, Ljava/util/Arrays;->toString([F)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_1

    .line 512
    :cond_7
    instance-of v3, v1, [D

    if-eqz v3, :cond_8

    .line 513
    check-cast v1, [D

    check-cast v1, [D

    invoke-static {v1}, Ljava/util/Arrays;->toString([D)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_1

    .line 514
    :cond_8
    instance-of v3, v1, [Ljava/lang/String;

    if-eqz v3, :cond_9

    .line 515
    check-cast v1, [Ljava/lang/String;

    check-cast v1, [Ljava/lang/String;

    invoke-static {v1}, Ljava/util/Arrays;->toString([Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_1

    .line 516
    :cond_9
    instance-of v3, v1, [Ljava/lang/CharSequence;

    if-eqz v3, :cond_a

    .line 517
    check-cast v1, [Ljava/lang/CharSequence;

    check-cast v1, [Ljava/lang/CharSequence;

    invoke-static {v1}, Ljava/util/Arrays;->toString([Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_1

    .line 518
    :cond_a
    instance-of v3, v1, [Landroid/os/Parcelable;

    if-eqz v3, :cond_b

    .line 519
    check-cast v1, [Landroid/os/Parcelable;

    check-cast v1, [Landroid/os/Parcelable;

    invoke-static {v1}, Ljava/util/Arrays;->toString([Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_1

    .line 520
    :cond_b
    instance-of v3, v1, Landroid/os/Bundle;

    if-eqz v3, :cond_c

    .line 521
    check-cast v1, Landroid/os/Bundle;

    invoke-static {v1}, Lme/embodied/services/BoUpdater;->bundleToString(Landroid/os/Bundle;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_1

    .line 523
    :cond_c
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 526
    :goto_1
    const/4 v1, 0x0

    .line 527
    goto/16 :goto_0

    .line 530
    :cond_d
    :goto_2
    const-string p0, "]"

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 531
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method private cleanupDownloads()V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 212
    const-string v0, "/sdcard/EmbodiedData/otaImages"

    invoke-static {v0}, Lme/embodied/services/BoUpdater;->filesFromDir(Ljava/lang/String;)Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/io/File;

    .line 213
    invoke-virtual {v1}, Ljava/io/File;->delete()Z

    goto :goto_0

    .line 214
    :cond_0
    return-void
.end method

.method private static filesFromDir(Ljava/lang/String;)Ljava/util/List;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            ")",
            "Ljava/util/List<",
            "Ljava/io/File;",
            ">;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 180
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 181
    new-instance v1, Ljava/io/File;

    invoke-direct {v1, p0}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    .line 182
    invoke-virtual {v1}, Ljava/io/File;->isDirectory()Z

    move-result v2

    if-nez v2, :cond_1

    invoke-virtual {v1}, Ljava/io/File;->mkdirs()Z

    move-result v2

    if-eqz v2, :cond_0

    goto :goto_0

    .line 183
    :cond_0
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "failed to mkdirs "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "BoUpdater"

    invoke-static {v1, v0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 184
    new-instance v0, Ljava/io/IOException;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "error creating ota download area: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-direct {v0, p0}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 186
    :cond_1
    :goto_0
    invoke-virtual {v1}, Ljava/io/File;->listFiles()[Ljava/io/File;

    move-result-object p0

    array-length v1, p0

    const/4 v2, 0x0

    :goto_1
    if-ge v2, v1, :cond_3

    aget-object v3, p0, v2

    .line 187
    invoke-virtual {v3}, Ljava/io/File;->isDirectory()Z

    move-result v4

    if-eqz v4, :cond_2

    .line 188
    goto :goto_2

    .line 189
    :cond_2
    invoke-virtual {v0, v3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 186
    :goto_2
    add-int/lit8 v2, v2, 0x1

    goto :goto_1

    .line 191
    :cond_3
    return-object v0
.end method

.method private handleDownloadProgress(F)V
    .locals 3

    .line 202
    new-instance v0, Landroid/content/Intent;

    invoke-direct {v0}, Landroid/content/Intent;-><init>()V

    .line 203
    const-string v1, "com.embodied.osctrl.ota_status"

    invoke-virtual {v0, v1}, Landroid/content/Intent;->setAction(Ljava/lang/String;)Landroid/content/Intent;

    .line 204
    const-string v1, "update_status"

    const/4 v2, 0x1

    invoke-virtual {v0, v1, v2}, Landroid/content/Intent;->putExtra(Ljava/lang/String;I)Landroid/content/Intent;

    .line 205
    const-string v1, "update_percent"

    invoke-virtual {v0, v1, p1}, Landroid/content/Intent;->putExtra(Ljava/lang/String;F)Landroid/content/Intent;

    .line 206
    const-string p1, "BoUpdater"

    const-string v1, "sending download-progress intent"

    invoke-static {p1, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 207
    invoke-virtual {p0, v0}, Lme/embodied/services/BoUpdater;->sendBroadcast(Landroid/content/Intent;)V

    .line 208
    return-void
.end method

.method public static intentToString(Landroid/content/Intent;)Ljava/lang/String;
    .locals 2

    .line 482
    if-nez p0, :cond_0

    .line 483
    const/4 p0, 0x0

    return-object p0

    .line 485
    :cond_0
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p0}, Landroid/content/Intent;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, " "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p0}, Landroid/content/Intent;->getExtras()Landroid/os/Bundle;

    move-result-object p0

    invoke-static {p0}, Lme/embodied/services/BoUpdater;->bundleToString(Landroid/os/Bundle;)Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method private isTargetVersionCompatible(Ljava/lang/String;)Z
    .locals 3

    .line 217
    iget-object v0, p0, Lme/embodied/services/BoUpdater;->os_props_:Lme/embodied/AndroidSystemProps;

    invoke-virtual {v0}, Lme/embodied/AndroidSystemProps;->getMinOTAOSRevision()I

    move-result v0

    const/4 v1, 0x1

    if-eqz v0, :cond_1

    .line 218
    invoke-static {p1}, Lme/embodied/AndroidSystemProps;->ParseOSVersion(Ljava/lang/String;)I

    move-result v0

    .line 219
    if-nez v0, :cond_0

    .line 220
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "BO#3042 Minumum OTA version configured, but target version could not be determined from: "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const-string v0, "BoUpdater"

    invoke-static {v0, p1}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 222
    return v1

    .line 223
    :cond_0
    iget-object p1, p0, Lme/embodied/services/BoUpdater;->os_props_:Lme/embodied/AndroidSystemProps;

    invoke-virtual {p1}, Lme/embodied/AndroidSystemProps;->getMinOTAOSRevision()I

    move-result p1

    if-ge v0, p1, :cond_1

    .line 224
    const/4 p1, 0x0

    return p1

    .line 227
    :cond_1
    return v1
.end method

.method private static nowAsTimestamp()Ljava/lang/String;
    .locals 2

    .line 158
    sget-object v0, Lme/embodied/services/BoUpdater;->UTC:Ljava/time/ZoneId;

    invoke-static {v0}, Ljava/time/LocalDateTime;->now(Ljava/time/ZoneId;)Ljava/time/LocalDateTime;

    move-result-object v0

    .line 159
    sget-object v1, Lme/embodied/services/BoUpdater;->otaLogDateTimeFormatter:Ljava/time/format/DateTimeFormatter;

    invoke-virtual {v1, v0}, Ljava/time/format/DateTimeFormatter;->format(Ljava/time/temporal/TemporalAccessor;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method private static readLineFromFile(Ljava/lang/String;)Ljava/lang/String;
    .locals 5
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 170
    new-instance v0, Ljava/io/FileReader;

    invoke-direct {v0, p0}, Ljava/io/FileReader;-><init>(Ljava/lang/String;)V

    .line 171
    :try_start_0
    new-instance v1, Ljava/io/BufferedReader;

    invoke-direct {v1, v0}, Ljava/io/BufferedReader;-><init>(Ljava/io/Reader;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_3

    .line 170
    nop

    .line 172
    :try_start_1
    invoke-virtual {v1}, Ljava/io/BufferedReader;->readLine()Ljava/lang/String;

    move-result-object v2
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 173
    if-eqz v2, :cond_0

    .line 175
    nop

    .line 176
    :try_start_2
    invoke-virtual {v1}, Ljava/io/BufferedReader;->close()V
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_3

    invoke-virtual {v0}, Ljava/io/FileReader;->close()V

    .line 175
    return-object v2

    .line 174
    :cond_0
    :try_start_3
    new-instance v2, Ljava/io/IOException;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "no line of text found in "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-direct {v2, p0}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    throw v2
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    .line 170
    :catchall_0
    move-exception p0

    :try_start_4
    throw p0
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_1

    .line 176
    :catchall_1
    move-exception v2

    :try_start_5
    invoke-virtual {v1}, Ljava/io/BufferedReader;->close()V
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_2

    goto :goto_0

    :catchall_2
    move-exception v1

    :try_start_6
    invoke-virtual {p0, v1}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    :goto_0
    throw v2
    :try_end_6
    .catchall {:try_start_6 .. :try_end_6} :catchall_3

    .line 170
    :catchall_3
    move-exception p0

    :try_start_7
    throw p0
    :try_end_7
    .catchall {:try_start_7 .. :try_end_7} :catchall_4

    .line 176
    :catchall_4
    move-exception v1

    :try_start_8
    invoke-virtual {v0}, Ljava/io/FileReader;->close()V
    :try_end_8
    .catchall {:try_start_8 .. :try_end_8} :catchall_5

    goto :goto_1

    :catchall_5
    move-exception v0

    invoke-virtual {p0, v0}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    :goto_1
    throw v1
.end method

.method private static writeOtaLog(Ljava/lang/String;)V
    .locals 3

    .line 162
    :try_start_0
    new-instance v0, Ljava/io/FileWriter;

    const-string v1, "/sdcard/EmbodiedData/otaLog.txt"

    const/4 v2, 0x1

    invoke-direct {v0, v1, v2}, Ljava/io/FileWriter;-><init>(Ljava/lang/String;Z)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    .line 163
    const/4 v1, 0x0

    :try_start_1
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result v2

    invoke-virtual {v0, p0, v1, v2}, Ljava/io/FileWriter;->write(Ljava/lang/String;II)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 164
    :try_start_2
    invoke-virtual {v0}, Ljava/io/FileWriter;->close()V
    :try_end_2
    .catch Ljava/io/IOException; {:try_start_2 .. :try_end_2} :catch_0

    .line 166
    goto :goto_1

    .line 162
    :catchall_0
    move-exception p0

    :try_start_3
    throw p0
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_1

    .line 164
    :catchall_1
    move-exception v1

    :try_start_4
    invoke-virtual {v0}, Ljava/io/FileWriter;->close()V
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_2

    goto :goto_0

    :catchall_2
    move-exception v0

    :try_start_5
    invoke-virtual {p0, v0}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    :goto_0
    throw v1
    :try_end_5
    .catch Ljava/io/IOException; {:try_start_5 .. :try_end_5} :catch_0

    :catch_0
    move-exception p0

    .line 165
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "writing OTA log: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    const-string v0, "BoUpdater"

    invoke-static {v0, p0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 167
    :goto_1
    return-void
.end method


# virtual methods
.method public CheckForUpdates()V
    .locals 23
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 232
    move-object/from16 v1, p0

    new-instance v0, Ljava/io/File;

    const-string v2, "/sdcard/EmbodiedData/otaInfo.txt"

    invoke-direct {v0, v2}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    .line 233
    invoke-virtual {v0}, Ljava/io/File;->exists()Z

    move-result v0

    if-nez v0, :cond_0

    .line 234
    return-void

    .line 236
    :cond_0
    nop

    .line 237
    new-instance v3, Ljava/io/FileReader;

    invoke-direct {v3, v2}, Ljava/io/FileReader;-><init>(Ljava/lang/String;)V

    .line 238
    :try_start_0
    new-instance v2, Ljava/io/BufferedReader;

    invoke-direct {v2, v3}, Ljava/io/BufferedReader;-><init>(Ljava/io/Reader;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_29

    .line 237
    nop

    .line 239
    :try_start_1
    invoke-virtual {v2}, Ljava/io/BufferedReader;->readLine()Ljava/lang/String;

    move-result-object v0

    .line 240
    invoke-virtual {v2}, Ljava/io/BufferedReader;->readLine()Ljava/lang/String;

    move-result-object v4
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_26

    .line 241
    :try_start_2
    invoke-virtual {v2}, Ljava/io/BufferedReader;->close()V
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_29

    invoke-virtual {v3}, Ljava/io/FileReader;->close()V

    .line 242
    const-string v2, "BoUpdater"

    if-eqz v0, :cond_23

    if-nez v4, :cond_1

    goto/16 :goto_25

    .line 249
    :cond_1
    sget-object v3, Lme/embodied/services/BoUpdater;->lastUpdateVersion:Ljava/lang/String;

    if-eqz v3, :cond_2

    invoke-virtual {v3, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_2

    .line 250
    return-void

    .line 254
    :cond_2
    invoke-virtual/range {p0 .. p0}, Lme/embodied/services/BoUpdater;->getSystemOtaVersion()Ljava/lang/String;

    move-result-object v3

    .line 255
    invoke-virtual {v4, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_3

    .line 256
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "presently system is at desired OTA version "

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v2, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 258
    invoke-direct/range {p0 .. p0}, Lme/embodied/services/BoUpdater;->cleanupDownloads()V

    .line 259
    return-void

    .line 262
    :cond_3
    new-instance v3, Ljava/io/File;

    const-string v5, "/sdcard/EmbodiedData/DISABLE_OTA"

    invoke-direct {v3, v5}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    .line 263
    invoke-virtual {v3}, Ljava/io/File;->exists()Z

    move-result v3

    if-eqz v3, :cond_4

    .line 264
    const-string v0, "OTA updates disabled since /sdcard/EmbodiedData/DISABLE_OTA exists"

    invoke-static {v2, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 265
    return-void

    .line 269
    :cond_4
    invoke-direct {v1, v4}, Lme/embodied/services/BoUpdater;->isTargetVersionCompatible(Ljava/lang/String;)Z

    move-result v3

    if-nez v3, :cond_7

    .line 272
    iget-object v0, v1, Lme/embodied/services/BoUpdater;->last_ota_warn_version_:Ljava/lang/String;

    if-eqz v0, :cond_5

    invoke-virtual {v0, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_6

    .line 274
    :cond_5
    iput-object v4, v1, Lme/embodied/services/BoUpdater;->last_ota_warn_version_:Ljava/lang/String;

    .line 275
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "BO#3041 Target OTA OS version is unsupported: "

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v2, v0}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 277
    :cond_6
    return-void

    .line 281
    :cond_7
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "/sdcard/EmbodiedData/otaImages/"

    invoke-virtual {v3, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    .line 282
    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v5, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v6, ".properties"

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    .line 283
    nop

    .line 284
    nop

    .line 285
    nop

    .line 286
    nop

    .line 287
    nop

    .line 288
    const-string v6, "trying to read properties"

    invoke-static {v2, v6}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 289
    new-instance v6, Ljava/io/File;

    invoke-direct {v6, v5}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-virtual {v6}, Ljava/io/File;->exists()Z

    move-result v6

    const/4 v7, 0x1

    const/4 v8, 0x0

    const/4 v9, 0x0

    if-eqz v6, :cond_b

    .line 290
    new-instance v6, Ljava/io/FileReader;

    invoke-direct {v6, v5}, Ljava/io/FileReader;-><init>(Ljava/lang/String;)V

    .line 291
    :try_start_3
    new-instance v10, Ljava/io/BufferedReader;

    invoke-direct {v10, v6}, Ljava/io/BufferedReader;-><init>(Ljava/io/Reader;)V
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_3

    .line 290
    nop

    .line 292
    const/4 v11, 0x4

    :try_start_4
    new-array v12, v11, [Ljava/lang/String;

    .line 293
    move v13, v9

    :goto_0
    if-ge v13, v11, :cond_9

    .line 294
    invoke-virtual {v10}, Ljava/io/BufferedReader;->readLine()Ljava/lang/String;

    move-result-object v14

    aput-object v14, v12, v13

    .line 295
    aget-object v14, v12, v13

    if-nez v14, :cond_8

    .line 296
    nop

    .line 297
    move-object v12, v8

    goto :goto_1

    .line 293
    :cond_8
    add-int/lit8 v13, v13, 0x1

    goto :goto_0

    .line 300
    :cond_9
    :goto_1
    if-eqz v12, :cond_a

    .line 301
    aget-object v8, v12, v9

    .line 302
    aget-object v11, v12, v7

    .line 303
    const/4 v13, 0x2

    aget-object v13, v12, v13

    .line 304
    const/4 v14, 0x3

    aget-object v12, v12, v14
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_0

    .line 305
    move v14, v7

    goto :goto_2

    .line 300
    :cond_a
    move-object v11, v8

    move-object v12, v11

    move-object v13, v12

    move v14, v9

    .line 307
    :goto_2
    :try_start_5
    invoke-virtual {v10}, Ljava/io/BufferedReader;->close()V
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_3

    invoke-virtual {v6}, Ljava/io/FileReader;->close()V

    goto :goto_5

    .line 290
    :catchall_0
    move-exception v0

    move-object v2, v0

    :try_start_6
    throw v2
    :try_end_6
    .catchall {:try_start_6 .. :try_end_6} :catchall_1

    .line 307
    :catchall_1
    move-exception v0

    move-object v3, v0

    :try_start_7
    invoke-virtual {v10}, Ljava/io/BufferedReader;->close()V
    :try_end_7
    .catchall {:try_start_7 .. :try_end_7} :catchall_2

    goto :goto_3

    :catchall_2
    move-exception v0

    move-object v4, v0

    :try_start_8
    invoke-virtual {v2, v4}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    :goto_3
    throw v3
    :try_end_8
    .catchall {:try_start_8 .. :try_end_8} :catchall_3

    .line 290
    :catchall_3
    move-exception v0

    move-object v2, v0

    :try_start_9
    throw v2
    :try_end_9
    .catchall {:try_start_9 .. :try_end_9} :catchall_4

    .line 307
    :catchall_4
    move-exception v0

    move-object v3, v0

    :try_start_a
    invoke-virtual {v6}, Ljava/io/FileReader;->close()V
    :try_end_a
    .catchall {:try_start_a .. :try_end_a} :catchall_5

    goto :goto_4

    :catchall_5
    move-exception v0

    move-object v4, v0

    invoke-virtual {v2, v4}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    :goto_4
    throw v3

    .line 289
    :cond_b
    move-object v11, v8

    move-object v12, v11

    move-object v13, v12

    move v14, v9

    .line 309
    :goto_5
    new-instance v6, Ljava/io/File;

    invoke-direct {v6, v3}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    .line 310
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v10, "finalFile="

    invoke-virtual {v3, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v10, "; gotProperties="

    invoke-virtual {v3, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v14}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-static {v2, v3}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 311
    invoke-virtual {v6}, Ljava/io/File;->exists()Z

    move-result v3

    const-string v10, "\n"

    if-eqz v3, :cond_c

    if-nez v14, :cond_21

    .line 314
    :cond_c
    invoke-virtual/range {p0 .. p0}, Lme/embodied/services/BoUpdater;->cancelPendingOTAs()V

    .line 317
    invoke-direct/range {p0 .. p0}, Lme/embodied/services/BoUpdater;->cleanupDownloads()V

    .line 319
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v15, "/sdcard/EmbodiedData/otaImages/.tmp-"

    invoke-virtual {v3, v15}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    .line 321
    const/16 v15, 0x4000

    new-array v15, v15, [B

    .line 322
    nop

    .line 323
    new-instance v7, Ljava/net/URL;

    invoke-direct {v7, v0}, Ljava/net/URL;-><init>(Ljava/lang/String;)V

    .line 324
    invoke-virtual {v7}, Ljava/net/URL;->openConnection()Ljava/net/URLConnection;

    move-result-object v0

    check-cast v0, Ljava/net/HttpURLConnection;

    .line 325
    const/16 v9, 0x7530

    invoke-virtual {v0, v9}, Ljava/net/HttpURLConnection;->setConnectTimeout(I)V

    .line 326
    invoke-virtual {v0, v9}, Ljava/net/HttpURLConnection;->setReadTimeout(I)V

    .line 327
    invoke-virtual {v0}, Ljava/net/HttpURLConnection;->connect()V

    .line 328
    new-instance v9, Ljava/lang/StringBuilder;

    invoke-direct {v9}, Ljava/lang/StringBuilder;-><init>()V

    move-object/from16 v17, v8

    const-string v8, "about to start download from "

    invoke-virtual {v9, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v9, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v7, " to "

    invoke-virtual {v9, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v9, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v9}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    invoke-static {v2, v7}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 329
    new-instance v7, Lme/embodied/services/BoUpdater$2;

    invoke-direct {v7, v1, v6}, Lme/embodied/services/BoUpdater$2;-><init>(Lme/embodied/services/BoUpdater;Ljava/io/File;)V

    .line 345
    iget-object v8, v1, Lme/embodied/services/BoUpdater;->disk_cleaner_:Lme/embodied/DiskCleanup;

    invoke-virtual {v0}, Ljava/net/HttpURLConnection;->getContentLengthLong()J

    move-result-wide v18

    const-wide/32 v20, 0xc800000

    move-object v9, v11

    move-object/from16 v22, v12

    add-long v11, v18, v20

    move-object/from16 v18, v9

    const/4 v9, 0x0

    invoke-virtual {v8, v11, v12, v9}, Lme/embodied/DiskCleanup;->ensureFreeSpace(JZ)Z

    .line 346
    :try_start_b
    invoke-virtual {v0}, Ljava/net/HttpURLConnection;->getInputStream()Ljava/io/InputStream;

    move-result-object v8
    :try_end_b
    .catch Ljava/lang/Exception; {:try_start_b .. :try_end_b} :catch_4

    .line 347
    :try_start_c
    new-instance v9, Lme/embodied/services/BoUpdater$MonitoredInputStream;

    .line 348
    invoke-virtual {v0}, Ljava/net/HttpURLConnection;->getContentLengthLong()J

    move-result-wide v11

    invoke-direct {v9, v8, v11, v12, v7}, Lme/embodied/services/BoUpdater$MonitoredInputStream;-><init>(Ljava/io/InputStream;JLme/embodied/services/BoUpdater$MonitoredInputStream$Monitor;)V
    :try_end_c
    .catchall {:try_start_c .. :try_end_c} :catchall_1d

    .line 346
    nop

    .line 350
    :try_start_d
    new-instance v7, Ljava/io/BufferedInputStream;

    invoke-direct {v7, v9}, Ljava/io/BufferedInputStream;-><init>(Ljava/io/InputStream;)V
    :try_end_d
    .catchall {:try_start_d .. :try_end_d} :catchall_19

    .line 346
    nop

    .line 351
    :try_start_e
    new-instance v11, Ljava/util/zip/ZipInputStream;

    invoke-direct {v11, v7}, Ljava/util/zip/ZipInputStream;-><init>(Ljava/io/InputStream;)V
    :try_end_e
    .catchall {:try_start_e .. :try_end_e} :catchall_15

    .line 346
    const/4 v12, 0x0

    .line 353
    :goto_6
    :try_start_f
    invoke-virtual {v11}, Ljava/util/zip/ZipInputStream;->getNextEntry()Ljava/util/zip/ZipEntry;

    move-result-object v0
    :try_end_f
    .catchall {:try_start_f .. :try_end_f} :catchall_11

    if-eqz v0, :cond_1c

    .line 354
    move/from16 v19, v12

    :try_start_10
    new-instance v12, Ljava/lang/StringBuilder;

    invoke-direct {v12}, Ljava/lang/StringBuilder;-><init>()V
    :try_end_10
    .catchall {:try_start_10 .. :try_end_10} :catchall_c

    move-object/from16 v20, v13

    :try_start_11
    const-string v13, "got zip entry "

    invoke-virtual {v12, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/util/zip/ZipEntry;->getName()Ljava/lang/String;

    move-result-object v13

    invoke-virtual {v12, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v12

    invoke-static {v2, v12}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 355
    invoke-virtual {v0}, Ljava/util/zip/ZipEntry;->getName()Ljava/lang/String;

    move-result-object v12

    const-string v13, "payload.bin"

    invoke-virtual {v12, v13}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v12

    if-eqz v12, :cond_e

    .line 356
    new-instance v12, Ljava/io/FileOutputStream;

    invoke-direct {v12, v3}, Ljava/io/FileOutputStream;-><init>(Ljava/lang/String;)V
    :try_end_11
    .catchall {:try_start_11 .. :try_end_11} :catchall_10

    .line 357
    :goto_7
    :try_start_12
    invoke-virtual {v11, v15}, Ljava/util/zip/ZipInputStream;->read([B)I

    move-result v0

    if-lez v0, :cond_d

    .line 358
    const/4 v13, 0x0

    invoke-virtual {v12, v15, v13, v0}, Ljava/io/OutputStream;->write([BII)V
    :try_end_12
    .catchall {:try_start_12 .. :try_end_12} :catchall_6

    goto :goto_7

    .line 359
    :cond_d
    const/4 v13, 0x0

    :try_start_13
    invoke-virtual {v12}, Ljava/io/OutputStream;->close()V
    :try_end_13
    .catchall {:try_start_13 .. :try_end_13} :catchall_10

    .line 360
    move-object/from16 v13, v20

    const/4 v12, 0x1

    goto :goto_6

    .line 356
    :catchall_6
    move-exception v0

    move-object v13, v0

    :try_start_14
    throw v13
    :try_end_14
    .catchall {:try_start_14 .. :try_end_14} :catchall_7

    .line 359
    :catchall_7
    move-exception v0

    move-object v15, v0

    :try_start_15
    invoke-virtual {v12}, Ljava/io/OutputStream;->close()V
    :try_end_15
    .catchall {:try_start_15 .. :try_end_15} :catchall_8

    goto :goto_8

    :catchall_8
    move-exception v0

    move-object v12, v0

    :try_start_16
    invoke-virtual {v13, v12}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    :goto_8
    throw v15

    .line 361
    :cond_e
    const/4 v13, 0x0

    invoke-virtual {v0}, Ljava/util/zip/ZipEntry;->getName()Ljava/lang/String;

    move-result-object v0

    const-string v12, "payload_properties.txt"

    invoke-virtual {v0, v12}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0
    :try_end_16
    .catchall {:try_start_16 .. :try_end_16} :catchall_10

    if-eqz v0, :cond_1b

    .line 363
    :try_start_17
    new-instance v0, Ljava/io/BufferedReader;

    new-instance v12, Ljava/io/InputStreamReader;

    invoke-direct {v12, v11}, Ljava/io/InputStreamReader;-><init>(Ljava/io/InputStream;)V

    invoke-direct {v0, v12}, Ljava/io/BufferedReader;-><init>(Ljava/io/Reader;)V

    .line 365
    :goto_9
    invoke-virtual {v0}, Ljava/io/BufferedReader;->readLine()Ljava/lang/String;

    move-result-object v12

    if-eqz v12, :cond_13

    .line 366
    const-string v13, "FILE_HASH"

    invoke-virtual {v12, v13}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v13

    if-eqz v13, :cond_f

    .line 367
    move-object/from16 v17, v12

    const/4 v13, 0x0

    goto :goto_9

    .line 368
    :cond_f
    const-string v13, "FILE_SIZE"

    invoke-virtual {v12, v13}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v13

    if-eqz v13, :cond_10

    .line 369
    move-object/from16 v18, v12

    const/4 v13, 0x0

    goto :goto_9

    .line 370
    :cond_10
    const-string v13, "METADATA_HASH"

    invoke-virtual {v12, v13}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v13

    if-eqz v13, :cond_11

    .line 371
    move-object/from16 v20, v12

    const/4 v13, 0x0

    goto :goto_9

    .line 372
    :cond_11
    const-string v13, "METADATA_SIZE"

    invoke-virtual {v12, v13}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v13
    :try_end_17
    .catch Ljava/io/IOException; {:try_start_17 .. :try_end_17} :catch_0
    .catchall {:try_start_17 .. :try_end_17} :catchall_10

    if-eqz v13, :cond_12

    .line 373
    move-object/from16 v22, v12

    const/4 v13, 0x0

    goto :goto_9

    .line 372
    :cond_12
    const/4 v13, 0x0

    goto :goto_9

    .line 379
    :cond_13
    nop

    .line 380
    if-eqz v17, :cond_15

    if-eqz v18, :cond_15

    if-eqz v20, :cond_15

    if-nez v22, :cond_14

    goto :goto_a

    .line 390
    :cond_14
    move/from16 v12, v19

    move-object/from16 v13, v20

    const/4 v14, 0x1

    goto/16 :goto_6

    .line 384
    :cond_15
    :goto_a
    :try_start_18
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v12, "payload_properties invalid: missing:"

    invoke-virtual {v0, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;
    :try_end_18
    .catchall {:try_start_18 .. :try_end_18} :catchall_10

    const-string v12, ""

    if-nez v17, :cond_16

    move-object v13, v12

    goto :goto_b

    :cond_16
    :try_start_19
    const-string v13, " file-hash"

    :goto_b
    invoke-virtual {v0, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    if-nez v18, :cond_17

    move-object v13, v12

    goto :goto_c

    :cond_17
    const-string v13, " file-size"

    :goto_c
    invoke-virtual {v0, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    if-nez v20, :cond_18

    move-object v13, v12

    goto :goto_d

    :cond_18
    const-string v13, " metadata-hash"

    :goto_d
    invoke-virtual {v0, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    if-nez v22, :cond_19

    goto :goto_e

    :cond_19
    const-string v12, " metadata-size"

    :goto_e
    invoke-virtual {v0, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v2, v0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I
    :try_end_19
    .catchall {:try_start_19 .. :try_end_19} :catchall_10

    move/from16 v12, v19

    move-object/from16 v13, v20

    goto/16 :goto_6

    .line 376
    :catch_0
    move-exception v0

    move-object/from16 v13, v20

    move-object/from16 v12, v22

    .line 377
    :try_start_1a
    const-string v15, "ERROR PARSING THIS THING"

    invoke-static {v2, v15, v0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)I
    :try_end_1a
    .catchall {:try_start_1a .. :try_end_1a} :catchall_b

    .line 395
    :try_start_1b
    invoke-virtual {v11}, Ljava/util/zip/ZipInputStream;->close()V
    :try_end_1b
    .catchall {:try_start_1b .. :try_end_1b} :catchall_14

    :try_start_1c
    invoke-virtual {v7}, Ljava/io/BufferedInputStream;->close()V
    :try_end_1c
    .catchall {:try_start_1c .. :try_end_1c} :catchall_a

    :try_start_1d
    invoke-virtual {v9}, Lme/embodied/services/BoUpdater$MonitoredInputStream;->close()V
    :try_end_1d
    .catchall {:try_start_1d .. :try_end_1d} :catchall_9

    if-eqz v8, :cond_1a

    :try_start_1e
    invoke-virtual {v8}, Ljava/io/InputStream;->close()V
    :try_end_1e
    .catch Ljava/lang/Exception; {:try_start_1e .. :try_end_1e} :catch_1

    goto :goto_f

    .line 396
    :catch_1
    move-exception v0

    move-object/from16 v8, v17

    move-object/from16 v11, v18

    move/from16 v9, v19

    goto/16 :goto_20

    .line 378
    :cond_1a
    :goto_f
    return-void

    .line 346
    :catchall_9
    move-exception v0

    move-object v7, v0

    move-object/from16 v11, v18

    goto/16 :goto_1b

    :catchall_a
    move-exception v0

    move-object v7, v0

    move-object/from16 v11, v18

    goto/16 :goto_19

    :catchall_b
    move-exception v0

    move-object v15, v0

    goto/16 :goto_13

    .line 361
    :cond_1b
    move/from16 v12, v19

    move-object/from16 v13, v20

    goto/16 :goto_6

    .line 346
    :catchall_c
    move-exception v0

    goto :goto_11

    .line 394
    :cond_1c
    move/from16 v19, v12

    move-object/from16 v20, v13

    :try_start_1f
    const-string v0, "done processing zip file"

    invoke-static {v2, v0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I
    :try_end_1f
    .catchall {:try_start_1f .. :try_end_1f} :catchall_10

    .line 395
    :try_start_20
    invoke-virtual {v11}, Ljava/util/zip/ZipInputStream;->close()V
    :try_end_20
    .catchall {:try_start_20 .. :try_end_20} :catchall_f

    :try_start_21
    invoke-virtual {v7}, Ljava/io/BufferedInputStream;->close()V
    :try_end_21
    .catchall {:try_start_21 .. :try_end_21} :catchall_e

    :try_start_22
    invoke-virtual {v9}, Lme/embodied/services/BoUpdater$MonitoredInputStream;->close()V
    :try_end_22
    .catchall {:try_start_22 .. :try_end_22} :catchall_d

    if-eqz v8, :cond_1d

    :try_start_23
    invoke-virtual {v8}, Ljava/io/InputStream;->close()V
    :try_end_23
    .catch Ljava/lang/Exception; {:try_start_23 .. :try_end_23} :catch_2

    goto :goto_10

    .line 396
    :catch_2
    move-exception v0

    move-object/from16 v8, v17

    move-object/from16 v11, v18

    move/from16 v9, v19

    move-object/from16 v13, v20

    goto/16 :goto_1f

    .line 399
    :cond_1d
    :goto_10
    move-object/from16 v8, v17

    move-object/from16 v11, v18

    move/from16 v12, v19

    move-object/from16 v13, v20

    move-object/from16 v0, v22

    goto/16 :goto_21

    .line 346
    :catchall_d
    move-exception v0

    move-object v7, v0

    move-object/from16 v11, v18

    move/from16 v9, v19

    move-object/from16 v13, v20

    goto/16 :goto_1c

    :catchall_e
    move-exception v0

    move-object v7, v0

    move-object/from16 v11, v18

    move-object/from16 v13, v20

    goto :goto_18

    :catchall_f
    move-exception v0

    move-object v15, v0

    move-object/from16 v11, v18

    move-object/from16 v13, v20

    goto :goto_15

    :catchall_10
    move-exception v0

    move-object v15, v0

    move-object/from16 v13, v20

    goto :goto_12

    :catchall_11
    move-exception v0

    move/from16 v19, v12

    :goto_11
    move-object/from16 v20, v13

    move-object v15, v0

    :goto_12
    move-object/from16 v12, v22

    :goto_13
    :try_start_24
    throw v15
    :try_end_24
    .catchall {:try_start_24 .. :try_end_24} :catchall_12

    .line 395
    :catchall_12
    move-exception v0

    move-object/from16 v16, v0

    :try_start_25
    invoke-virtual {v11}, Ljava/util/zip/ZipInputStream;->close()V
    :try_end_25
    .catchall {:try_start_25 .. :try_end_25} :catchall_13

    goto :goto_14

    :catchall_13
    move-exception v0

    move-object v11, v0

    :try_start_26
    invoke-virtual {v15, v11}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    :goto_14
    throw v16
    :try_end_26
    .catchall {:try_start_26 .. :try_end_26} :catchall_14

    .line 346
    :catchall_14
    move-exception v0

    move-object v15, v0

    move-object/from16 v11, v18

    goto :goto_16

    :catchall_15
    move-exception v0

    const/4 v11, 0x0

    move-object v15, v0

    move/from16 v19, v11

    move-object/from16 v11, v18

    :goto_15
    move-object/from16 v12, v22

    :goto_16
    :try_start_27
    throw v15
    :try_end_27
    .catchall {:try_start_27 .. :try_end_27} :catchall_16

    .line 395
    :catchall_16
    move-exception v0

    move-object/from16 v16, v0

    :try_start_28
    invoke-virtual {v7}, Ljava/io/BufferedInputStream;->close()V
    :try_end_28
    .catchall {:try_start_28 .. :try_end_28} :catchall_17

    goto :goto_17

    :catchall_17
    move-exception v0

    move-object v7, v0

    :try_start_29
    invoke-virtual {v15, v7}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    :goto_17
    throw v16
    :try_end_29
    .catchall {:try_start_29 .. :try_end_29} :catchall_18

    .line 346
    :catchall_18
    move-exception v0

    move-object v7, v0

    goto :goto_19

    :catchall_19
    move-exception v0

    const/4 v11, 0x0

    move-object v7, v0

    move/from16 v19, v11

    move-object/from16 v11, v18

    :goto_18
    move-object/from16 v12, v22

    :goto_19
    :try_start_2a
    throw v7
    :try_end_2a
    .catchall {:try_start_2a .. :try_end_2a} :catchall_1a

    .line 395
    :catchall_1a
    move-exception v0

    move-object v15, v0

    :try_start_2b
    invoke-virtual {v9}, Lme/embodied/services/BoUpdater$MonitoredInputStream;->close()V
    :try_end_2b
    .catchall {:try_start_2b .. :try_end_2b} :catchall_1b

    goto :goto_1a

    :catchall_1b
    move-exception v0

    move-object v9, v0

    :try_start_2c
    invoke-virtual {v7, v9}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    :goto_1a
    throw v15
    :try_end_2c
    .catchall {:try_start_2c .. :try_end_2c} :catchall_1c

    .line 346
    :catchall_1c
    move-exception v0

    move-object v7, v0

    :goto_1b
    move/from16 v9, v19

    goto :goto_1d

    :catchall_1d
    move-exception v0

    const/4 v11, 0x0

    move-object v7, v0

    move v9, v11

    move-object/from16 v11, v18

    :goto_1c
    move-object/from16 v12, v22

    :goto_1d
    :try_start_2d
    throw v7
    :try_end_2d
    .catchall {:try_start_2d .. :try_end_2d} :catchall_1e

    .line 395
    :catchall_1e
    move-exception v0

    move-object v15, v0

    if-eqz v8, :cond_1e

    :try_start_2e
    invoke-virtual {v8}, Ljava/io/InputStream;->close()V
    :try_end_2e
    .catchall {:try_start_2e .. :try_end_2e} :catchall_1f

    goto :goto_1e

    :catchall_1f
    move-exception v0

    move-object v8, v0

    :try_start_2f
    invoke-virtual {v7, v8}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    :cond_1e
    :goto_1e
    throw v15
    :try_end_2f
    .catch Ljava/lang/Exception; {:try_start_2f .. :try_end_2f} :catch_3

    .line 396
    :catch_3
    move-exception v0

    move-object/from16 v8, v17

    goto :goto_20

    :catch_4
    move-exception v0

    const/4 v11, 0x0

    move v9, v11

    move-object/from16 v8, v17

    move-object/from16 v11, v18

    :goto_1f
    move-object/from16 v12, v22

    .line 398
    :goto_20
    const-string v7, "ALWAYS LOG YOUR CATCHES"

    invoke-static {v2, v7, v0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)I

    move-object v0, v12

    move v12, v9

    .line 400
    :goto_21
    if-nez v12, :cond_1f

    .line 401
    const-string v0, "missing update payload"

    invoke-static {v2, v0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 402
    return-void

    .line 404
    :cond_1f
    if-nez v14, :cond_20

    .line 405
    const-string v0, "missing update properties"

    invoke-static {v2, v0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 406
    return-void

    .line 410
    :cond_20
    new-instance v7, Ljava/io/FileWriter;

    invoke-direct {v7, v5}, Ljava/io/FileWriter;-><init>(Ljava/lang/String;)V

    .line 411
    :try_start_30
    new-instance v5, Ljava/io/PrintWriter;

    invoke-direct {v5, v7}, Ljava/io/PrintWriter;-><init>(Ljava/io/Writer;)V
    :try_end_30
    .catchall {:try_start_30 .. :try_end_30} :catchall_23

    .line 410
    nop

    .line 412
    :try_start_31
    new-instance v9, Ljava/lang/StringBuilder;

    invoke-direct {v9}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v9, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v9, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v9, v11}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v9, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v9, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v9, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v9, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v9, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v9}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v5, v9}, Ljava/io/PrintWriter;->print(Ljava/lang/String;)V
    :try_end_31
    .catchall {:try_start_31 .. :try_end_31} :catchall_20

    .line 416
    :try_start_32
    invoke-virtual {v5}, Ljava/io/PrintWriter;->close()V
    :try_end_32
    .catchall {:try_start_32 .. :try_end_32} :catchall_23

    invoke-virtual {v7}, Ljava/io/FileWriter;->close()V

    .line 418
    new-instance v5, Ljava/io/File;

    invoke-direct {v5, v3}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    .line 419
    invoke-virtual {v5, v6}, Ljava/io/File;->renameTo(Ljava/io/File;)Z

    move-object v12, v0

    .line 422
    :cond_21
    sput-object v4, Lme/embodied/services/BoUpdater;->lastUpdateVersion:Ljava/lang/String;

    .line 423
    iget v0, v1, Lme/embodied/services/BoUpdater;->os_revision_:I

    const/16 v3, 0x5078

    const-string v4, "prop3"

    const-string v5, "prop2"

    const-string v7, "prop1"

    const-string v9, "prop0"

    const-string v14, "file://"

    const-string v15, "url"

    if-lt v0, v3, :cond_22

    .line 425
    new-instance v0, Landroid/content/Intent;

    invoke-direct {v0}, Landroid/content/Intent;-><init>()V

    .line 426
    const-string v3, "com.embodied.osctrl.ota"

    invoke-virtual {v0, v3}, Landroid/content/Intent;->setAction(Ljava/lang/String;)Landroid/content/Intent;

    .line 427
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6}, Ljava/io/File;->getAbsolutePath()Ljava/lang/String;

    move-result-object v14

    invoke-virtual {v3, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v0, v15, v3}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Ljava/lang/String;)Landroid/content/Intent;

    .line 428
    invoke-virtual {v0, v9, v8}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Ljava/lang/String;)Landroid/content/Intent;

    .line 429
    invoke-virtual {v0, v7, v11}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Ljava/lang/String;)Landroid/content/Intent;

    .line 430
    invoke-virtual {v0, v5, v13}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Ljava/lang/String;)Landroid/content/Intent;

    .line 431
    invoke-virtual {v0, v4, v12}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Ljava/lang/String;)Landroid/content/Intent;

    .line 433
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "Requesting OTA by intent: "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-static {v0}, Lme/embodied/services/BoUpdater;->intentToString(Landroid/content/Intent;)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-static {v2, v3}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 434
    invoke-virtual {v1, v0}, Lme/embodied/services/BoUpdater;->sendBroadcast(Landroid/content/Intent;)V

    .line 435
    move-object/from16 v16, v10

    goto :goto_22

    .line 438
    :cond_22
    new-instance v0, Landroid/content/Intent;

    invoke-direct {v0}, Landroid/content/Intent;-><init>()V

    .line 439
    const-string v3, "itc.sample.test"

    invoke-virtual {v0, v3}, Landroid/content/Intent;->setAction(Ljava/lang/String;)Landroid/content/Intent;

    .line 440
    const-string v3, "test"

    move-object/from16 v16, v10

    const-string v10, "ota"

    invoke-virtual {v0, v3, v10}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Ljava/lang/String;)Landroid/content/Intent;

    .line 441
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6}, Ljava/io/File;->getAbsolutePath()Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v3, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v0, v15, v3}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Ljava/lang/String;)Landroid/content/Intent;

    .line 442
    invoke-virtual {v0, v9, v8}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Ljava/lang/String;)Landroid/content/Intent;

    .line 443
    invoke-virtual {v0, v7, v11}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Ljava/lang/String;)Landroid/content/Intent;

    .line 444
    invoke-virtual {v0, v5, v13}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Ljava/lang/String;)Landroid/content/Intent;

    .line 445
    invoke-virtual {v0, v4, v12}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Ljava/lang/String;)Landroid/content/Intent;

    .line 447
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "Requesting OTA by LEGACY intent:"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-static {v0}, Lme/embodied/services/BoUpdater;->intentToString(Landroid/content/Intent;)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-static {v2, v3}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 448
    invoke-virtual {v1, v0}, Lme/embodied/services/BoUpdater;->sendBroadcast(Landroid/content/Intent;)V

    .line 451
    :goto_22
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-static {}, Lme/embodied/services/BoUpdater;->nowAsTimestamp()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v2, ": sending file "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6}, Ljava/io/File;->getAbsolutePath()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v2, " to OTA handling intent: fileSize="

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v11}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-object/from16 v2, v16

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lme/embodied/services/BoUpdater;->writeOtaLog(Ljava/lang/String;)V

    .line 470
    return-void

    .line 410
    :catchall_20
    move-exception v0

    move-object v2, v0

    :try_start_33
    throw v2
    :try_end_33
    .catchall {:try_start_33 .. :try_end_33} :catchall_21

    .line 416
    :catchall_21
    move-exception v0

    move-object v3, v0

    :try_start_34
    invoke-virtual {v5}, Ljava/io/PrintWriter;->close()V
    :try_end_34
    .catchall {:try_start_34 .. :try_end_34} :catchall_22

    goto :goto_23

    :catchall_22
    move-exception v0

    move-object v4, v0

    :try_start_35
    invoke-virtual {v2, v4}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    :goto_23
    throw v3
    :try_end_35
    .catchall {:try_start_35 .. :try_end_35} :catchall_23

    .line 410
    :catchall_23
    move-exception v0

    move-object v2, v0

    :try_start_36
    throw v2
    :try_end_36
    .catchall {:try_start_36 .. :try_end_36} :catchall_24

    .line 416
    :catchall_24
    move-exception v0

    move-object v3, v0

    :try_start_37
    invoke-virtual {v7}, Ljava/io/FileWriter;->close()V
    :try_end_37
    .catchall {:try_start_37 .. :try_end_37} :catchall_25

    goto :goto_24

    :catchall_25
    move-exception v0

    move-object v4, v0

    invoke-virtual {v2, v4}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    :goto_24
    throw v3

    .line 243
    :cond_23
    :goto_25
    const-string v0, "malformed OTA Info file (/sdcard/EmbodiedData/otaInfo.txt) from logger/client-services"

    invoke-static {v2, v0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 244
    return-void

    .line 237
    :catchall_26
    move-exception v0

    move-object v4, v0

    :try_start_38
    throw v4
    :try_end_38
    .catchall {:try_start_38 .. :try_end_38} :catchall_27

    .line 241
    :catchall_27
    move-exception v0

    move-object v5, v0

    :try_start_39
    invoke-virtual {v2}, Ljava/io/BufferedReader;->close()V
    :try_end_39
    .catchall {:try_start_39 .. :try_end_39} :catchall_28

    goto :goto_26

    :catchall_28
    move-exception v0

    move-object v2, v0

    :try_start_3a
    invoke-virtual {v4, v2}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    :goto_26
    throw v5
    :try_end_3a
    .catchall {:try_start_3a .. :try_end_3a} :catchall_29

    .line 237
    :catchall_29
    move-exception v0

    move-object v2, v0

    :try_start_3b
    throw v2
    :try_end_3b
    .catchall {:try_start_3b .. :try_end_3b} :catchall_2a

    .line 241
    :catchall_2a
    move-exception v0

    move-object v4, v0

    :try_start_3c
    invoke-virtual {v3}, Ljava/io/FileReader;->close()V
    :try_end_3c
    .catchall {:try_start_3c .. :try_end_3c} :catchall_2b

    goto :goto_27

    :catchall_2b
    move-exception v0

    move-object v3, v0

    invoke-virtual {v2, v3}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    :goto_27
    throw v4
.end method

.method public CheckForUpdatesUntilStopped()V
    .locals 6

    .line 129
    monitor-enter p0

    .line 131
    nop

    .line 132
    :try_start_0
    const-string v0, "BoUpdater"

    const-string v1, "Waiting for system to stabilize."

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    const/4 v0, 0x0

    move v1, v0

    .line 133
    :goto_0
    iget-boolean v2, p0, Lme/embodied/services/BoUpdater;->checking_:Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz v2, :cond_1

    .line 135
    const-wide/16 v2, 0x1388

    :try_start_1
    invoke-virtual {p0, v2, v3}, Ljava/lang/Object;->wait(J)V
    :try_end_1
    .catch Ljava/lang/InterruptedException; {:try_start_1 .. :try_end_1} :catch_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 140
    nop

    .line 142
    add-int/lit8 v2, v1, 0x1

    :try_start_2
    rem-int/lit8 v1, v1, 0xc

    if-nez v1, :cond_0

    .line 143
    iget-object v1, p0, Lme/embodied/services/BoUpdater;->disk_cleaner_:Lme/embodied/DiskCleanup;

    const-wide/32 v3, 0x20000000

    invoke-virtual {v1, v3, v4, v0}, Lme/embodied/DiskCleanup;->ensureFreeSpace(JZ)Z
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    .line 145
    :cond_0
    :try_start_3
    const-string v1, "BoUpdater"

    const-string v3, "CheckForUpdates..."

    invoke-static {v1, v3}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 146
    invoke-virtual {p0}, Lme/embodied/services/BoUpdater;->CheckForUpdates()V
    :try_end_3
    .catch Ljava/io/IOException; {:try_start_3 .. :try_end_3} :catch_0
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    .line 149
    :goto_1
    goto :goto_2

    .line 147
    :catch_0
    move-exception v1

    .line 148
    :try_start_4
    const-string v3, "BoUpdater"

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "BO#3022 CheckForUpdates failed: "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v3, v1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_1

    .line 133
    :goto_2
    move v1, v2

    goto :goto_0

    .line 136
    :catch_1
    move-exception v0

    .line 138
    const-string v0, "BoUpdater"

    const-string v1, "Updater check interrupted."

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 139
    monitor-exit p0

    return-void

    .line 151
    :cond_1
    monitor-exit p0

    .line 152
    return-void

    .line 151
    :catchall_0
    move-exception v0

    monitor-exit p0
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_0

    throw v0
.end method

.method cancelPendingOTAs()V
    .locals 2

    .line 473
    const-string v0, "BoUpdater"

    const-string v1, "Canceling any pending OTA operations"

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 474
    new-instance v0, Landroid/content/Intent;

    invoke-direct {v0}, Landroid/content/Intent;-><init>()V

    .line 475
    const-string v1, "com.embodied.osctrl.ota.cancel"

    invoke-virtual {v0, v1}, Landroid/content/Intent;->setAction(Ljava/lang/String;)Landroid/content/Intent;

    .line 476
    invoke-virtual {p0, v0}, Lme/embodied/services/BoUpdater;->sendBroadcast(Landroid/content/Intent;)V

    .line 477
    return-void
.end method

.method declared-synchronized getSystemOtaVersion()Ljava/lang/String;
    .locals 3

    monitor-enter p0

    .line 196
    :try_start_0
    iget-object v0, p0, Lme/embodied/services/BoUpdater;->cachedOtaVersion:Ljava/lang/String;

    if-nez v0, :cond_0

    .line 197
    iget-object v0, p0, Lme/embodied/services/BoUpdater;->os_props_:Lme/embodied/AndroidSystemProps;

    const-string v1, "sys.embodied.otaver"

    const/4 v2, 0x0

    invoke-virtual {v0, v1, v2}, Lme/embodied/AndroidSystemProps;->getProperty(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lme/embodied/services/BoUpdater;->cachedOtaVersion:Ljava/lang/String;

    .line 198
    :cond_0
    iget-object v0, p0, Lme/embodied/services/BoUpdater;->cachedOtaVersion:Ljava/lang/String;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-object v0

    .line 195
    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method public onBind(Landroid/content/Intent;)Landroid/os/IBinder;
    .locals 1

    .line 78
    const-string p1, "BoUpdater"

    const-string v0, "onBind"

    invoke-static {p1, v0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 79
    const/4 p1, 0x0

    return-object p1
.end method

.method public onDestroy()V
    .locals 1

    .line 115
    monitor-enter p0

    .line 116
    const/4 v0, 0x0

    :try_start_0
    iput-boolean v0, p0, Lme/embodied/services/BoUpdater;->checking_:Z

    .line 117
    invoke-virtual {p0}, Ljava/lang/Object;->notify()V

    .line 118
    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 120
    :try_start_1
    iget-object v0, p0, Lme/embodied/services/BoUpdater;->thread_:Ljava/lang/Thread;

    invoke-virtual {v0}, Ljava/lang/Thread;->join()V
    :try_end_1
    .catch Ljava/lang/InterruptedException; {:try_start_1 .. :try_end_1} :catch_0

    .line 123
    goto :goto_0

    .line 121
    :catch_0
    move-exception v0

    .line 124
    :goto_0
    const/4 v0, 0x0

    iput-object v0, p0, Lme/embodied/services/BoUpdater;->thread_:Ljava/lang/Thread;

    .line 125
    invoke-super {p0}, Landroid/app/Service;->onDestroy()V

    .line 126
    return-void

    .line 118
    :catchall_0
    move-exception v0

    :try_start_2
    monitor-exit p0
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    throw v0
.end method

.method public onStartCommand(Landroid/content/Intent;II)I
    .locals 1

    .line 84
    const-string p1, "BoUpdater"

    const-string p2, "onStartCommand"

    invoke-static {p1, p2}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 85
    invoke-direct {p0}, Lme/embodied/services/BoUpdater;->ReportStartup()V

    .line 86
    new-instance p1, Lme/embodied/AndroidSystemProps;

    invoke-direct {p1, p0}, Lme/embodied/AndroidSystemProps;-><init>(Landroid/content/Context;)V

    iput-object p1, p0, Lme/embodied/services/BoUpdater;->os_props_:Lme/embodied/AndroidSystemProps;

    .line 87
    invoke-virtual {p1}, Lme/embodied/AndroidSystemProps;->getOSRevision()I

    move-result p1

    iput p1, p0, Lme/embodied/services/BoUpdater;->os_revision_:I

    .line 88
    new-instance p1, Ljava/io/File;

    const-string p2, "/sdcard/EmbodiedData/otaLog.txt"

    invoke-direct {p1, p2}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    const-wide/32 p2, 0xa00000

    const/4 v0, 0x5

    invoke-static {p1, p2, p3, v0}, Lme/embodied/DiskCleanup;->rotateFile(Ljava/io/File;JI)V

    .line 89
    new-instance p1, Lme/embodied/services/BoUpdater$1;

    invoke-direct {p1, p0}, Lme/embodied/services/BoUpdater$1;-><init>(Lme/embodied/services/BoUpdater;)V

    iput-object p1, p0, Lme/embodied/services/BoUpdater;->thread_:Ljava/lang/Thread;

    .line 95
    invoke-virtual {p1}, Ljava/lang/Thread;->start()V

    .line 96
    nop

    .line 98
    :try_start_0
    invoke-virtual {p0}, Lme/embodied/services/BoUpdater;->getContentResolver()Landroid/content/ContentResolver;

    move-result-object p1

    const-string p2, "boot_count"

    invoke-static {p1, p2}, Landroid/provider/Settings$Global;->getInt(Landroid/content/ContentResolver;Ljava/lang/String;)I

    move-result p1
    :try_end_0
    .catch Landroid/provider/Settings$SettingNotFoundException; {:try_start_0 .. :try_end_0} :catch_0

    .line 101
    goto :goto_0

    .line 100
    :catch_0
    move-exception p1

    const/4 p1, -0x1

    .line 102
    :goto_0
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-static {}, Lme/embodied/services/BoUpdater;->nowAsTimestamp()Ljava/lang/String;

    move-result-object p3

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p3, ": launcher starting: boot count="

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string p1, "\n"

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Lme/embodied/services/BoUpdater;->writeOtaLog(Ljava/lang/String;)V

    .line 103
    const/4 p1, 0x1

    return p1
.end method
