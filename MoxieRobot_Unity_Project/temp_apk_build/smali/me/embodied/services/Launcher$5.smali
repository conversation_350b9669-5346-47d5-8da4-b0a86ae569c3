.class Lme/embodied/services/Launcher$5;
.super Ljava/util/TimerTask;
.source "Launcher.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lme/embodied/services/Launcher;->startDelayedDisengage()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lme/embodied/services/Launcher;


# direct methods
.method constructor <init>(Lme/embodied/services/Launcher;)V
    .locals 0

    .line 1291
    iput-object p1, p0, Lme/embodied/services/Launcher$5;->this$0:Lme/embodied/services/Launcher;

    invoke-direct {p0}, Ljava/util/TimerTask;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 3

    .line 1294
    iget-object v0, p0, Lme/embodied/services/Launcher$5;->this$0:Lme/embodied/services/Launcher;

    const/4 v1, 0x0

    iput-object v1, v0, Lme/embodied/services/Launcher;->unpairing_delay_timer_:Ljava/util/Timer;

    .line 1295
    const-string v0, "bo-launcher-j"

    const-string v1, "BO#8010 Unpairing interaction did not complete in alotted time.  Forcing unpairing state."

    invoke-static {v0, v1}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 1296
    iget-object v1, p0, Lme/embodied/services/Launcher$5;->this$0:Lme/embodied/services/Launcher;

    iget-object v1, v1, Lme/embodied/services/Launcher;->config_state_override_:Lme/embodied/services/Launcher$LauncherState;

    sget-object v2, Lme/embodied/services/Launcher$LauncherState;->STATE_RECOVERY:Lme/embodied/services/Launcher$LauncherState;

    if-ne v1, v2, :cond_0

    .line 1299
    const-string v1, "Unsafe to transition to RECOVERY, returning to Wifi App to complete user data exchange."

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1300
    iget-object v0, p0, Lme/embodied/services/Launcher$5;->this$0:Lme/embodied/services/Launcher;

    sget-object v1, Lme/embodied/services/Launcher$LauncherState;->STATE_INIT:Lme/embodied/services/Launcher$LauncherState;

    iput-object v1, v0, Lme/embodied/services/Launcher;->config_state_override_:Lme/embodied/services/Launcher$LauncherState;

    .line 1302
    :cond_0
    iget-object v0, p0, Lme/embodied/services/Launcher$5;->this$0:Lme/embodied/services/Launcher;

    invoke-virtual {v0}, Lme/embodied/services/Launcher;->OnUnpairUserReady()V

    .line 1303
    return-void
.end method
