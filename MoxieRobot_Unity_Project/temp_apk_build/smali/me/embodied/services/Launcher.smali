.class public Lme/embodied/services/Launcher;
.super Ljava/lang/Object;
.source "Launcher.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lme/embodied/services/Launcher$ServiceMonitor;,
        Lme/embodied/services/Launcher$SuspendWatcher;,
        Lme/embodied/services/Launcher$LauncherState;,
        Lme/embodied/services/Launcher$LightSleepFilter;,
        Lme/embodied/services/Launcher$SuspendFilter;,
        Lme/embodied/services/Launcher$ComponentFilter;,
        Lme/embodied/services/Launcher$AnimType;,
        Lme/embodied/services/Launcher$FactoryTest;,
        Lme/embodied/services/Launcher$BOComponent;,
        Lme/embodied/services/Launcher$FeatureFlags;,
        Lme/embodied/services/Launcher$DisengageReason;,
        Lme/embodied/services/Launcher$UserDataRecoveryMode;
    }
.end annotation


# static fields
.field private static final BO_ERR_SERVICE_BASE:I = 0x1f56

.field private static final CONFIG_COMPONENT:Landroid/content/ComponentName;

.field private static final CONFIG_PACKAGE:Ljava/lang/String; = "com.embodied.bo_unity_wifi"

.field private static final CONFIG_TIMEOUT_INTERVAL_MS:I = 0x1388

.field private static final CONFIG_TIMESHIFT_MS:I = 0xea60

.field static final CPU_LIST_SCALES_FILE:Ljava/lang/String; = "/sys/devices/system/cpu/cpu0/cpufreq/scaling_available_frequencies"

.field static final CPU_SCALE_FILE:Ljava/lang/String; = "/sys/devices/system/cpu/cpu0/cpufreq/scaling_max_freq"

.field static final DEBUG_CONFIG_TYPE_ADB:I = 0x0

.field private static final DEFAULT_UNITY_ACTIVITY:Ljava/lang/String; = "com.unity3d.player.UnityPlayerActivity"

.field private static final KEY_MAX_FILE_SYNC_DELAY:Ljava/lang/String; = "file_sync_wait"

.field private static final LEGACY_S3_DATA:Ljava/io/File;

.field private static final MAX_NETWORK_WAIT:J = 0x4e20L

.field private static final MAX_SUSPEND_WAIT_OFF_CYCLES:I = 0x14

.field private static final MAX_SUSPEND_WAIT_OFF_INTERVAL:J = 0x64L

.field private static final MAX_SUSPEND_WAIT_ON_CYCLES:I = 0x2d

.field private static final MAX_SUSPEND_WAIT_ON_INTERVAL:J = 0x3e8L

.field private static final MONITOR_SLEEP_DURATION_MS:I = 0x3e8

.field private static final NETWORK_RECOVERY_TIMEOUT_S:I = 0x3c

.field private static final NETWORK_WAIT_INTERVAL:J = 0x1f4L

.field private static final OTA_ACTIVITY_TIMEOUT:J = 0x493e0L

.field private static final POWER_RESUME_DELAY_MS:J = 0x32L

.field private static final SERVICE_WAKE_SLEEP_MS:I = 0x7d0

.field private static final SUSPEND_ANALYTICS_STOP_DELAY:I = 0xfa

.field private static final SUSPEND_APP_STOP_DELAY:I = 0x7d0

.field private static final SUSPEND_COMMAND:B = 0x31t

.field private static final SUSPEND_COMMAND_SYS_FILE:Ljava/io/File;

.field private static final SUSPEND_IDLE_TIMEOUT:J = 0x4e20L

.field private static final SUSPEND_SERVICE_STOP_DELAY:I = 0x7d0

.field private static final TAG:Ljava/lang/String; = "bo-launcher-j"

.field private static final UNITY_COMPONENT:Landroid/content/ComponentName;

.field private static final UNITY_PACKAGE:Ljava/lang/String; = "com.embodied.bo_unity"

.field private static final UNPAIR_DELAY_SECONDS:I = 0x14

.field private static final USER_BRIGHTNESS_PATH:Ljava/lang/String; = "/sdcard/EmbodiedStaticData/PERSISTENT_DATA/brightness"

.field private static final USER_DATA_RECOVER_STATE:I = 0x5

.field private static final USER_DATA_UPDATED:I = 0x6

.field private static final USER_PAIRING_FILEPATH:Ljava/lang/String; = "/sdcard/EmbodiedStaticData/PERSISTENT_DATA/rightpoint/user_key.pub"

.field private static final WAKEWORD_KEY_M:I = 0xdb

.field private static final WAKEWORD_KEY_WAKEUP:I = 0x8f

.field private static instance_:Lme/embodied/services/Launcher;


# instance fields
.field private alarm_wakeup_pending_:Landroid/app/PendingIntent;

.field animation_:Lme/embodied/TransitionAnimation;

.field brain_update_ready_:Z

.field private components_:Ljava/util/Vector;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Vector<",
            "Landroid/content/ComponentName;",
            ">;"
        }
    .end annotation
.end field

.field config_state_agency_:Lme/embodied/services/Launcher$DisengageReason;

.field config_state_override_:Lme/embodied/services/Launcher$LauncherState;

.field private context_:Landroid/content/Context;

.field private developer_mode_:Z

.field factory_demo_mode_:Z

.field factory_demo_player_:Ljava/lang/Thread;

.field private flag_skip_unity_launch_:Z

.field private has_config_app_:Z

.field private last_app_start_:J

.field private last_config_checkin_:J

.field private last_ota_signal_:J

.field private last_wake_signal_:J

.field low_clock_rate_:J

.field max_clock_rate_:J

.field private monitor_:Lme/embodied/services/Launcher$ServiceMonitor;

.field private os_props_:Lme/embodied/AndroidSystemProps;

.field private os_revision_:I

.field pending_user_alarms_:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field

.field prev_state_:Lme/embodied/services/Launcher$LauncherState;

.field private self_managed_:Z

.field private service_:Lme/embodied/services/ServiceLauncher;

.field private service_metrics_:Lme/embodied/ServiceMetrics;

.field silent_ota_resume_pending_:Z

.field state_:Lme/embodied/services/Launcher$LauncherState;

.field private state_change_exec_:Ljava/util/concurrent/ExecutorService;

.field private suspend_watcher_:Lme/embodied/services/Launcher$SuspendWatcher;

.field telehealth_state_:Z

.field unpairing_delay_timer_:Ljava/util/Timer;

.field private user_data_dirty_:Z

.field private user_data_recover_active_:Z

.field private user_data_recovery_mode_:Lme/embodied/services/Launcher$UserDataRecoveryMode;

.field private wakeword_key_:I


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 79
    const-string v0, "watchdog"

    invoke-static {v0}, Ljava/lang/System;->loadLibrary(Ljava/lang/String;)V

    .line 84
    const/4 v0, 0x0

    sput-object v0, Lme/embodied/services/Launcher;->instance_:Lme/embodied/services/Launcher;

    .line 132
    new-instance v0, Ljava/io/File;

    const-string v1, "/sys/devices/platform/rockchip-key/power_key"

    invoke-direct {v0, v1}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    sput-object v0, Lme/embodied/services/Launcher;->SUSPEND_COMMAND_SYS_FILE:Ljava/io/File;

    .line 166
    new-instance v0, Ljava/io/File;

    const-string v1, "/sdcard/EmbodiedStaticData/PERSISTENT_DATA/S3"

    invoke-direct {v0, v1}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    sput-object v0, Lme/embodied/services/Launcher;->LEGACY_S3_DATA:Ljava/io/File;

    .line 245
    sget-object v0, Lme/embodied/services/Launcher$BOComponent;->BO_MAINAPP:Lme/embodied/services/Launcher$BOComponent;

    invoke-virtual {v0}, Lme/embodied/services/Launcher$BOComponent;->getComponentName()Landroid/content/ComponentName;

    move-result-object v0

    sput-object v0, Lme/embodied/services/Launcher;->UNITY_COMPONENT:Landroid/content/ComponentName;

    .line 246
    sget-object v0, Lme/embodied/services/Launcher$BOComponent;->BO_CFGAPP:Lme/embodied/services/Launcher$BOComponent;

    invoke-virtual {v0}, Lme/embodied/services/Launcher$BOComponent;->getComponentName()Landroid/content/ComponentName;

    move-result-object v0

    sput-object v0, Lme/embodied/services/Launcher;->CONFIG_COMPONENT:Landroid/content/ComponentName;

    return-void
.end method

.method public constructor <init>(Lme/embodied/services/ServiceLauncher;Landroid/app/PendingIntent;)V
    .locals 2

    .line 446
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 86
    sget-object v0, Lme/embodied/services/Launcher$LauncherState;->STATE_INIT:Lme/embodied/services/Launcher$LauncherState;

    iput-object v0, p0, Lme/embodied/services/Launcher;->state_:Lme/embodied/services/Launcher$LauncherState;

    .line 87
    sget-object v0, Lme/embodied/services/Launcher$LauncherState;->STATE_INIT:Lme/embodied/services/Launcher$LauncherState;

    iput-object v0, p0, Lme/embodied/services/Launcher;->prev_state_:Lme/embodied/services/Launcher$LauncherState;

    .line 93
    sget-object v0, Lme/embodied/services/Launcher$UserDataRecoveryMode;->RECOVER_CLOUD:Lme/embodied/services/Launcher$UserDataRecoveryMode;

    iput-object v0, p0, Lme/embodied/services/Launcher;->user_data_recovery_mode_:Lme/embodied/services/Launcher$UserDataRecoveryMode;

    .line 94
    const/4 v0, 0x0

    iput-boolean v0, p0, Lme/embodied/services/Launcher;->user_data_recover_active_:Z

    .line 95
    iput-boolean v0, p0, Lme/embodied/services/Launcher;->user_data_dirty_:Z

    .line 118
    iput-boolean v0, p0, Lme/embodied/services/Launcher;->self_managed_:Z

    .line 175
    new-instance v1, Ljava/util/Vector;

    invoke-direct {v1}, Ljava/util/Vector;-><init>()V

    iput-object v1, p0, Lme/embodied/services/Launcher;->components_:Ljava/util/Vector;

    .line 180
    iput-boolean v0, p0, Lme/embodied/services/Launcher;->flag_skip_unity_launch_:Z

    .line 186
    iput-boolean v0, p0, Lme/embodied/services/Launcher;->factory_demo_mode_:Z

    .line 187
    const/4 v1, 0x0

    iput-object v1, p0, Lme/embodied/services/Launcher;->factory_demo_player_:Ljava/lang/Thread;

    .line 189
    iput-boolean v0, p0, Lme/embodied/services/Launcher;->telehealth_state_:Z

    .line 190
    sget-object v1, Lme/embodied/services/Launcher$LauncherState;->STATE_INIT:Lme/embodied/services/Launcher$LauncherState;

    iput-object v1, p0, Lme/embodied/services/Launcher;->config_state_override_:Lme/embodied/services/Launcher$LauncherState;

    .line 191
    sget-object v1, Lme/embodied/services/Launcher$DisengageReason;->UNPAIRING:Lme/embodied/services/Launcher$DisengageReason;

    iput-object v1, p0, Lme/embodied/services/Launcher;->config_state_agency_:Lme/embodied/services/Launcher$DisengageReason;

    .line 192
    iput-boolean v0, p0, Lme/embodied/services/Launcher;->silent_ota_resume_pending_:Z

    .line 193
    iput-boolean v0, p0, Lme/embodied/services/Launcher;->brain_update_ready_:Z

    .line 194
    const/16 v0, 0x8f

    iput v0, p0, Lme/embodied/services/Launcher;->wakeword_key_:I

    .line 195
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lme/embodied/services/Launcher;->pending_user_alarms_:Ljava/util/ArrayList;

    .line 2100
    const-wide/16 v0, -0x1

    iput-wide v0, p0, Lme/embodied/services/Launcher;->low_clock_rate_:J

    .line 447
    iput-object p1, p0, Lme/embodied/services/Launcher;->service_:Lme/embodied/services/ServiceLauncher;

    .line 448
    invoke-virtual {p1}, Lme/embodied/services/ServiceLauncher;->getApplicationContext()Landroid/content/Context;

    move-result-object p1

    iput-object p1, p0, Lme/embodied/services/Launcher;->context_:Landroid/content/Context;

    .line 449
    iput-object p2, p0, Lme/embodied/services/Launcher;->alarm_wakeup_pending_:Landroid/app/PendingIntent;

    .line 450
    sget-object p1, Lme/embodied/RobotLauncher;->DEVELOPER_MODE_FILE:Ljava/io/File;

    invoke-virtual {p1}, Ljava/io/File;->exists()Z

    move-result p1

    iput-boolean p1, p0, Lme/embodied/services/Launcher;->developer_mode_:Z

    .line 452
    new-instance p1, Lme/embodied/AndroidSystemProps;

    iget-object p2, p0, Lme/embodied/services/Launcher;->context_:Landroid/content/Context;

    invoke-direct {p1, p2}, Lme/embodied/AndroidSystemProps;-><init>(Landroid/content/Context;)V

    iput-object p1, p0, Lme/embodied/services/Launcher;->os_props_:Lme/embodied/AndroidSystemProps;

    .line 453
    invoke-virtual {p1}, Lme/embodied/AndroidSystemProps;->getOSRevision()I

    move-result p1

    iput p1, p0, Lme/embodied/services/Launcher;->os_revision_:I

    .line 454
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string p2, "Got OS Revision: "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget p2, p0, Lme/embodied/services/Launcher;->os_revision_:I

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const-string p2, "bo-launcher-j"

    invoke-static {p2, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 456
    invoke-direct {p0}, Lme/embodied/services/Launcher;->InitNative()V

    .line 458
    new-instance p1, Lme/embodied/services/Launcher$SuspendWatcher;

    invoke-direct {p1, p0}, Lme/embodied/services/Launcher$SuspendWatcher;-><init>(Lme/embodied/services/Launcher;)V

    iput-object p1, p0, Lme/embodied/services/Launcher;->suspend_watcher_:Lme/embodied/services/Launcher$SuspendWatcher;

    .line 459
    const/4 p1, 0x1

    invoke-static {p1}, Ljava/util/concurrent/Executors;->newFixedThreadPool(I)Ljava/util/concurrent/ExecutorService;

    move-result-object p1

    iput-object p1, p0, Lme/embodied/services/Launcher;->state_change_exec_:Ljava/util/concurrent/ExecutorService;

    .line 460
    new-instance p1, Lme/embodied/ServiceMetrics;

    invoke-direct {p1}, Lme/embodied/ServiceMetrics;-><init>()V

    iput-object p1, p0, Lme/embodied/services/Launcher;->service_metrics_:Lme/embodied/ServiceMetrics;

    .line 461
    sget-object v0, Lme/embodied/services/Launcher$BOComponent;->BO_ANALYTICS:Lme/embodied/services/Launcher$BOComponent;

    invoke-virtual {v0}, Lme/embodied/services/Launcher$BOComponent;->getComponentName()Landroid/content/ComponentName;

    move-result-object v0

    invoke-virtual {p1, v0}, Lme/embodied/ServiceMetrics;->excludeComponent(Landroid/content/ComponentName;)Lme/embodied/ServiceMetrics;

    .line 462
    new-instance p1, Lme/embodied/services/Launcher$ServiceMonitor;

    iget-object v0, p0, Lme/embodied/services/Launcher;->context_:Landroid/content/Context;

    invoke-direct {p1, p0, v0}, Lme/embodied/services/Launcher$ServiceMonitor;-><init>(Lme/embodied/services/Launcher;Landroid/content/Context;)V

    iput-object p1, p0, Lme/embodied/services/Launcher;->monitor_:Lme/embodied/services/Launcher$ServiceMonitor;

    .line 463
    invoke-virtual {p1}, Lme/embodied/services/Launcher$ServiceMonitor;->start()V

    .line 465
    new-instance p1, Lme/embodied/TransitionAnimation;

    iget-object v0, p0, Lme/embodied/services/Launcher;->context_:Landroid/content/Context;

    invoke-direct {p1, v0}, Lme/embodied/TransitionAnimation;-><init>(Landroid/content/Context;)V

    iput-object p1, p0, Lme/embodied/services/Launcher;->animation_:Lme/embodied/TransitionAnimation;

    .line 466
    iget-object p1, p0, Lme/embodied/services/Launcher;->os_props_:Lme/embodied/AndroidSystemProps;

    invoke-virtual {p1}, Lme/embodied/AndroidSystemProps;->isSilentBoot()Z

    move-result p1

    iput-boolean p1, p0, Lme/embodied/services/Launcher;->silent_ota_resume_pending_:Z

    .line 467
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "CURRENT BOOT IS: "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-boolean v0, p0, Lme/embodied/services/Launcher;->silent_ota_resume_pending_:Z

    if-eqz v0, :cond_0

    const-string v0, "SILENT"

    goto :goto_0

    :cond_0
    const-string v0, "LOUD"

    :goto_0
    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {p2, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 468
    sput-object p0, Lme/embodied/services/Launcher;->instance_:Lme/embodied/services/Launcher;

    .line 469
    return-void
.end method

.method private static native BroadcastBrickedState()V
.end method

.method private ConfigStillAlive()Z
    .locals 11

    .line 1393
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    .line 1394
    iget-wide v2, p0, Lme/embodied/services/Launcher;->last_config_checkin_:J

    sub-long v2, v0, v2

    .line 1395
    const-wide/16 v4, 0x1388

    cmp-long v4, v2, v4

    const/4 v5, 0x1

    const-string v6, ", Age: "

    const-string v7, ", Now: "

    const-string v8, "bo-launcher-j"

    if-gez v4, :cond_0

    .line 1396
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v9, "Config App Alive. Last checkin: "

    invoke-virtual {v4, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-wide v9, p0, Lme/embodied/services/Launcher;->last_config_checkin_:J

    invoke-virtual {v4, v9, v10}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v0, v1}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v2, v3}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v8, v0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1397
    return v5

    .line 1398
    :cond_0
    const-wide/32 v9, 0xea60

    cmp-long v4, v2, v9

    if-lez v4, :cond_1

    .line 1400
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    iput-wide v0, p0, Lme/embodied/services/Launcher;->last_config_checkin_:J

    .line 1401
    const-string v0, "Time shift detected.  Resetting Config App timeout."

    invoke-static {v8, v0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1402
    return v5

    .line 1404
    :cond_1
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "Config App DEAD. Last checkin: "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-wide v9, p0, Lme/embodied/services/Launcher;->last_config_checkin_:J

    invoke-virtual {v4, v9, v10}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v0, v1}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v2, v3}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v8, v0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1406
    const/4 v0, 0x0

    return v0
.end method

.method private DoAudioDeviceDump()V
    .locals 7

    .line 1038
    iget-object v0, p0, Lme/embodied/services/Launcher;->context_:Landroid/content/Context;

    const-string v1, "audio"

    invoke-virtual {v0, v1}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/media/AudioManager;

    .line 1039
    const/4 v1, 0x3

    invoke-virtual {v0, v1}, Landroid/media/AudioManager;->getDevices(I)[Landroid/media/AudioDeviceInfo;

    move-result-object v0

    .line 1040
    array-length v1, v0

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_0

    aget-object v3, v0, v2

    .line 1041
    invoke-virtual {v3}, Landroid/media/AudioDeviceInfo;->getId()I

    move-result v4

    .line 1042
    invoke-virtual {v3}, Landroid/media/AudioDeviceInfo;->getProductName()Ljava/lang/CharSequence;

    move-result-object v3

    invoke-interface {v3}, Ljava/lang/CharSequence;->toString()Ljava/lang/String;

    move-result-object v3

    .line 1043
    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, "["

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v4, "]: "

    invoke-virtual {v5, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    const-string v4, "bo-launcher-j"

    invoke-static {v4, v3}, Landroid/util/Log;->v(Ljava/lang/String;Ljava/lang/String;)I

    .line 1040
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 1045
    :cond_0
    return-void
.end method

.method private DoShutdown()V
    .locals 3

    .line 1979
    const-string v0, "bo-launcher-j"

    :try_start_0
    const-string v1, "Entering shutdown state.  Shutting down in 10s."

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1980
    const-wide/16 v1, 0x2710

    invoke-static {v1, v2}, Ljava/lang/Thread;->sleep(J)V

    .line 1981
    const-string v1, "com.embodied.osctrl.shutdown"

    invoke-direct {p0, v1}, Lme/embodied/services/Launcher;->intentBroadcast(Ljava/lang/String;)V
    :try_end_0
    .catch Ljava/lang/InterruptedException; {:try_start_0 .. :try_end_0} :catch_0

    .line 1984
    goto :goto_0

    .line 1982
    :catch_0
    move-exception v1

    .line 1983
    const-string v1, "Failed to invoke shutdown.  Interrupted."

    invoke-static {v0, v1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 1985
    :goto_0
    return-void
.end method

.method private DoSilentReboot()V
    .locals 3

    .line 1962
    const-string v0, "bo-launcher-j"

    :try_start_0
    const-string v1, "Entering Silent Reboot state.  Rebooting in 10s."

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1963
    const-wide/16 v1, 0x2710

    invoke-static {v1, v2}, Ljava/lang/Thread;->sleep(J)V

    .line 1964
    const-string v1, "com.embodied.osctrl.reboot_quiet"

    invoke-direct {p0, v1}, Lme/embodied/services/Launcher;->intentBroadcast(Ljava/lang/String;)V
    :try_end_0
    .catch Ljava/lang/InterruptedException; {:try_start_0 .. :try_end_0} :catch_0

    .line 1967
    goto :goto_0

    .line 1965
    :catch_0
    move-exception v1

    .line 1966
    const-string v1, "Failed to invoke silent reboot.  Interrupted."

    invoke-static {v0, v1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 1968
    :goto_0
    return-void
.end method

.method private DoStateTransition(Lme/embodied/services/Launcher$LauncherState;Lme/embodied/services/Launcher$LauncherState;)V
    .locals 13
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/InterruptedException;
        }
    .end annotation

    .line 597
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Transitioning from "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v1, " to "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "bo-launcher-j"

    invoke-static {v1, v0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 599
    invoke-virtual {p1}, Lme/embodied/services/Launcher$LauncherState;->isSleepState()Z

    move-result v0

    const/4 v2, 0x0

    if-eqz v0, :cond_0

    invoke-virtual {p2}, Lme/embodied/services/Launcher$LauncherState;->isSleepState()Z

    move-result v0

    if-nez v0, :cond_0

    .line 601
    iget-object v0, p0, Lme/embodied/services/Launcher;->suspend_watcher_:Lme/embodied/services/Launcher$SuspendWatcher;

    invoke-virtual {v0}, Lme/embodied/services/Launcher$SuspendWatcher;->cancelWatch()V

    goto :goto_0

    .line 602
    :cond_0
    invoke-virtual {p1}, Lme/embodied/services/Launcher$LauncherState;->needSuspend()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 605
    iget-object v0, p0, Lme/embodied/services/Launcher;->suspend_watcher_:Lme/embodied/services/Launcher$SuspendWatcher;

    invoke-virtual {v0, v2}, Lme/embodied/services/Launcher$SuspendWatcher;->startSuspendWatch(Z)V

    .line 609
    :cond_1
    :goto_0
    sget-object v0, Lme/embodied/services/Launcher$7;->$SwitchMap$me$embodied$services$Launcher$AnimType:[I

    invoke-virtual {p2}, Lme/embodied/services/Launcher$LauncherState;->animationOnStart()Lme/embodied/services/Launcher$AnimType;

    move-result-object v3

    invoke-virtual {v3}, Lme/embodied/services/Launcher$AnimType;->ordinal()I

    move-result v3

    aget v0, v0, v3

    const/4 v3, 0x2

    const/4 v4, 0x1

    if-eq v0, v4, :cond_3

    if-eq v0, v3, :cond_2

    goto :goto_1

    .line 614
    :cond_2
    invoke-virtual {p0, v4}, Lme/embodied/services/Launcher;->setAnimationState(Z)V

    .line 615
    goto :goto_1

    .line 611
    :cond_3
    invoke-virtual {p0, v2}, Lme/embodied/services/Launcher;->setAnimationState(Z)V

    .line 612
    nop

    .line 621
    :goto_1
    invoke-virtual {p2}, Lme/embodied/services/Launcher$LauncherState;->screenActive()Z

    move-result v0

    if-eqz v0, :cond_4

    invoke-virtual {p1}, Lme/embodied/services/Launcher$LauncherState;->screenActive()Z

    move-result v0

    if-nez v0, :cond_4

    .line 622
    const/4 v0, 0x3

    invoke-static {v0}, Lme/embodied/services/Launcher;->NotifyResume(I)V

    .line 623
    const-wide/16 v5, 0x32

    invoke-static {v5, v6}, Ljava/lang/Thread;->sleep(J)V

    .line 627
    :cond_4
    invoke-virtual {p2}, Lme/embodied/services/Launcher$LauncherState;->screenActive()Z

    move-result v0

    invoke-direct {p0, v0}, Lme/embodied/services/Launcher;->setDisplayActive(Z)V

    .line 629
    nop

    .line 631
    invoke-static {}, Lme/embodied/services/Launcher$BOComponent;->values()[Lme/embodied/services/Launcher$BOComponent;

    move-result-object v0

    .line 632
    array-length v5, v0

    sub-int/2addr v5, v4

    const-wide/16 v6, 0x0

    move-wide v8, v6

    :goto_2
    if-ltz v5, :cond_6

    .line 633
    aget-object v10, v0, v5

    .line 635
    invoke-virtual {p2, v10}, Lme/embodied/services/Launcher$LauncherState;->componentActive(Lme/embodied/services/Launcher$BOComponent;)Z

    move-result v11

    if-nez v11, :cond_5

    invoke-virtual {v10}, Lme/embodied/services/Launcher$BOComponent;->getComponentName()Landroid/content/ComponentName;

    move-result-object v11

    invoke-direct {p0, v11}, Lme/embodied/services/Launcher;->StopComponent(Landroid/content/ComponentName;)Z

    move-result v11

    if-eqz v11, :cond_5

    .line 637
    invoke-virtual {v10}, Lme/embodied/services/Launcher$BOComponent;->delayAfterStop()J

    move-result-wide v11

    invoke-static {v8, v9, v11, v12}, Ljava/lang/Math;->max(JJ)J

    move-result-wide v8

    .line 638
    new-instance v11, Ljava/lang/StringBuilder;

    invoke-direct {v11}, Ljava/lang/StringBuilder;-><init>()V

    const-string v12, "Stopped active component: "

    invoke-virtual {v11, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v11}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v10

    invoke-static {v1, v10}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 632
    :cond_5
    add-int/lit8 v5, v5, -0x1

    goto :goto_2

    .line 642
    :cond_6
    cmp-long v0, v8, v6

    if-lez v0, :cond_7

    .line 643
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "Sleeping after stopping components for "

    invoke-virtual {v0, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v8, v9}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    const-string v5, " ms"

    invoke-virtual {v0, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 644
    invoke-static {v8, v9}, Ljava/lang/Thread;->sleep(J)V

    .line 648
    :cond_7
    sget-object v0, Lme/embodied/services/Launcher$FeatureFlags;->FEA_RESUME_WAIT_NET:Lme/embodied/services/Launcher$FeatureFlags;

    invoke-static {v0}, Lme/embodied/services/Launcher;->IsFeatureEnabled(Lme/embodied/services/Launcher$FeatureFlags;)Z

    move-result v0

    if-eqz v0, :cond_8

    .line 651
    invoke-virtual {p1}, Lme/embodied/services/Launcher$LauncherState;->needSuspend()Z

    move-result v0

    if-eqz v0, :cond_8

    sget-object v0, Lme/embodied/services/Launcher$LauncherState;->STATE_RUNNING:Lme/embodied/services/Launcher$LauncherState;

    if-ne p2, v0, :cond_8

    .line 652
    const-wide/16 v5, 0x4e20

    invoke-direct {p0, v5, v6}, Lme/embodied/services/Launcher;->waitOnNetwork(J)Z

    move-result v0

    if-nez v0, :cond_8

    .line 654
    const-string p1, "Could not detect internet after resume.  Returning to QR reading state."

    invoke-static {v1, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 655
    sget-object p1, Lme/embodied/services/Launcher$LauncherState;->STATE_CONFIG:Lme/embodied/services/Launcher$LauncherState;

    invoke-direct {p0, p1}, Lme/embodied/services/Launcher;->RequestState(Lme/embodied/services/Launcher$LauncherState;)V

    .line 656
    return-void

    .line 661
    :cond_8
    invoke-static {}, Lme/embodied/services/Launcher$BOComponent;->values()[Lme/embodied/services/Launcher$BOComponent;

    move-result-object v0

    array-length v5, v0

    :goto_3
    if-ge v2, v5, :cond_a

    aget-object v6, v0, v2

    .line 663
    invoke-virtual {p2, v6}, Lme/embodied/services/Launcher$LauncherState;->componentActive(Lme/embodied/services/Launcher$BOComponent;)Z

    move-result v7

    if-eqz v7, :cond_9

    invoke-virtual {v6}, Lme/embodied/services/Launcher$BOComponent;->getComponentName()Landroid/content/ComponentName;

    move-result-object v7

    invoke-direct {p0, v7}, Lme/embodied/services/Launcher;->StartComponent(Landroid/content/ComponentName;)Z

    move-result v7

    if-eqz v7, :cond_9

    .line 665
    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    const-string v8, "Started active component: "

    invoke-virtual {v7, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v6

    invoke-static {v1, v6}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 661
    :cond_9
    add-int/lit8 v2, v2, 0x1

    goto :goto_3

    .line 669
    :cond_a
    invoke-virtual {p2}, Lme/embodied/services/Launcher$LauncherState;->screenActive()Z

    move-result v0

    if-eqz v0, :cond_b

    invoke-virtual {p1}, Lme/embodied/services/Launcher$LauncherState;->screenActive()Z

    move-result p1

    if-nez p1, :cond_b

    .line 676
    invoke-static {v3}, Lme/embodied/services/Launcher;->NotifyResume(I)V

    .line 677
    const-string p1, "Notified resume to apps."

    invoke-static {v1, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 679
    invoke-virtual {p0}, Lme/embodied/services/Launcher;->notifyPendingAlarms()V

    .line 681
    invoke-direct {p0, v4, v4}, Lme/embodied/services/Launcher;->forceMaxCPU(ZZ)V

    .line 685
    :cond_b
    invoke-virtual {p2}, Lme/embodied/services/Launcher$LauncherState;->isSleepState()Z

    move-result p1

    if-eqz p1, :cond_c

    .line 686
    iget-object p1, p0, Lme/embodied/services/Launcher;->suspend_watcher_:Lme/embodied/services/Launcher$SuspendWatcher;

    invoke-virtual {p2}, Lme/embodied/services/Launcher$LauncherState;->needSuspend()Z

    move-result v0

    invoke-virtual {p1, v0}, Lme/embodied/services/Launcher$SuspendWatcher;->startSuspendWatch(Z)V

    .line 690
    :cond_c
    iget-object p1, p2, Lme/embodied/services/Launcher$LauncherState;->on_state_final_:Ljava/lang/Runnable;

    if-eqz p1, :cond_d

    .line 691
    iget-object p1, p2, Lme/embodied/services/Launcher$LauncherState;->on_state_final_:Ljava/lang/Runnable;

    invoke-interface {p1}, Ljava/lang/Runnable;->run()V

    .line 693
    :cond_d
    return-void
.end method

.method private static native FeatureEnabled(II)Z
.end method

.method private static native FlagBrainInit(I)V
.end method

.method private FlagBrainInitReason()V
    .locals 3

    .line 1900
    nop

    .line 1901
    iget-boolean v0, p0, Lme/embodied/services/Launcher;->silent_ota_resume_pending_:Z

    if-eqz v0, :cond_0

    .line 1902
    const/4 v0, 0x2

    goto :goto_0

    .line 1903
    :cond_0
    iget-boolean v0, p0, Lme/embodied/services/Launcher;->brain_update_ready_:Z

    if-eqz v0, :cond_1

    .line 1904
    const/4 v0, 0x3

    goto :goto_0

    .line 1903
    :cond_1
    const/4 v0, 0x1

    .line 1905
    :goto_0
    invoke-static {v0}, Lme/embodied/services/Launcher;->FlagBrainInit(I)V

    .line 1906
    const/4 v1, 0x0

    iput-boolean v1, p0, Lme/embodied/services/Launcher;->brain_update_ready_:Z

    .line 1907
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "FlagBrainInit("

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v0, ")"

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "bo-launcher-j"

    invoke-static {v1, v0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1908
    return-void
.end method

.method private static native FlagNetworkRecovery(I)V
.end method

.method private native InitNative()V
.end method

.method private static IsFeatureEnabled(Lme/embodied/services/Launcher$FeatureFlags;)Z
    .locals 1

    .line 1851
    invoke-virtual {p0}, Lme/embodied/services/Launcher$FeatureFlags;->ordinal()I

    move-result p0

    sget-object v0, Lme/embodied/services/Launcher;->instance_:Lme/embodied/services/Launcher;

    if-eqz v0, :cond_0

    iget v0, v0, Lme/embodied/services/Launcher;->os_revision_:I

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    invoke-static {p0, v0}, Lme/embodied/services/Launcher;->FeatureEnabled(II)Z

    move-result p0

    return p0
.end method

.method private KillBySystem(Landroid/content/ComponentName;)Z
    .locals 6

    .line 819
    iget-object v0, p0, Lme/embodied/services/Launcher;->context_:Landroid/content/Context;

    const-string v1, "activity"

    invoke-virtual {v0, v1}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/app/ActivityManager;

    .line 820
    invoke-virtual {v0}, Landroid/app/ActivityManager;->getAppTasks()Ljava/util/List;

    move-result-object v0

    .line 821
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    const/4 v2, 0x0

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroid/app/ActivityManager$AppTask;

    .line 822
    invoke-virtual {v1}, Landroid/app/ActivityManager$AppTask;->getTaskInfo()Landroid/app/ActivityManager$RecentTaskInfo;

    move-result-object v3

    iget-object v3, v3, Landroid/app/ActivityManager$RecentTaskInfo;->topActivity:Landroid/content/ComponentName;

    invoke-virtual {p1, v3}, Landroid/content/ComponentName;->equals(Ljava/lang/Object;)Z

    move-result v3

    const-string v4, "bo-launcher-j"

    const/4 v5, 0x1

    if-eqz v3, :cond_0

    .line 823
    new-array v0, v5, [Ljava/lang/Object;

    invoke-virtual {p1}, Landroid/content/ComponentName;->getClassName()Ljava/lang/String;

    move-result-object p1

    aput-object p1, v0, v2

    const-string p1, "Attempting to stop: %s"

    invoke-static {p1, v0}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    invoke-static {v4, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 824
    invoke-virtual {v1}, Landroid/app/ActivityManager$AppTask;->finishAndRemoveTask()V

    .line 825
    return v5

    .line 827
    :cond_0
    const/4 v3, 0x2

    new-array v3, v3, [Ljava/lang/Object;

    invoke-virtual {v1}, Landroid/app/ActivityManager$AppTask;->getTaskInfo()Landroid/app/ActivityManager$RecentTaskInfo;

    move-result-object v1

    iget-object v1, v1, Landroid/app/ActivityManager$RecentTaskInfo;->topActivity:Landroid/content/ComponentName;

    aput-object v1, v3, v2

    invoke-virtual {p1}, Landroid/content/ComponentName;->getClassName()Ljava/lang/String;

    move-result-object v1

    aput-object v1, v3, v5

    const-string v1, "Ignoring %s while trying to stop: %s"

    invoke-static {v1, v3}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    invoke-static {v4, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 829
    goto :goto_0

    .line 830
    :cond_1
    return v2
.end method

.method private static native KillByZMQ(Ljava/lang/String;)V
.end method

.method private LaunchComponent(Landroid/content/ComponentName;)V
    .locals 7

    .line 740
    const-string v0, "bo-launcher-j"

    new-instance v1, Landroid/content/Intent;

    invoke-direct {v1}, Landroid/content/Intent;-><init>()V

    .line 741
    invoke-virtual {v1, p1}, Landroid/content/Intent;->setComponent(Landroid/content/ComponentName;)Landroid/content/Intent;

    .line 745
    const/4 v2, 0x0

    const/4 v3, 0x1

    :try_start_0
    const-class v4, Landroid/app/Activity;

    invoke-virtual {p1}, Landroid/content/ComponentName;->getClassName()Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Ljava/lang/Class;->forName(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object v5

    invoke-virtual {v4, v5}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v4
    :try_end_0
    .catch Ljava/lang/ClassNotFoundException; {:try_start_0 .. :try_end_0} :catch_0

    .line 749
    nop

    .line 751
    const/4 v5, 0x2

    new-array v5, v5, [Ljava/lang/Object;

    if-eqz v4, :cond_0

    const-string v6, "activity"

    goto :goto_0

    :cond_0
    const-string v6, "service"

    :goto_0
    aput-object v6, v5, v2

    invoke-virtual {p1}, Landroid/content/ComponentName;->toString()Ljava/lang/String;

    move-result-object v2

    aput-object v2, v5, v3

    const-string v2, "Starting %s \'%s\'"

    invoke-static {v2, v5}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v2

    invoke-static {v0, v2}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 752
    if-eqz v4, :cond_3

    .line 753
    const-string v2, "android.intent.action.MAIN"

    invoke-virtual {v1, v2}, Landroid/content/Intent;->setAction(Ljava/lang/String;)Landroid/content/Intent;

    .line 754
    const v2, 0x10018000

    invoke-virtual {v1, v2}, Landroid/content/Intent;->setFlags(I)Landroid/content/Intent;

    .line 755
    sget-object v2, Lme/embodied/services/Launcher$BOComponent;->BO_CFGAPP:Lme/embodied/services/Launcher$BOComponent;

    invoke-virtual {v2}, Lme/embodied/services/Launcher$BOComponent;->getComponentName()Landroid/content/ComponentName;

    move-result-object v2

    if-ne p1, v2, :cond_1

    .line 756
    const-string p1, "Launching Config App"

    invoke-static {v0, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 757
    iget-object p1, p0, Lme/embodied/services/Launcher;->context_:Landroid/content/Context;

    invoke-virtual {p1, v1}, Landroid/content/Context;->startActivity(Landroid/content/Intent;)V

    .line 758
    invoke-direct {p0}, Lme/embodied/services/Launcher;->ResetConfigCheckin()V

    goto :goto_1

    .line 759
    :cond_1
    sget-object v2, Lme/embodied/services/Launcher$BOComponent;->BO_MAINAPP:Lme/embodied/services/Launcher$BOComponent;

    invoke-virtual {v2}, Lme/embodied/services/Launcher$BOComponent;->getComponentName()Landroid/content/ComponentName;

    move-result-object v2

    if-ne p1, v2, :cond_2

    .line 760
    const-string p1, "Launching Main App"

    invoke-static {v0, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 761
    iget-object p1, p0, Lme/embodied/services/Launcher;->context_:Landroid/content/Context;

    invoke-virtual {p1, v1}, Landroid/content/Context;->startActivity(Landroid/content/Intent;)V

    .line 762
    invoke-direct {p0}, Lme/embodied/services/Launcher;->ResetLaunchTime()V

    goto :goto_1

    .line 771
    :cond_2
    iget-object p1, p0, Lme/embodied/services/Launcher;->context_:Landroid/content/Context;

    invoke-virtual {p1, v1}, Landroid/content/Context;->startActivity(Landroid/content/Intent;)V

    goto :goto_1

    .line 774
    :cond_3
    iget-object p1, p0, Lme/embodied/services/Launcher;->context_:Landroid/content/Context;

    invoke-virtual {p1, v1}, Landroid/content/Context;->startService(Landroid/content/Intent;)Landroid/content/ComponentName;

    .line 776
    :goto_1
    return-void

    .line 746
    :catch_0
    move-exception v1

    .line 747
    new-array v1, v3, [Ljava/lang/Object;

    invoke-virtual {p1}, Landroid/content/ComponentName;->getClassName()Ljava/lang/String;

    move-result-object p1

    aput-object p1, v1, v2

    const-string p1, "Failed to find class for %s"

    invoke-static {p1, v1}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    invoke-static {v0, p1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 748
    return-void
.end method

.method private static native LogComponentState(Ljava/lang/String;I)V
.end method

.method private static native NotifyPowerState(II)V
.end method

.method private static native NotifyRecoveryPending(II)V
.end method

.method private static native NotifyResume(I)V
.end method

.method private static native PendingBrainUpdate()Z
.end method

.method private static native PendingFileSyncs()Z
.end method

.method private static PendingSilentRebootReady()Z
    .locals 1

    .line 1946
    sget-object v0, Lme/embodied/services/ServiceLauncher;->REBOOT_CLEAR_FILE:Ljava/io/File;

    invoke-virtual {v0}, Ljava/io/File;->exists()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 1948
    invoke-static {}, Lme/embodied/services/Launcher;->SilentRebootAllowed()Z

    move-result v0

    return v0

    .line 1950
    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method private static PerformShutdown()V
    .locals 2

    .line 1971
    sget-object v0, Lme/embodied/services/Launcher;->instance_:Lme/embodied/services/Launcher;

    if-eqz v0, :cond_0

    .line 1972
    invoke-direct {v0}, Lme/embodied/services/Launcher;->DoShutdown()V

    goto :goto_0

    .line 1974
    :cond_0
    const-string v0, "bo-launcher-j"

    const-string v1, "Failed to locate Launcher instance to perform shutdown."

    invoke-static {v0, v1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 1975
    :goto_0
    return-void
.end method

.method private static PerformSilentReboot()V
    .locals 2

    .line 1954
    sget-object v0, Lme/embodied/services/Launcher;->instance_:Lme/embodied/services/Launcher;

    if-eqz v0, :cond_0

    .line 1955
    invoke-direct {v0}, Lme/embodied/services/Launcher;->DoSilentReboot()V

    goto :goto_0

    .line 1957
    :cond_0
    const-string v0, "bo-launcher-j"

    const-string v1, "Failed to locate Launcher instance to perform silent reboot."

    invoke-static {v0, v1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 1958
    :goto_0
    return-void
.end method

.method private RecentlyLaunched()Z
    .locals 6

    .line 1420
    iget-wide v0, p0, Lme/embodied/services/Launcher;->last_app_start_:J

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v2

    const-wide/16 v4, 0x1388

    sub-long/2addr v2, v4

    cmp-long v0, v0, v2

    if-lez v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method private RecoverComponent(Landroid/content/ComponentName;)V
    .locals 1

    .line 734
    iget-object v0, p0, Lme/embodied/services/Launcher;->service_metrics_:Lme/embodied/ServiceMetrics;

    invoke-virtual {v0, p1}, Lme/embodied/ServiceMetrics;->onRecover(Landroid/content/ComponentName;)V

    .line 735
    invoke-direct {p0, p1}, Lme/embodied/services/Launcher;->LaunchComponent(Landroid/content/ComponentName;)V

    .line 737
    return-void
.end method

.method private static native RecoveryAgency()V
.end method

.method private static native RequestAssetLoad(Ljava/lang/String;)V
.end method

.method private static native RequestAssetRelease(Ljava/lang/String;)V
.end method

.method private static native RequestAssetReload(Ljava/lang/String;)V
.end method

.method private static native RequestAssetScan()V
.end method

.method private RequestConfigRetarget(Lme/embodied/services/Launcher$LauncherState;Lme/embodied/services/Launcher$DisengageReason;)V
    .locals 0

    .line 522
    iput-object p1, p0, Lme/embodied/services/Launcher;->config_state_override_:Lme/embodied/services/Launcher$LauncherState;

    .line 523
    iput-object p2, p0, Lme/embodied/services/Launcher;->config_state_agency_:Lme/embodied/services/Launcher$DisengageReason;

    .line 524
    invoke-virtual {p0}, Lme/embodied/services/Launcher;->OnConfigNeeded()V

    .line 525
    return-void
.end method

.method private RequestPrimaryRunState()V
    .locals 1

    .line 504
    iget-boolean v0, p0, Lme/embodied/services/Launcher;->telehealth_state_:Z

    if-eqz v0, :cond_0

    sget-object v0, Lme/embodied/services/Launcher$LauncherState;->STATE_TELEBRAIN:Lme/embodied/services/Launcher$LauncherState;

    goto :goto_0

    :cond_0
    sget-object v0, Lme/embodied/services/Launcher$LauncherState;->STATE_RUNNING:Lme/embodied/services/Launcher$LauncherState;

    :goto_0
    invoke-direct {p0, v0}, Lme/embodied/services/Launcher;->RequestState(Lme/embodied/services/Launcher$LauncherState;)V

    .line 505
    return-void
.end method

.method private static native RequestResetXMOS()V
.end method

.method private RequestState(Lme/embodied/services/Launcher$LauncherState;)V
    .locals 3

    .line 554
    iget-object v0, p0, Lme/embodied/services/Launcher;->state_:Lme/embodied/services/Launcher$LauncherState;

    if-eq p1, v0, :cond_1

    .line 555
    invoke-virtual {v0}, Lme/embodied/services/Launcher$LauncherState;->isFinalState()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 556
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Ignoring state request from FINAL state: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lme/embodied/services/Launcher;->state_:Lme/embodied/services/Launcher$LauncherState;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v1, " to "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const-string v0, "bo-launcher-j"

    invoke-static {v0, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 557
    return-void

    .line 559
    :cond_0
    iget-object v0, p0, Lme/embodied/services/Launcher;->state_:Lme/embodied/services/Launcher$LauncherState;

    .line 560
    iput-object p1, p0, Lme/embodied/services/Launcher;->state_:Lme/embodied/services/Launcher$LauncherState;

    .line 561
    iput-object v0, p0, Lme/embodied/services/Launcher;->prev_state_:Lme/embodied/services/Launcher$LauncherState;

    .line 562
    iget-object v1, p0, Lme/embodied/services/Launcher;->state_change_exec_:Ljava/util/concurrent/ExecutorService;

    new-instance v2, Lme/embodied/services/Launcher$2;

    invoke-direct {v2, p0, v0, p1}, Lme/embodied/services/Launcher$2;-><init>(Lme/embodied/services/Launcher;Lme/embodied/services/Launcher$LauncherState;Lme/embodied/services/Launcher$LauncherState;)V

    invoke-interface {v1, v2}, Ljava/util/concurrent/ExecutorService;->execute(Ljava/lang/Runnable;)V

    .line 582
    :cond_1
    return-void
.end method

.method private static native RequestUserDisengage(II)V
.end method

.method private static native RequestUserRecovery(I)V
.end method

.method private RequstConfigState()V
    .locals 2

    .line 511
    iget-object v0, p0, Lme/embodied/services/Launcher;->config_state_override_:Lme/embodied/services/Launcher$LauncherState;

    sget-object v1, Lme/embodied/services/Launcher$LauncherState;->STATE_INIT:Lme/embodied/services/Launcher$LauncherState;

    if-eq v0, v1, :cond_0

    .line 512
    iget-object v0, p0, Lme/embodied/services/Launcher;->config_state_override_:Lme/embodied/services/Launcher$LauncherState;

    invoke-direct {p0, v0}, Lme/embodied/services/Launcher;->RequestState(Lme/embodied/services/Launcher$LauncherState;)V

    goto :goto_0

    .line 514
    :cond_0
    sget-object v0, Lme/embodied/services/Launcher$LauncherState;->STATE_CONFIG:Lme/embodied/services/Launcher$LauncherState;

    invoke-direct {p0, v0}, Lme/embodied/services/Launcher;->RequestState(Lme/embodied/services/Launcher$LauncherState;)V

    .line 516
    :goto_0
    sget-object v0, Lme/embodied/services/Launcher$LauncherState;->STATE_INIT:Lme/embodied/services/Launcher$LauncherState;

    iput-object v0, p0, Lme/embodied/services/Launcher;->config_state_override_:Lme/embodied/services/Launcher$LauncherState;

    .line 517
    return-void
.end method

.method private ResetConfigCheckin()V
    .locals 4

    .line 1411
    const/4 v0, 0x1

    iput-boolean v0, p0, Lme/embodied/services/Launcher;->has_config_app_:Z

    .line 1413
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    const-wide/16 v2, 0x1388

    add-long/2addr v0, v2

    iput-wide v0, p0, Lme/embodied/services/Launcher;->last_config_checkin_:J

    .line 1414
    return-void
.end method

.method private ResetLaunchTime()V
    .locals 2

    .line 1427
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    iput-wide v0, p0, Lme/embodied/services/Launcher;->last_app_start_:J

    .line 1428
    return-void
.end method

.method private static native SendDemoSequence()V
.end method

.method private static ServiceErrorID(Landroid/content/ComponentName;)I
    .locals 5

    .line 696
    invoke-static {}, Lme/embodied/services/Launcher$BOComponent;->values()[Lme/embodied/services/Launcher$BOComponent;

    move-result-object v0

    array-length v1, v0

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_1

    aget-object v3, v0, v2

    .line 697
    invoke-virtual {v3}, Lme/embodied/services/Launcher$BOComponent;->getComponentName()Landroid/content/ComponentName;

    move-result-object v4

    invoke-virtual {v4, p0}, Landroid/content/ComponentName;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_0

    .line 698
    invoke-virtual {v3}, Lme/embodied/services/Launcher$BOComponent;->ordinal()I

    move-result p0

    add-int/lit16 p0, p0, 0x1f56

    return p0

    .line 696
    :cond_0
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 701
    :cond_1
    const/4 p0, -0x1

    return p0
.end method

.method private SetBacklight(I)V
    .locals 2

    .line 1926
    new-instance v0, Landroid/content/Intent;

    invoke-direct {v0}, Landroid/content/Intent;-><init>()V

    .line 1927
    const-string v1, "com.embodied.osctrl.backlight"

    invoke-virtual {v0, v1}, Landroid/content/Intent;->setAction(Ljava/lang/String;)Landroid/content/Intent;

    .line 1928
    invoke-static {p1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object p1

    const-string v1, "brightness"

    invoke-virtual {v0, v1, p1}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Ljava/lang/String;)Landroid/content/Intent;

    .line 1929
    iget-object p1, p0, Lme/embodied/services/Launcher;->context_:Landroid/content/Context;

    invoke-virtual {p1, v0}, Landroid/content/Context;->sendBroadcast(Landroid/content/Intent;)V

    .line 1930
    return-void
.end method

.method private SetSpeaker(Z)V
    .locals 2

    .line 1933
    new-instance v0, Landroid/content/Intent;

    invoke-direct {v0}, Landroid/content/Intent;-><init>()V

    .line 1934
    const-string v1, "com.embodied.osctrl.speaker"

    invoke-virtual {v0, v1}, Landroid/content/Intent;->setAction(Ljava/lang/String;)Landroid/content/Intent;

    .line 1935
    const-string v1, "enabled"

    invoke-virtual {v0, v1, p1}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Z)Landroid/content/Intent;

    .line 1936
    iget-object p1, p0, Lme/embodied/services/Launcher;->context_:Landroid/content/Context;

    invoke-virtual {p1, v0}, Landroid/content/Context;->sendBroadcast(Landroid/content/Intent;)V

    .line 1937
    return-void
.end method

.method private SetWakeWordKey(I)V
    .locals 3

    .line 1917
    new-instance v0, Landroid/content/Intent;

    invoke-direct {v0}, Landroid/content/Intent;-><init>()V

    .line 1918
    const-string v1, "com.embodied.osctrl.custom_key_id"

    invoke-virtual {v0, v1}, Landroid/content/Intent;->setAction(Ljava/lang/String;)Landroid/content/Intent;

    .line 1919
    invoke-static {p1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object v1

    const-string v2, "custom_key_id"

    invoke-virtual {v0, v2, v1}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Ljava/lang/String;)Landroid/content/Intent;

    .line 1920
    iget-object v1, p0, Lme/embodied/services/Launcher;->context_:Landroid/content/Context;

    invoke-virtual {v1, v0}, Landroid/content/Context;->sendBroadcast(Landroid/content/Intent;)V

    .line 1921
    iput p1, p0, Lme/embodied/services/Launcher;->wakeword_key_:I

    .line 1922
    return-void
.end method

.method private static native SignalUserAlarm(I)V
.end method

.method private static native SilentBootComplete()V
.end method

.method private static native SilentRebootAllowed()Z
.end method

.method private StartComponent(Landroid/content/ComponentName;)Z
    .locals 4

    .line 710
    nop

    .line 711
    iget-boolean v0, p0, Lme/embodied/services/Launcher;->flag_skip_unity_launch_:Z

    const/4 v1, 0x1

    const/4 v2, 0x0

    if-eqz v0, :cond_0

    sget-object v0, Lme/embodied/services/Launcher$BOComponent;->BO_MAINAPP:Lme/embodied/services/Launcher$BOComponent;

    invoke-virtual {v0}, Lme/embodied/services/Launcher$BOComponent;->getComponentName()Landroid/content/ComponentName;

    move-result-object v0

    if-ne p1, v0, :cond_0

    .line 713
    nop

    .line 714
    iput-boolean v2, p0, Lme/embodied/services/Launcher;->flag_skip_unity_launch_:Z

    .line 715
    const-string v0, "bo-launcher-j"

    const-string v3, "Preventing launch of Main App - Main App started externally."

    invoke-static {v0, v3}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    move v0, v2

    goto :goto_0

    .line 717
    :cond_0
    move v0, v1

    :goto_0
    nop

    .line 718
    iget-object v3, p0, Lme/embodied/services/Launcher;->components_:Ljava/util/Vector;

    invoke-virtual {v3, p1}, Ljava/util/Vector;->contains(Ljava/lang/Object;)Z

    move-result v3

    if-nez v3, :cond_1

    .line 719
    iget-object v2, p0, Lme/embodied/services/Launcher;->components_:Ljava/util/Vector;

    invoke-virtual {v2, p1}, Ljava/util/Vector;->add(Ljava/lang/Object;)Z

    .line 720
    nop

    .line 721
    iget-object v2, p0, Lme/embodied/services/Launcher;->service_metrics_:Lme/embodied/ServiceMetrics;

    invoke-static {p1}, Lme/embodied/services/Launcher;->ServiceErrorID(Landroid/content/ComponentName;)I

    move-result v3

    invoke-virtual {v2, p1, v3}, Lme/embodied/ServiceMetrics;->addMonitor(Landroid/content/ComponentName;I)V

    goto :goto_1

    .line 718
    :cond_1
    move v1, v2

    .line 723
    :goto_1
    if-eqz v1, :cond_3

    if-eqz v0, :cond_3

    .line 724
    invoke-virtual {p1}, Landroid/content/ComponentName;->getClassName()Ljava/lang/String;

    move-result-object v0

    const-class v2, Lme/embodied/services/BoBrain;

    invoke-virtual {v2}, Ljava/lang/Class;->getCanonicalName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_2

    .line 726
    invoke-direct {p0}, Lme/embodied/services/Launcher;->FlagBrainInitReason()V

    .line 728
    :cond_2
    invoke-direct {p0, p1}, Lme/embodied/services/Launcher;->LaunchComponent(Landroid/content/ComponentName;)V

    .line 730
    :cond_3
    return v1
.end method

.method private StartComponent(Ljava/lang/Class;)Z
    .locals 2

    .line 705
    new-instance v0, Landroid/content/ComponentName;

    invoke-virtual {p1}, Ljava/lang/Class;->getCanonicalName()Ljava/lang/String;

    move-result-object p1

    const-string v1, "com.embodied.bo_unity"

    invoke-direct {v0, v1, p1}, Landroid/content/ComponentName;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    invoke-direct {p0, v0}, Lme/embodied/services/Launcher;->StartComponent(Landroid/content/ComponentName;)Z

    move-result p1

    return p1
.end method

.method private static StartUserRecovery()V
    .locals 2

    .line 1885
    sget-object v0, Lme/embodied/services/Launcher;->instance_:Lme/embodied/services/Launcher;

    if-eqz v0, :cond_1

    .line 1886
    iget-boolean v1, v0, Lme/embodied/services/Launcher;->user_data_recover_active_:Z

    if-nez v1, :cond_0

    .line 1887
    const/4 v1, 0x1

    iput-boolean v1, v0, Lme/embodied/services/Launcher;->user_data_recover_active_:Z

    .line 1888
    iget-object v0, v0, Lme/embodied/services/Launcher;->user_data_recovery_mode_:Lme/embodied/services/Launcher$UserDataRecoveryMode;

    invoke-virtual {v0}, Lme/embodied/services/Launcher$UserDataRecoveryMode;->ordinal()I

    move-result v0

    invoke-static {v0}, Lme/embodied/services/Launcher;->RequestUserRecovery(I)V

    goto :goto_0

    .line 1890
    :cond_0
    const-string v0, "bo-launcher-j"

    const-string v1, "Entering a recovery state, but user data recovery has already started."

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1893
    :cond_1
    :goto_0
    return-void
.end method

.method private StopComponent(Landroid/content/ComponentName;)Z
    .locals 8

    .line 783
    const-string v0, "bo-launcher-j"

    iget-object v1, p0, Lme/embodied/services/Launcher;->components_:Ljava/util/Vector;

    invoke-virtual {v1, p1}, Ljava/util/Vector;->remove(Ljava/lang/Object;)Z

    move-result v1

    .line 784
    if-eqz v1, :cond_0

    .line 785
    iget-object v2, p0, Lme/embodied/services/Launcher;->service_metrics_:Lme/embodied/ServiceMetrics;

    invoke-virtual {v2, p1}, Lme/embodied/ServiceMetrics;->removeMonitor(Landroid/content/ComponentName;)V

    .line 788
    :cond_0
    new-instance v2, Landroid/content/Intent;

    invoke-direct {v2}, Landroid/content/Intent;-><init>()V

    .line 789
    invoke-virtual {v2, p1}, Landroid/content/Intent;->setComponent(Landroid/content/ComponentName;)Landroid/content/Intent;

    .line 793
    const/4 v3, 0x0

    const/4 v4, 0x1

    :try_start_0
    invoke-virtual {p1}, Landroid/content/ComponentName;->getClassName()Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Ljava/lang/Class;->forName(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object v5

    .line 794
    const-class v6, Landroid/app/Activity;

    invoke-virtual {v6, v5}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v5
    :try_end_0
    .catch Ljava/lang/ClassNotFoundException; {:try_start_0 .. :try_end_0} :catch_0

    .line 798
    nop

    .line 800
    const/4 v6, 0x2

    new-array v6, v6, [Ljava/lang/Object;

    if-eqz v5, :cond_1

    const-string v7, "activity"

    goto :goto_0

    :cond_1
    const-string v7, "service"

    :goto_0
    aput-object v7, v6, v3

    invoke-virtual {p1}, Landroid/content/ComponentName;->toString()Ljava/lang/String;

    move-result-object v3

    aput-object v3, v6, v4

    const-string v3, "Stopping %s \'%s\'"

    invoke-static {v3, v6}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v3

    invoke-static {v0, v3}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 801
    if-eqz v5, :cond_3

    .line 802
    sget-object v2, Lme/embodied/services/Launcher$BOComponent;->BO_MAINAPP:Lme/embodied/services/Launcher$BOComponent;

    invoke-virtual {v2}, Lme/embodied/services/Launcher$BOComponent;->getComponentName()Landroid/content/ComponentName;

    move-result-object v2

    if-ne p1, v2, :cond_2

    .line 805
    invoke-direct {p0, p1}, Lme/embodied/services/Launcher;->KillBySystem(Landroid/content/ComponentName;)Z

    .line 807
    invoke-virtual {p1}, Landroid/content/ComponentName;->getPackageName()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Lme/embodied/services/Launcher;->KillByZMQ(Ljava/lang/String;)V

    goto :goto_1

    .line 809
    :cond_2
    const-string v2, "Attempting to kill activity using ZMQ"

    invoke-static {v0, v2}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 810
    invoke-virtual {p1}, Landroid/content/ComponentName;->getPackageName()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Lme/embodied/services/Launcher;->KillByZMQ(Ljava/lang/String;)V

    goto :goto_1

    .line 813
    :cond_3
    iget-object p1, p0, Lme/embodied/services/Launcher;->context_:Landroid/content/Context;

    invoke-virtual {p1, v2}, Landroid/content/Context;->stopService(Landroid/content/Intent;)Z

    .line 815
    :goto_1
    return v1

    .line 795
    :catch_0
    move-exception v2

    .line 796
    new-array v2, v4, [Ljava/lang/Object;

    invoke-virtual {p1}, Landroid/content/ComponentName;->getClassName()Ljava/lang/String;

    move-result-object p1

    aput-object p1, v2, v3

    const-string p1, "Failed to find class for %s"

    invoke-static {p1, v2}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    invoke-static {v0, p1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 797
    return v1
.end method

.method private StopComponent(Ljava/lang/Class;)Z
    .locals 2

    .line 779
    new-instance v0, Landroid/content/ComponentName;

    invoke-virtual {p1}, Ljava/lang/Class;->getCanonicalName()Ljava/lang/String;

    move-result-object p1

    const-string v1, "com.embodied.bo_unity"

    invoke-direct {v0, v1, p1}, Landroid/content/ComponentName;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    invoke-direct {p0, v0}, Lme/embodied/services/Launcher;->StopComponent(Landroid/content/ComponentName;)Z

    move-result p1

    return p1
.end method

.method static synthetic access$000(Lme/embodied/services/Launcher$FeatureFlags;)Z
    .locals 0

    .line 76
    invoke-static {p0}, Lme/embodied/services/Launcher;->IsFeatureEnabled(Lme/embodied/services/Launcher$FeatureFlags;)Z

    move-result p0

    return p0
.end method

.method static synthetic access$100()V
    .locals 0

    .line 76
    invoke-static {}, Lme/embodied/services/Launcher;->StartUserRecovery()V

    return-void
.end method

.method static synthetic access$1000(Lme/embodied/services/Launcher;)V
    .locals 0

    .line 76
    invoke-direct {p0}, Lme/embodied/services/Launcher;->forceStandby()V

    return-void
.end method

.method static synthetic access$1100(Lme/embodied/services/Launcher;ZIJ)Z
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/InterruptedException;
        }
    .end annotation

    .line 76
    invoke-direct {p0, p1, p2, p3, p4}, Lme/embodied/services/Launcher;->waitDisplayState(ZIJ)Z

    move-result p0

    return p0
.end method

.method static synthetic access$1200(Lme/embodied/services/Launcher;)V
    .locals 0

    .line 76
    invoke-direct {p0}, Lme/embodied/services/Launcher;->forceResume()V

    return-void
.end method

.method static synthetic access$1300()Z
    .locals 1

    .line 76
    invoke-static {}, Lme/embodied/services/Launcher;->PendingFileSyncs()Z

    move-result v0

    return v0
.end method

.method static synthetic access$1400(Lme/embodied/services/Launcher;)Z
    .locals 0

    .line 76
    invoke-direct {p0}, Lme/embodied/services/Launcher;->ConfigStillAlive()Z

    move-result p0

    return p0
.end method

.method static synthetic access$1500(Lme/embodied/services/Launcher;)Z
    .locals 0

    .line 76
    invoke-direct {p0}, Lme/embodied/services/Launcher;->RecentlyLaunched()Z

    move-result p0

    return p0
.end method

.method static synthetic access$1600(Ljava/lang/String;I)V
    .locals 0

    .line 76
    invoke-static {p0, p1}, Lme/embodied/services/Launcher;->LogComponentState(Ljava/lang/String;I)V

    return-void
.end method

.method static synthetic access$1700()Landroid/content/ComponentName;
    .locals 1

    .line 76
    sget-object v0, Lme/embodied/services/Launcher;->CONFIG_COMPONENT:Landroid/content/ComponentName;

    return-object v0
.end method

.method static synthetic access$1800()Landroid/content/ComponentName;
    .locals 1

    .line 76
    sget-object v0, Lme/embodied/services/Launcher;->UNITY_COMPONENT:Landroid/content/ComponentName;

    return-object v0
.end method

.method static synthetic access$1900(Lme/embodied/services/Launcher;)Ljava/util/Vector;
    .locals 0

    .line 76
    iget-object p0, p0, Lme/embodied/services/Launcher;->components_:Ljava/util/Vector;

    return-object p0
.end method

.method static synthetic access$200()V
    .locals 0

    .line 76
    invoke-static {}, Lme/embodied/services/Launcher;->PerformSilentReboot()V

    return-void
.end method

.method static synthetic access$2000(Lme/embodied/services/Launcher;Landroid/content/ComponentName;)V
    .locals 0

    .line 76
    invoke-direct {p0, p1}, Lme/embodied/services/Launcher;->LaunchComponent(Landroid/content/ComponentName;)V

    return-void
.end method

.method static synthetic access$2100(Lme/embodied/services/Launcher;)Z
    .locals 0

    .line 76
    iget-boolean p0, p0, Lme/embodied/services/Launcher;->self_managed_:Z

    return p0
.end method

.method static synthetic access$2200(Lme/embodied/services/Launcher;)Z
    .locals 0

    .line 76
    iget-boolean p0, p0, Lme/embodied/services/Launcher;->has_config_app_:Z

    return p0
.end method

.method static synthetic access$2300(Lme/embodied/services/Launcher;)V
    .locals 0

    .line 76
    invoke-direct {p0}, Lme/embodied/services/Launcher;->RequstConfigState()V

    return-void
.end method

.method static synthetic access$2400(Lme/embodied/services/Launcher;)Z
    .locals 0

    .line 76
    iget-boolean p0, p0, Lme/embodied/services/Launcher;->developer_mode_:Z

    return p0
.end method

.method static synthetic access$2500(Lme/embodied/services/Launcher;Landroid/content/ComponentName;)V
    .locals 0

    .line 76
    invoke-direct {p0, p1}, Lme/embodied/services/Launcher;->RecoverComponent(Landroid/content/ComponentName;)V

    return-void
.end method

.method static synthetic access$2600(Lme/embodied/services/Launcher;)J
    .locals 2

    .line 76
    iget-wide v0, p0, Lme/embodied/services/Launcher;->last_wake_signal_:J

    return-wide v0
.end method

.method static synthetic access$2602(Lme/embodied/services/Launcher;J)J
    .locals 0

    .line 76
    iput-wide p1, p0, Lme/embodied/services/Launcher;->last_wake_signal_:J

    return-wide p1
.end method

.method static synthetic access$2700(Lme/embodied/services/Launcher;ZZ)V
    .locals 0

    .line 76
    invoke-direct {p0, p1, p2}, Lme/embodied/services/Launcher;->forceMaxCPU(ZZ)V

    return-void
.end method

.method static synthetic access$2800()Z
    .locals 1

    .line 76
    invoke-static {}, Lme/embodied/services/Launcher;->PendingSilentRebootReady()Z

    move-result v0

    return v0
.end method

.method static synthetic access$2900(II)V
    .locals 0

    .line 76
    invoke-static {p0, p1}, Lme/embodied/services/Launcher;->NotifyPowerState(II)V

    return-void
.end method

.method static synthetic access$300()V
    .locals 0

    .line 76
    invoke-static {}, Lme/embodied/services/Launcher;->PerformShutdown()V

    return-void
.end method

.method static synthetic access$3000(Lme/embodied/services/Launcher;Ljava/lang/String;)V
    .locals 0

    .line 76
    invoke-direct {p0, p1}, Lme/embodied/services/Launcher;->forceReboot(Ljava/lang/String;)V

    return-void
.end method

.method static synthetic access$3100(II)V
    .locals 0

    .line 76
    invoke-static {p0, p1}, Lme/embodied/services/Launcher;->NotifyRecoveryPending(II)V

    return-void
.end method

.method static synthetic access$400(Lme/embodied/services/Launcher;Lme/embodied/services/Launcher$LauncherState;Lme/embodied/services/Launcher$LauncherState;)V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/InterruptedException;
        }
    .end annotation

    .line 76
    invoke-direct {p0, p1, p2}, Lme/embodied/services/Launcher;->DoStateTransition(Lme/embodied/services/Launcher$LauncherState;Lme/embodied/services/Launcher$LauncherState;)V

    return-void
.end method

.method static synthetic access$500(Lme/embodied/services/Launcher;Lme/embodied/services/Launcher$LauncherState;)V
    .locals 0

    .line 76
    invoke-direct {p0, p1}, Lme/embodied/services/Launcher;->RequestState(Lme/embodied/services/Launcher$LauncherState;)V

    return-void
.end method

.method static synthetic access$600()V
    .locals 0

    .line 76
    invoke-static {}, Lme/embodied/services/Launcher;->SendDemoSequence()V

    return-void
.end method

.method static synthetic access$700()Ljava/io/File;
    .locals 1

    .line 76
    sget-object v0, Lme/embodied/services/Launcher;->LEGACY_S3_DATA:Ljava/io/File;

    return-object v0
.end method

.method static synthetic access$800(Lme/embodied/services/Launcher;)Lme/embodied/ServiceMetrics;
    .locals 0

    .line 76
    iget-object p0, p0, Lme/embodied/services/Launcher;->service_metrics_:Lme/embodied/ServiceMetrics;

    return-object p0
.end method

.method static synthetic access$900(Lme/embodied/services/Launcher;)Landroid/content/Context;
    .locals 0

    .line 76
    iget-object p0, p0, Lme/embodied/services/Launcher;->context_:Landroid/content/Context;

    return-object p0
.end method

.method private anyDisplayActive()Z
    .locals 7

    .line 1072
    iget-object v0, p0, Lme/embodied/services/Launcher;->context_:Landroid/content/Context;

    const-string v1, "display"

    invoke-virtual {v0, v1}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/hardware/display/DisplayManager;

    .line 1073
    invoke-virtual {v0}, Landroid/hardware/display/DisplayManager;->getDisplays()[Landroid/view/Display;

    move-result-object v0

    array-length v1, v0

    const/4 v2, 0x0

    move v3, v2

    :goto_0
    if-ge v3, v1, :cond_1

    aget-object v4, v0, v3

    .line 1074
    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, "Display State: "

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Landroid/view/Display;->getState()I

    move-result v6

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    const-string v6, "bo-launcher-j"

    invoke-static {v6, v5}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 1075
    invoke-virtual {v4}, Landroid/view/Display;->getState()I

    move-result v4

    const/4 v5, 0x2

    if-ne v4, v5, :cond_0

    .line 1076
    const/4 v0, 0x1

    return v0

    .line 1073
    :cond_0
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 1079
    :cond_1
    return v2
.end method

.method private cleanLegacyFilesRestart()V
    .locals 1

    .line 1113
    new-instance v0, Lme/embodied/services/Launcher$4;

    invoke-direct {v0, p0}, Lme/embodied/services/Launcher$4;-><init>(Lme/embodied/services/Launcher;)V

    .line 1128
    invoke-virtual {v0}, Lme/embodied/services/Launcher$4;->start()V

    .line 1129
    return-void
.end method

.method private clearSilentRebootIfSet(ZZ)V
    .locals 2

    .line 2009
    iget-boolean v0, p0, Lme/embodied/services/Launcher;->silent_ota_resume_pending_:Z

    const-string v1, "bo-launcher-j"

    if-eqz v0, :cond_2

    .line 2010
    const-string v0, "Silent Reboot IS set, requesting to unquiet our reboot"

    invoke-static {v1, v0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 2011
    const/4 v0, 0x0

    if-eqz p1, :cond_0

    .line 2012
    const-string p1, "Unquiet cannot proceed to normal suspend.  Welcome to quiet boot... jail."

    invoke-static {v1, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 2018
    const/16 p1, 0xdb

    invoke-direct {p0, p1}, Lme/embodied/services/Launcher;->SetWakeWordKey(I)V

    .line 2019
    invoke-direct {p0, v0}, Lme/embodied/services/Launcher;->SetSpeaker(Z)V

    .line 2020
    const-string p1, "com.embodied.bo_unity_wifi.silent_boot"

    invoke-static {p1}, Lme/embodied/services/Launcher;->KillByZMQ(Ljava/lang/String;)V

    .line 2021
    return-void

    .line 2024
    :cond_0
    const-string p1, "com.embodied.osctrl.quiet_boot_done"

    if-eqz p2, :cond_1

    .line 2025
    const-string p2, "com.embodied.osctrl.suspend"

    invoke-direct {p0, p2}, Lme/embodied/services/Launcher;->intentBroadcast(Ljava/lang/String;)V

    .line 2026
    invoke-direct {p0, p1}, Lme/embodied/services/Launcher;->intentBroadcast(Ljava/lang/String;)V

    .line 2027
    const-string p1, "com.embodied.osctrl.resume"

    invoke-direct {p0, p1}, Lme/embodied/services/Launcher;->intentBroadcast(Ljava/lang/String;)V

    .line 2028
    const/4 p1, 0x1

    invoke-direct {p0, p1}, Lme/embodied/services/Launcher;->SetSpeaker(Z)V

    goto :goto_0

    .line 2030
    :cond_1
    invoke-direct {p0, p1}, Lme/embodied/services/Launcher;->intentBroadcast(Ljava/lang/String;)V

    .line 2032
    :goto_0
    invoke-static {}, Lme/embodied/services/Launcher;->SilentBootComplete()V

    .line 2033
    iput-boolean v0, p0, Lme/embodied/services/Launcher;->silent_ota_resume_pending_:Z

    goto :goto_1

    .line 2035
    :cond_2
    const-string p1, "Silent Reboot is not set"

    invoke-static {v1, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 2037
    :goto_1
    return-void
.end method

.method private forceMaxCPU(ZZ)V
    .locals 6

    .line 2103
    const-string v0, "bo-launcher-j"

    :try_start_0
    const-string v1, "/sys/devices/system/cpu/cpu0/cpufreq/scaling_max_freq"

    invoke-static {v1}, Lme/embodied/services/Launcher;->readLineFromFile(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Ljava/lang/Long;->parseLong(Ljava/lang/String;)J

    move-result-wide v1
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 2104
    const-string v3, " to "

    if-eqz p1, :cond_2

    .line 2106
    :try_start_1
    invoke-direct {p0}, Lme/embodied/services/Launcher;->getMaxCPURate()J

    move-result-wide v4

    cmp-long p1, v1, v4

    if-eqz p1, :cond_1

    .line 2107
    if-eqz p2, :cond_0

    .line 2108
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string p2, "BO#8014 CPU scale incorrect on resume, Requesting to increase cpu scaling_max_freq from "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1, v1, v2}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {p1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-direct {p0}, Lme/embodied/services/Launcher;->getMaxCPURate()J

    move-result-wide v1

    invoke-virtual {p1, v1, v2}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v0, p1}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_0

    .line 2110
    :cond_0
    iput-wide v1, p0, Lme/embodied/services/Launcher;->low_clock_rate_:J

    .line 2111
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string p2, "Requesting to increase cpu scaling_max_freq from "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1, v1, v2}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {p1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-direct {p0}, Lme/embodied/services/Launcher;->getMaxCPURate()J

    move-result-wide v1

    invoke-virtual {p1, v1, v2}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v0, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 2113
    :goto_0
    invoke-direct {p0}, Lme/embodied/services/Launcher;->getMaxCPURate()J

    move-result-wide p1

    invoke-direct {p0, p1, p2}, Lme/embodied/services/Launcher;->requestCPURate(J)V

    goto :goto_1

    .line 2114
    :cond_1
    if-eqz p2, :cond_3

    .line 2115
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string p2, "CPU scale confirmed max rate on resume at "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1, v1, v2}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v0, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_1

    .line 2118
    :cond_2
    iget-wide p1, p0, Lme/embodied/services/Launcher;->low_clock_rate_:J

    const-wide/16 v4, 0x0

    cmp-long p1, p1, v4

    if-lez p1, :cond_3

    .line 2119
    iget-wide p1, p0, Lme/embodied/services/Launcher;->low_clock_rate_:J

    cmp-long p1, v1, p1

    if-eqz p1, :cond_3

    .line 2120
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string p2, "Requesting to restore cpu scaling_max_freq from "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1, v1, v2}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {p1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-wide v1, p0, Lme/embodied/services/Launcher;->low_clock_rate_:J

    invoke-virtual {p1, v1, v2}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v0, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 2121
    iget-wide p1, p0, Lme/embodied/services/Launcher;->low_clock_rate_:J

    invoke-direct {p0, p1, p2}, Lme/embodied/services/Launcher;->requestCPURate(J)V
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_0

    .line 2126
    :cond_3
    :goto_1
    goto :goto_2

    .line 2124
    :catch_0
    move-exception p1

    .line 2125
    const-string p2, "Unable to adjust CPU frequency."

    invoke-static {v0, p2, p1}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)I

    .line 2127
    :goto_2
    return-void
.end method

.method private forceReboot(Ljava/lang/String;)V
    .locals 3

    .line 1094
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Rebooting due to unrecoverable error: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const-string v0, "bo-launcher-j"

    invoke-static {v0, p1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 1097
    const-wide/16 v1, 0x3e8

    :try_start_0
    invoke-static {v1, v2}, Ljava/lang/Thread;->sleep(J)V

    .line 1098
    iget p1, p0, Lme/embodied/services/Launcher;->os_revision_:I

    const/16 v1, 0x5078

    if-lt p1, v1, :cond_0

    .line 1100
    new-instance p1, Landroid/content/Intent;

    invoke-direct {p1}, Landroid/content/Intent;-><init>()V

    .line 1101
    const-string v1, "com.embodied.osctrl.reboot"

    invoke-virtual {p1, v1}, Landroid/content/Intent;->setAction(Ljava/lang/String;)Landroid/content/Intent;

    .line 1102
    iget-object v1, p0, Lme/embodied/services/Launcher;->context_:Landroid/content/Context;

    invoke-virtual {v1, p1}, Landroid/content/Context;->sendBroadcast(Landroid/content/Intent;)V

    .line 1103
    goto :goto_0

    .line 1105
    :cond_0
    invoke-static {}, Ljava/lang/Runtime;->getRuntime()Ljava/lang/Runtime;

    move-result-object p1

    const-string v1, "reboot"

    invoke-virtual {p1, v1}, Ljava/lang/Runtime;->exec(Ljava/lang/String;)Ljava/lang/Process;
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 1109
    :goto_0
    goto :goto_1

    .line 1107
    :catch_0
    move-exception p1

    .line 1108
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "FAILED to force reboot: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v0, p1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 1110
    :goto_1
    return-void
.end method

.method private forceResume()V
    .locals 2

    .line 2049
    new-instance v0, Landroid/content/Intent;

    invoke-direct {v0}, Landroid/content/Intent;-><init>()V

    .line 2050
    const-string v1, "com.embodied.osctrl.resume"

    invoke-virtual {v0, v1}, Landroid/content/Intent;->setAction(Ljava/lang/String;)Landroid/content/Intent;

    .line 2051
    iget-object v1, p0, Lme/embodied/services/Launcher;->context_:Landroid/content/Context;

    invoke-virtual {v1, v0}, Landroid/content/Context;->sendBroadcast(Landroid/content/Intent;)V

    .line 2052
    return-void
.end method

.method private forceStandby()V
    .locals 4

    .line 1991
    iget v0, p0, Lme/embodied/services/Launcher;->os_revision_:I

    const-string v1, "bo-launcher-j"

    const/16 v2, 0x5078

    if-lt v0, v2, :cond_0

    .line 1993
    const-string v0, "com.embodied.osctrl.suspend"

    invoke-direct {p0, v0}, Lme/embodied/services/Launcher;->intentBroadcast(Ljava/lang/String;)V

    .line 1995
    const-string v0, "Checking Silent Reboot on first forced Standby"

    invoke-static {v1, v0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1997
    const/4 v0, 0x0

    invoke-direct {p0, v0, v0}, Lme/embodied/services/Launcher;->clearSilentRebootIfSet(ZZ)V

    goto :goto_1

    .line 2000
    :cond_0
    :try_start_0
    new-instance v0, Ljava/io/FileOutputStream;

    sget-object v2, Lme/embodied/services/Launcher;->SUSPEND_COMMAND_SYS_FILE:Ljava/io/File;

    invoke-direct {v0, v2}, Ljava/io/FileOutputStream;-><init>(Ljava/io/File;)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    .line 2001
    const/16 v2, 0x31

    :try_start_1
    invoke-virtual {v0, v2}, Ljava/io/FileOutputStream;->write(I)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 2002
    :try_start_2
    invoke-virtual {v0}, Ljava/io/FileOutputStream;->close()V
    :try_end_2
    .catch Ljava/io/IOException; {:try_start_2 .. :try_end_2} :catch_0

    .line 2004
    goto :goto_1

    .line 2000
    :catchall_0
    move-exception v2

    :try_start_3
    throw v2
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_1

    .line 2002
    :catchall_1
    move-exception v3

    :try_start_4
    invoke-virtual {v0}, Ljava/io/FileOutputStream;->close()V
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_2

    goto :goto_0

    :catchall_2
    move-exception v0

    :try_start_5
    invoke-virtual {v2, v0}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    :goto_0
    throw v3
    :try_end_5
    .catch Ljava/io/IOException; {:try_start_5 .. :try_end_5} :catch_0

    :catch_0
    move-exception v0

    .line 2003
    const-string v2, "Failed to advance to suspend. Unable to write signal."

    invoke-static {v1, v2, v0}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)I

    .line 2006
    :goto_1
    return-void
.end method

.method private getMaxCPURate()J
    .locals 4

    .line 2080
    iget-wide v0, p0, Lme/embodied/services/Launcher;->max_clock_rate_:J

    const-wide/16 v2, 0x0

    cmp-long v0, v0, v2

    if-gtz v0, :cond_0

    .line 2083
    :try_start_0
    const-string v0, "/sys/devices/system/cpu/cpu0/cpufreq/scaling_available_frequencies"

    invoke-static {v0}, Lme/embodied/services/Launcher;->readLineFromFile(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    const-string v1, " "

    invoke-virtual {v0, v1}, Ljava/lang/String;->split(Ljava/lang/String;)[Ljava/lang/String;

    move-result-object v0

    .line 2084
    array-length v1, v0

    add-int/lit8 v1, v1, -0x1

    aget-object v0, v0, v1

    invoke-static {v0}, Ljava/lang/Long;->parseLong(Ljava/lang/String;)J

    move-result-wide v0

    iput-wide v0, p0, Lme/embodied/services/Launcher;->max_clock_rate_:J
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    .line 2088
    goto :goto_0

    .line 2085
    :catch_0
    move-exception v0

    .line 2086
    const-string v0, "bo-launcher-j"

    const-string v1, "Failed to read available scales.  Assuming max for rk3288."

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 2087
    const-wide/32 v0, 0x188940

    iput-wide v0, p0, Lme/embodied/services/Launcher;->max_clock_rate_:J

    .line 2090
    :cond_0
    :goto_0
    iget-wide v0, p0, Lme/embodied/services/Launcher;->max_clock_rate_:J

    return-wide v0
.end method

.method private intentBroadcast(Ljava/lang/String;)V
    .locals 1

    .line 1940
    new-instance v0, Landroid/content/Intent;

    invoke-direct {v0}, Landroid/content/Intent;-><init>()V

    .line 1941
    invoke-virtual {v0, p1}, Landroid/content/Intent;->setAction(Ljava/lang/String;)Landroid/content/Intent;

    .line 1942
    iget-object p1, p0, Lme/embodied/services/Launcher;->context_:Landroid/content/Context;

    invoke-virtual {p1, v0}, Landroid/content/Context;->sendBroadcast(Landroid/content/Intent;)V

    .line 1943
    return-void
.end method

.method private static readLineFromFile(Ljava/lang/String;)Ljava/lang/String;
    .locals 5
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 2067
    new-instance v0, Ljava/io/FileReader;

    invoke-direct {v0, p0}, Ljava/io/FileReader;-><init>(Ljava/lang/String;)V

    :try_start_0
    new-instance v1, Ljava/io/BufferedReader;

    invoke-direct {v1, v0}, Ljava/io/BufferedReader;-><init>(Ljava/io/Reader;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_3

    .line 2068
    :try_start_1
    invoke-virtual {v1}, Ljava/io/BufferedReader;->readLine()Ljava/lang/String;

    move-result-object v2
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 2069
    if-eqz v2, :cond_0

    .line 2071
    nop

    .line 2072
    :try_start_2
    invoke-virtual {v1}, Ljava/io/BufferedReader;->close()V
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_3

    invoke-virtual {v0}, Ljava/io/FileReader;->close()V

    .line 2071
    return-object v2

    .line 2070
    :cond_0
    :try_start_3
    new-instance v2, Ljava/io/IOException;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "Couldn\'t read from "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-direct {v2, p0}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    throw v2
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    .line 2067
    :catchall_0
    move-exception p0

    :try_start_4
    throw p0
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_1

    .line 2072
    :catchall_1
    move-exception v2

    :try_start_5
    invoke-virtual {v1}, Ljava/io/BufferedReader;->close()V
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_2

    goto :goto_0

    :catchall_2
    move-exception v1

    :try_start_6
    invoke-virtual {p0, v1}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    :goto_0
    throw v2
    :try_end_6
    .catchall {:try_start_6 .. :try_end_6} :catchall_3

    .line 2067
    :catchall_3
    move-exception p0

    :try_start_7
    throw p0
    :try_end_7
    .catchall {:try_start_7 .. :try_end_7} :catchall_4

    .line 2072
    :catchall_4
    move-exception v1

    :try_start_8
    invoke-virtual {v0}, Ljava/io/FileReader;->close()V
    :try_end_8
    .catchall {:try_start_8 .. :try_end_8} :catchall_5

    goto :goto_1

    :catchall_5
    move-exception v0

    invoke-virtual {p0, v0}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    :goto_1
    throw v1
.end method

.method private static native reportOTAStatus(IFZI)V
.end method

.method private requestCPURate(J)V
    .locals 1

    .line 2058
    invoke-static {p1, p2}, Ljava/lang/Long;->toString(J)Ljava/lang/String;

    move-result-object p1

    .line 2059
    new-instance p2, Landroid/content/Intent;

    invoke-direct {p2}, Landroid/content/Intent;-><init>()V

    .line 2060
    const-string v0, "com.embodied.osctrl.cpu"

    invoke-virtual {p2, v0}, Landroid/content/Intent;->setAction(Ljava/lang/String;)Landroid/content/Intent;

    .line 2061
    const-string v0, "scaling_max_freq"

    invoke-virtual {p2, v0, p1}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Ljava/lang/String;)Landroid/content/Intent;

    .line 2062
    iget-object p1, p0, Lme/embodied/services/Launcher;->context_:Landroid/content/Context;

    invoke-virtual {p1, p2}, Landroid/content/Context;->sendBroadcast(Landroid/content/Intent;)V

    .line 2063
    return-void
.end method

.method private setDisplayActive(Z)V
    .locals 2

    .line 2041
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "State wants display "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    if-eqz p1, :cond_0

    const-string p1, "ON"

    goto :goto_0

    :cond_0
    const-string p1, "OFF"

    :goto_0
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const-string v0, "bo-launcher-j"

    invoke-static {v0, p1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 2042
    return-void
.end method

.method private startDemoMode()V
    .locals 1

    .line 891
    sget-object v0, Lme/embodied/services/Launcher$LauncherState;->STATE_DEMO:Lme/embodied/services/Launcher$LauncherState;

    invoke-direct {p0, v0}, Lme/embodied/services/Launcher;->RequestState(Lme/embodied/services/Launcher$LauncherState;)V

    .line 893
    new-instance v0, Lme/embodied/services/Launcher$3;

    invoke-direct {v0, p0}, Lme/embodied/services/Launcher$3;-><init>(Lme/embodied/services/Launcher;)V

    iput-object v0, p0, Lme/embodied/services/Launcher;->factory_demo_player_:Ljava/lang/Thread;

    .line 899
    return-void
.end method

.method private waitDisplayState(ZIJ)Z
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/InterruptedException;
        }
    .end annotation

    .line 1084
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Waiting for display to go: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "bo-launcher-j"

    invoke-static {v1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 1085
    invoke-direct {p0}, Lme/embodied/services/Launcher;->anyDisplayActive()Z

    move-result v0

    .line 1086
    :goto_0
    if-eq v0, p1, :cond_0

    add-int/lit8 v1, p2, -0x1

    if-lez p2, :cond_0

    .line 1087
    invoke-static {p3, p4}, Ljava/lang/Thread;->sleep(J)V

    .line 1088
    invoke-direct {p0}, Lme/embodied/services/Launcher;->anyDisplayActive()Z

    move-result v0

    move p2, v1

    goto :goto_0

    .line 1090
    :cond_0
    if-ne v0, p1, :cond_1

    const/4 p1, 0x1

    goto :goto_1

    :cond_1
    const/4 p1, 0x0

    :goto_1
    return p1
.end method

.method private waitOnNetwork(J)Z
    .locals 7
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/InterruptedException;
        }
    .end annotation

    .line 1048
    nop

    .line 1049
    const-string v0, "bo-launcher-j"

    const-string v1, "Enabling wifi"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 1050
    iget-object v1, p0, Lme/embodied/services/Launcher;->context_:Landroid/content/Context;

    const/4 v2, 0x1

    invoke-static {v1, v2}, Lme/embodied/NetworkUtil;->enableWifi(Landroid/content/Context;Z)V

    const-wide/16 v3, 0x0

    .line 1052
    :goto_0
    cmp-long v1, v3, p1

    if-gez v1, :cond_2

    .line 1053
    iget-object v1, p0, Lme/embodied/services/Launcher;->context_:Landroid/content/Context;

    invoke-static {v1}, Lme/embodied/NetworkUtil;->isNetworkConnected(Landroid/content/Context;)Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-static {}, Lme/embodied/NetworkUtil;->isInternetAvailable()Z

    move-result v1

    if-eqz v1, :cond_1

    .line 1054
    iget-object v1, p0, Lme/embodied/services/Launcher;->context_:Landroid/content/Context;

    invoke-static {v1}, Lme/embodied/NetworkUtil;->getWifiActiveSSID(Landroid/content/Context;)Ljava/lang/String;

    move-result-object v1

    .line 1055
    if-nez v1, :cond_0

    .line 1056
    const-string v1, "Network seems OK after waiting, but no WiFi SSID"

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_1

    .line 1059
    :cond_0
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string p2, "Network OK after waiting.  Waited "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1, v3, v4}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v0, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1060
    return v2

    .line 1063
    :cond_1
    :goto_1
    const-wide/16 v5, 0x1f4

    invoke-static {v5, v6}, Ljava/lang/Thread;->sleep(J)V

    .line 1064
    add-long/2addr v3, v5

    goto :goto_0

    .line 1066
    :cond_2
    const-string p1, "Network gave up"

    invoke-static {v0, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1067
    const/4 p1, 0x0

    return p1
.end method


# virtual methods
.method public LaunchServices()V
    .locals 2

    .line 850
    const-string v0, "bo-launcher-j"

    const-string v1, "LaunchServices"

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 851
    const/4 v0, 0x1

    iput-boolean v0, p0, Lme/embodied/services/Launcher;->self_managed_:Z

    .line 852
    iput-boolean v0, p0, Lme/embodied/services/Launcher;->flag_skip_unity_launch_:Z

    .line 854
    iget-boolean v0, p0, Lme/embodied/services/Launcher;->factory_demo_mode_:Z

    if-eqz v0, :cond_0

    .line 855
    const/4 v0, 0x0

    iput-boolean v0, p0, Lme/embodied/services/Launcher;->factory_demo_mode_:Z

    .line 856
    invoke-direct {p0}, Lme/embodied/services/Launcher;->startDemoMode()V

    .line 857
    return-void

    .line 860
    :cond_0
    invoke-direct {p0}, Lme/embodied/services/Launcher;->RequestPrimaryRunState()V

    .line 861
    return-void
.end method

.method public OnAlarmPing()V
    .locals 2

    .line 1748
    const-string v0, "bo-launcher-j"

    const-string v1, "GOT ALARM CLOCK PING!"

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1749
    iget-object v0, p0, Lme/embodied/services/Launcher;->state_:Lme/embodied/services/Launcher$LauncherState;

    sget-object v1, Lme/embodied/services/Launcher$LauncherState;->STATE_SUSPEND:Lme/embodied/services/Launcher$LauncherState;

    if-ne v0, v1, :cond_0

    .line 1750
    invoke-virtual {p0}, Lme/embodied/services/Launcher;->OnStayAwake()V

    .line 1751
    sget-object v0, Lme/embodied/services/Launcher$LauncherState;->STATE_LIGHT_SLEEP:Lme/embodied/services/Launcher$LauncherState;

    invoke-direct {p0, v0}, Lme/embodied/services/Launcher;->RequestState(Lme/embodied/services/Launcher$LauncherState;)V

    .line 1753
    :cond_0
    return-void
.end method

.method public OnAppWaitReady()V
    .locals 3

    .line 1663
    iget-object v0, p0, Lme/embodied/services/Launcher;->state_:Lme/embodied/services/Launcher$LauncherState;

    sget-object v1, Lme/embodied/services/Launcher$LauncherState;->STATE_CONFIG:Lme/embodied/services/Launcher$LauncherState;

    const/4 v2, 0x0

    if-ne v0, v1, :cond_0

    .line 1664
    const-string v0, "bo-launcher-j"

    const-string v1, "Checking Silent Reboot at CONFIG state"

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1665
    const/4 v0, 0x1

    invoke-direct {p0, v0, v2}, Lme/embodied/services/Launcher;->clearSilentRebootIfSet(ZZ)V

    .line 1668
    :cond_0
    invoke-virtual {p0, v2}, Lme/embodied/services/Launcher;->setAnimationState(Z)V

    .line 1670
    iget-object v0, p0, Lme/embodied/services/Launcher;->factory_demo_player_:Ljava/lang/Thread;

    if-eqz v0, :cond_1

    .line 1672
    invoke-virtual {v0}, Ljava/lang/Thread;->start()V

    .line 1673
    const/4 v0, 0x0

    iput-object v0, p0, Lme/embodied/services/Launcher;->factory_demo_player_:Ljava/lang/Thread;

    .line 1675
    :cond_1
    return-void
.end method

.method public OnBatteryCritical()V
    .locals 2

    .line 1722
    const-string v0, "bo-launcher-j"

    const-string v1, "BO#8015 Battery level is critical.  Transitioning to emergency shutdown."

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1723
    sget-object v0, Lme/embodied/services/Launcher$LauncherState;->STATE_SHUTDOWN:Lme/embodied/services/Launcher$LauncherState;

    invoke-direct {p0, v0}, Lme/embodied/services/Launcher;->RequestState(Lme/embodied/services/Launcher$LauncherState;)V

    .line 1724
    return-void
.end method

.method public OnBrainRecoveryNeeded(Z)V
    .locals 2

    .line 1449
    iget-object v0, p0, Lme/embodied/services/Launcher;->state_:Lme/embodied/services/Launcher$LauncherState;

    sget-object v1, Lme/embodied/services/Launcher$LauncherState;->STATE_RUNNING:Lme/embodied/services/Launcher$LauncherState;

    if-ne v0, v1, :cond_1

    .line 1451
    if-eqz p1, :cond_0

    sget-object p1, Lme/embodied/services/Launcher$UserDataRecoveryMode;->RECOVER_LOCAL:Lme/embodied/services/Launcher$UserDataRecoveryMode;

    goto :goto_0

    :cond_0
    sget-object p1, Lme/embodied/services/Launcher$UserDataRecoveryMode;->RECOVER_CLOUD:Lme/embodied/services/Launcher$UserDataRecoveryMode;

    :goto_0
    iput-object p1, p0, Lme/embodied/services/Launcher;->user_data_recovery_mode_:Lme/embodied/services/Launcher$UserDataRecoveryMode;

    .line 1452
    sget-object p1, Lme/embodied/services/Launcher$LauncherState;->STATE_RECOVERY:Lme/embodied/services/Launcher$LauncherState;

    invoke-direct {p0, p1}, Lme/embodied/services/Launcher;->RequestState(Lme/embodied/services/Launcher$LauncherState;)V

    .line 1454
    :cond_1
    return-void
.end method

.method public OnConfigCheckin()V
    .locals 3

    .line 1370
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    iput-wide v0, p0, Lme/embodied/services/Launcher;->last_config_checkin_:J

    .line 1371
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Config App has Checked in: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-wide v1, p0, Lme/embodied/services/Launcher;->last_config_checkin_:J

    invoke-virtual {v0, v1, v2}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "bo-launcher-j"

    invoke-static {v1, v0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1372
    return-void
.end method

.method public OnConfigComplete()V
    .locals 2

    .line 1325
    iget v0, p0, Lme/embodied/services/Launcher;->wakeword_key_:I

    const/16 v1, 0x8f

    if-eq v0, v1, :cond_0

    .line 1327
    invoke-direct {p0, v1}, Lme/embodied/services/Launcher;->SetWakeWordKey(I)V

    .line 1330
    :cond_0
    invoke-static {}, Lme/embodied/services/Launcher;->PendingFileSyncs()Z

    move-result v0

    if-nez v0, :cond_1

    .line 1332
    const-string v0, "bo-launcher-j"

    const-string v1, "Configuration completed and ready. Starting Bo-Unity."

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1333
    sget-object v0, Lme/embodied/services/Launcher$LauncherState;->STATE_STARTUP:Lme/embodied/services/Launcher$LauncherState;

    invoke-direct {p0, v0}, Lme/embodied/services/Launcher;->RequestState(Lme/embodied/services/Launcher$LauncherState;)V

    goto :goto_0

    .line 1338
    :cond_1
    const/4 v0, 0x1

    invoke-virtual {p0, v0}, Lme/embodied/services/Launcher;->setAnimationState(Z)V

    .line 1339
    new-instance v0, Lme/embodied/services/Launcher$6;

    invoke-direct {v0, p0}, Lme/embodied/services/Launcher$6;-><init>(Lme/embodied/services/Launcher;)V

    .line 1361
    invoke-virtual {v0}, Lme/embodied/services/Launcher$6;->start()V

    .line 1363
    :goto_0
    return-void
.end method

.method public OnConfigNeeded()V
    .locals 3

    .line 1506
    iget-boolean v0, p0, Lme/embodied/services/Launcher;->has_config_app_:Z

    const-string v1, "bo-launcher-j"

    if-eqz v0, :cond_2

    iget-boolean v0, p0, Lme/embodied/services/Launcher;->self_managed_:Z

    if-nez v0, :cond_0

    goto :goto_1

    .line 1512
    :cond_0
    iget-object v0, p0, Lme/embodied/services/Launcher;->state_:Lme/embodied/services/Launcher$LauncherState;

    sget-object v2, Lme/embodied/services/Launcher$LauncherState;->STATE_RUNNING:Lme/embodied/services/Launcher$LauncherState;

    if-ne v0, v2, :cond_1

    .line 1513
    const-string v0, "Configuration needed, but running.  Requesting graceful user interaction."

    invoke-static {v1, v0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1514
    invoke-virtual {p0}, Lme/embodied/services/Launcher;->startDelayedDisengage()V

    goto :goto_0

    .line 1517
    :cond_1
    const-string v0, "Configuration needed. Starting config app."

    invoke-static {v1, v0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1518
    invoke-direct {p0}, Lme/embodied/services/Launcher;->RequstConfigState()V

    .line 1520
    :goto_0
    return-void

    .line 1507
    :cond_2
    :goto_1
    const-string v0, "BO#8007 Configuration needed, but config app is unavailable or not running from RobotLauncher. Ignoring."

    invoke-static {v1, v0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 1508
    return-void
.end method

.method public OnConfigUnquiet()V
    .locals 3

    .line 1379
    const-string v0, "bo-launcher-j"

    const-string v1, "Config app wants unquiet, must REALLY not have wifi."

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1381
    const/16 v1, 0x8f

    invoke-direct {p0, v1}, Lme/embodied/services/Launcher;->SetWakeWordKey(I)V

    .line 1382
    const/4 v1, 0x0

    const/4 v2, 0x1

    invoke-direct {p0, v1, v2}, Lme/embodied/services/Launcher;->clearSilentRebootIfSet(ZZ)V

    .line 1384
    :try_start_0
    const-string v1, "/sdcard/EmbodiedStaticData/PERSISTENT_DATA/brightness"

    invoke-static {v1}, Lme/embodied/services/Launcher;->readLineFromFile(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v1

    invoke-direct {p0, v1}, Lme/embodied/services/Launcher;->SetBacklight(I)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    .line 1388
    goto :goto_0

    .line 1385
    :catch_0
    move-exception v1

    .line 1386
    const-string v1, "Failed to read user brightness, using default 100"

    invoke-static {v0, v1}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 1387
    const/16 v0, 0x64

    invoke-direct {p0, v0}, Lme/embodied/services/Launcher;->SetBacklight(I)V

    .line 1389
    :goto_0
    return-void
.end method

.method public OnDebugConfigRequest(II)V
    .locals 3

    .line 1639
    const-string v0, "bo-launcher-j"

    if-eqz p1, :cond_0

    .line 1654
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "BO#3033 Internal error passing unsupported DebugConfigRequest target: "

    invoke-virtual {p2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v0, p1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_3

    .line 1642
    :cond_0
    if-nez p2, :cond_1

    const/4 p1, 0x1

    goto :goto_0

    :cond_1
    const/4 p1, 0x0

    .line 1643
    :goto_0
    if-nez p1, :cond_2

    iget-object p2, p0, Lme/embodied/services/Launcher;->os_props_:Lme/embodied/AndroidSystemProps;

    invoke-virtual {p2}, Lme/embodied/AndroidSystemProps;->isDebugEnvironment()Z

    move-result p2

    if-nez p2, :cond_2

    .line 1644
    const-string p1, "BO#3033 ADB debug over internet NOT enabled due to production robot."

    invoke-static {v0, p1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 1645
    return-void

    .line 1647
    :cond_2
    new-instance p2, Landroid/content/Intent;

    invoke-direct {p2}, Landroid/content/Intent;-><init>()V

    .line 1648
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "com.embodied.osctrl.adb_wifi_"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    if-eqz p1, :cond_3

    const-string v2, "disable"

    goto :goto_1

    :cond_3
    const-string v2, "enable"

    :goto_1
    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p2, v1}, Landroid/content/Intent;->setAction(Ljava/lang/String;)Landroid/content/Intent;

    .line 1649
    iget-object v1, p0, Lme/embodied/services/Launcher;->context_:Landroid/content/Context;

    invoke-virtual {v1, p2}, Landroid/content/Context;->sendBroadcast(Landroid/content/Intent;)V

    .line 1650
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "BO#3033 ADB debug over internet has been "

    invoke-virtual {p2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    if-eqz p1, :cond_4

    const-string p1, "DISABLED"

    goto :goto_2

    :cond_4
    const-string p1, "ENABLED"

    :goto_2
    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v0, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1651
    nop

    .line 1657
    :goto_3
    return-void
.end method

.method public OnFactoryTestRequest(I)V
    .locals 4

    .line 1608
    const-string v0, "bo-launcher-j"

    if-ltz p1, :cond_2

    :try_start_0
    invoke-static {}, Lme/embodied/services/Launcher$FactoryTest;->values()[Lme/embodied/services/Launcher$FactoryTest;

    move-result-object v1

    array-length v1, v1

    if-lt p1, v1, :cond_0

    goto :goto_0

    .line 1613
    :cond_0
    invoke-static {}, Lme/embodied/services/Launcher$FactoryTest;->values()[Lme/embodied/services/Launcher$FactoryTest;

    move-result-object v1

    aget-object p1, v1, p1

    .line 1614
    sget-object v1, Lme/embodied/services/Launcher$FactoryTest;->DEMO_MODE:Lme/embodied/services/Launcher$FactoryTest;

    if-ne p1, v1, :cond_1

    .line 1617
    const/4 p1, 0x1

    iput-boolean p1, p0, Lme/embodied/services/Launcher;->factory_demo_mode_:Z

    .line 1618
    sget-object p1, Lme/embodied/services/Launcher$LauncherState;->STATE_STARTUP:Lme/embodied/services/Launcher$LauncherState;

    invoke-direct {p0, p1}, Lme/embodied/services/Launcher;->RequestState(Lme/embodied/services/Launcher$LauncherState;)V

    .line 1619
    return-void

    .line 1622
    :cond_1
    sget-object v1, Lme/embodied/services/Launcher$LauncherState;->STATE_INIT:Lme/embodied/services/Launcher$LauncherState;

    invoke-direct {p0, v1}, Lme/embodied/services/Launcher;->RequestState(Lme/embodied/services/Launcher$LauncherState;)V

    .line 1623
    const-wide/16 v1, 0x3e8

    invoke-static {v1, v2}, Ljava/lang/Thread;->sleep(J)V

    .line 1624
    new-instance v1, Landroid/content/Intent;

    invoke-direct {v1}, Landroid/content/Intent;-><init>()V

    .line 1625
    invoke-virtual {p1}, Lme/embodied/services/Launcher$FactoryTest;->getComponent()Landroid/content/ComponentName;

    move-result-object v2

    invoke-virtual {v1, v2}, Landroid/content/Intent;->setComponent(Landroid/content/ComponentName;)Landroid/content/Intent;

    .line 1626
    const-string v2, "android.intent.action.MAIN"

    invoke-virtual {v1, v2}, Landroid/content/Intent;->setAction(Ljava/lang/String;)Landroid/content/Intent;

    .line 1627
    const v2, 0x10018000

    invoke-virtual {v1, v2}, Landroid/content/Intent;->setFlags(I)Landroid/content/Intent;

    .line 1628
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "BO#8008 Launching Factory Test: "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v0, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1629
    iget-object p1, p0, Lme/embodied/services/Launcher;->context_:Landroid/content/Context;

    invoke-virtual {p1, v1}, Landroid/content/Context;->startActivity(Landroid/content/Intent;)V

    .line 1634
    goto :goto_2

    .line 1631
    :catch_0
    move-exception p1

    goto :goto_1

    .line 1609
    :cond_2
    :goto_0
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "BO#8009 Received unsupported factory test id: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v0, p1}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 1610
    return-void

    .line 1632
    :goto_1
    const-string v1, "BO#8009 Failed to launch factory test app.  Returning to config state."

    invoke-static {v0, v1, p1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)I

    .line 1633
    sget-object p1, Lme/embodied/services/Launcher$LauncherState;->STATE_CONFIG:Lme/embodied/services/Launcher$LauncherState;

    invoke-direct {p0, p1}, Lme/embodied/services/Launcher;->RequestState(Lme/embodied/services/Launcher$LauncherState;)V

    .line 1635
    :goto_2
    return-void
.end method

.method public OnNetRecoveryNeeded()V
    .locals 2

    .line 1436
    iget-object v0, p0, Lme/embodied/services/Launcher;->state_:Lme/embodied/services/Launcher$LauncherState;

    sget-object v1, Lme/embodied/services/Launcher$LauncherState;->STATE_RUNNING:Lme/embodied/services/Launcher$LauncherState;

    if-eq v0, v1, :cond_1

    iget-object v0, p0, Lme/embodied/services/Launcher;->state_:Lme/embodied/services/Launcher$LauncherState;

    sget-object v1, Lme/embodied/services/Launcher$LauncherState;->STATE_STARTUP:Lme/embodied/services/Launcher$LauncherState;

    if-eq v0, v1, :cond_1

    iget-object v0, p0, Lme/embodied/services/Launcher;->state_:Lme/embodied/services/Launcher$LauncherState;

    sget-object v1, Lme/embodied/services/Launcher$LauncherState;->STATE_DEMO:Lme/embodied/services/Launcher$LauncherState;

    if-eq v0, v1, :cond_1

    iget-object v0, p0, Lme/embodied/services/Launcher;->state_:Lme/embodied/services/Launcher$LauncherState;

    sget-object v1, Lme/embodied/services/Launcher$LauncherState;->STATE_RECOVERY:Lme/embodied/services/Launcher$LauncherState;

    if-eq v0, v1, :cond_1

    iget-object v0, p0, Lme/embodied/services/Launcher;->state_:Lme/embodied/services/Launcher$LauncherState;

    sget-object v1, Lme/embodied/services/Launcher$LauncherState;->STATE_TELEBRAIN:Lme/embodied/services/Launcher$LauncherState;

    if-ne v0, v1, :cond_0

    goto :goto_0

    .line 1439
    :cond_0
    iget-object v0, p0, Lme/embodied/services/Launcher;->state_:Lme/embodied/services/Launcher$LauncherState;

    sget-object v1, Lme/embodied/services/Launcher$LauncherState;->STATE_LIGHT_SLEEP:Lme/embodied/services/Launcher$LauncherState;

    if-ne v0, v1, :cond_2

    .line 1440
    sget-object v0, Lme/embodied/services/Launcher$LauncherState;->STATE_SUSPEND:Lme/embodied/services/Launcher$LauncherState;

    invoke-direct {p0, v0}, Lme/embodied/services/Launcher;->RequestState(Lme/embodied/services/Launcher$LauncherState;)V

    goto :goto_1

    .line 1438
    :cond_1
    :goto_0
    sget-object v0, Lme/embodied/services/Launcher$LauncherState;->STATE_CONFIG:Lme/embodied/services/Launcher$LauncherState;

    invoke-direct {p0, v0}, Lme/embodied/services/Launcher;->RequestState(Lme/embodied/services/Launcher$LauncherState;)V

    .line 1442
    :cond_2
    :goto_1
    return-void
.end method

.method public OnOTAStatus(IFZI)V
    .locals 3

    .line 1733
    const-string v0, "bo-launcher-j"

    if-nez p1, :cond_0

    if-eqz p3, :cond_0

    .line 1734
    const-string v1, "OTA Status: Complete, clearing last OTA signal."

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1735
    const-wide/16 v0, 0x0

    iput-wide v0, p0, Lme/embodied/services/Launcher;->last_ota_signal_:J

    goto :goto_0

    .line 1736
    :cond_0
    const/4 v1, 0x1

    if-lt p1, v1, :cond_1

    const/4 v1, 0x5

    if-gt p1, v1, :cond_1

    .line 1737
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "OTA Status: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v2, ", percent: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, p2}, Ljava/lang/StringBuilder;->append(F)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1738
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    iput-wide v0, p0, Lme/embodied/services/Launcher;->last_ota_signal_:J

    .line 1740
    :cond_1
    :goto_0
    invoke-static {p1, p2, p3, p4}, Lme/embodied/services/Launcher;->reportOTAStatus(IFZI)V

    .line 1741
    return-void
.end method

.method public OnServiceStartup(Ljava/lang/String;)V
    .locals 3

    .line 1727
    const/16 v0, 0x2e

    invoke-virtual {p1, v0}, Ljava/lang/String;->lastIndexOf(I)I

    move-result v0

    add-int/lit8 v0, v0, 0x1

    invoke-virtual {p1, v0}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object v0

    .line 1728
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Hello, service "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, "!"

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "bo-launcher-j"

    invoke-static {v1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 1729
    iget-object v0, p0, Lme/embodied/services/Launcher;->service_metrics_:Lme/embodied/ServiceMetrics;

    new-instance v1, Landroid/content/ComponentName;

    const-string v2, "com.embodied.bo_unity"

    invoke-direct {v1, v2, p1}, Landroid/content/ComponentName;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Lme/embodied/ServiceMetrics;->onStartupDetect(Landroid/content/ComponentName;)V

    .line 1730
    return-void
.end method

.method public OnSetState(Ljava/lang/String;Z)V
    .locals 2

    .line 1560
    const/4 v0, 0x2

    new-array v0, v0, [Ljava/lang/Object;

    if-eqz p2, :cond_0

    const-string p2, "start"

    goto :goto_0

    :cond_0
    const-string p2, "stop"

    :goto_0
    const/4 v1, 0x0

    aput-object p2, v0, v1

    const/4 p2, 0x1

    aput-object p1, v0, p2

    const-string p1, "Received request to %s %s"

    invoke-static {p1, v0}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    const-string p2, "bo-launcher-j"

    invoke-static {p2, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1561
    return-void
.end method

.method public OnShutdownRequest(I)V
    .locals 2

    .line 1565
    const/4 v0, 0x3

    if-ne p1, v0, :cond_0

    .line 1567
    invoke-direct {p0}, Lme/embodied/services/Launcher;->cleanLegacyFilesRestart()V

    goto :goto_0

    .line 1570
    :cond_0
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    iput-wide v0, p0, Lme/embodied/services/Launcher;->last_wake_signal_:J

    .line 1571
    iget-object v0, p0, Lme/embodied/services/Launcher;->service_metrics_:Lme/embodied/ServiceMetrics;

    invoke-virtual {v0, p1}, Lme/embodied/ServiceMetrics;->onExternalError(I)V

    .line 1573
    :goto_0
    return-void
.end method

.method public OnStayAwake()V
    .locals 2

    .line 1497
    const-string v0, "bo-launcher-j"

    const-string v1, "Received StayAwakeSignal"

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1498
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    iput-wide v0, p0, Lme/embodied/services/Launcher;->last_wake_signal_:J

    .line 1499
    return-void
.end method

.method public OnSuspendRequest()V
    .locals 1

    .line 1316
    invoke-virtual {p0}, Lme/embodied/services/Launcher;->OnStayAwake()V

    .line 1317
    invoke-virtual {p0}, Lme/embodied/services/Launcher;->SupportsLightSleep()Z

    move-result v0

    if-eqz v0, :cond_0

    sget-object v0, Lme/embodied/services/Launcher$LauncherState;->STATE_LIGHT_SLEEP:Lme/embodied/services/Launcher$LauncherState;

    goto :goto_0

    :cond_0
    sget-object v0, Lme/embodied/services/Launcher$LauncherState;->STATE_SUSPEND:Lme/embodied/services/Launcher$LauncherState;

    :goto_0
    invoke-direct {p0, v0}, Lme/embodied/services/Launcher;->RequestState(Lme/embodied/services/Launcher$LauncherState;)V

    .line 1318
    return-void
.end method

.method public OnTelehealthChange(ZZ)V
    .locals 2

    .line 1577
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Telehealth Changed to: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-static {p1}, Ljava/lang/Boolean;->toString(Z)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "bo-launcher-j"

    invoke-static {v1, v0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1578
    iput-boolean p1, p0, Lme/embodied/services/Launcher;->telehealth_state_:Z

    .line 1580
    if-eqz p1, :cond_3

    .line 1581
    iget-object p1, p0, Lme/embodied/services/Launcher;->state_:Lme/embodied/services/Launcher$LauncherState;

    sget-object v0, Lme/embodied/services/Launcher$LauncherState;->STATE_RUNNING:Lme/embodied/services/Launcher$LauncherState;

    if-eq p1, v0, :cond_1

    iget-object p1, p0, Lme/embodied/services/Launcher;->state_:Lme/embodied/services/Launcher$LauncherState;

    sget-object v0, Lme/embodied/services/Launcher$LauncherState;->STATE_LIGHT_SLEEP:Lme/embodied/services/Launcher$LauncherState;

    if-eq p1, v0, :cond_1

    iget-object p1, p0, Lme/embodied/services/Launcher;->state_:Lme/embodied/services/Launcher$LauncherState;

    sget-object v0, Lme/embodied/services/Launcher$LauncherState;->STATE_SUSPEND:Lme/embodied/services/Launcher$LauncherState;

    if-ne p1, v0, :cond_0

    goto :goto_0

    .line 1592
    :cond_0
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string p2, "Telehealth activation detected while in pending state: "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object p2, p0, Lme/embodied/services/Launcher;->state_:Lme/embodied/services/Launcher$LauncherState;

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v1, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_1

    .line 1582
    :cond_1
    :goto_0
    if-eqz p2, :cond_2

    .line 1584
    const-string p1, "Telehealth activating while in active use.  Exiting gracefully."

    invoke-static {v1, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1585
    sget-object p1, Lme/embodied/services/Launcher$LauncherState;->STATE_TELEBRAIN:Lme/embodied/services/Launcher$LauncherState;

    sget-object p2, Lme/embodied/services/Launcher$DisengageReason;->TELEHEALTH:Lme/embodied/services/Launcher$DisengageReason;

    invoke-direct {p0, p1, p2}, Lme/embodied/services/Launcher;->RequestConfigRetarget(Lme/embodied/services/Launcher$LauncherState;Lme/embodied/services/Launcher$DisengageReason;)V

    goto :goto_1

    .line 1587
    :cond_2
    const-string p1, "Telehealth activating while out of session.  Switching aggressively."

    invoke-static {v1, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1588
    sget-object p1, Lme/embodied/services/Launcher$LauncherState;->STATE_TELEBRAIN:Lme/embodied/services/Launcher$LauncherState;

    invoke-direct {p0, p1}, Lme/embodied/services/Launcher;->RequestState(Lme/embodied/services/Launcher$LauncherState;)V

    goto :goto_1

    .line 1595
    :cond_3
    iget-object p1, p0, Lme/embodied/services/Launcher;->state_:Lme/embodied/services/Launcher$LauncherState;

    sget-object p2, Lme/embodied/services/Launcher$LauncherState;->STATE_TELEBRAIN:Lme/embodied/services/Launcher$LauncherState;

    if-ne p1, p2, :cond_4

    .line 1597
    const-string p1, "Telehealth deactivating, fast switching to primary runnign state."

    invoke-static {v1, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1598
    sget-object p1, Lme/embodied/services/Launcher$LauncherState;->STATE_RUNNING:Lme/embodied/services/Launcher$LauncherState;

    invoke-direct {p0, p1}, Lme/embodied/services/Launcher;->RequestState(Lme/embodied/services/Launcher$LauncherState;)V

    goto :goto_1

    .line 1600
    :cond_4
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string p2, "Telehealth deactivation detected while in state: "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object p2, p0, Lme/embodied/services/Launcher;->state_:Lme/embodied/services/Launcher$LauncherState;

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v1, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1603
    :goto_1
    return-void
.end method

.method public OnUnpairUserReady()V
    .locals 2

    .line 1550
    const-string v0, "bo-launcher-j"

    const-string v1, "User interaction for Unpairing complete. Starting config app."

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1551
    invoke-virtual {p0}, Lme/embodied/services/Launcher;->cancelDelayedUnpairing()V

    .line 1552
    invoke-direct {p0}, Lme/embodied/services/Launcher;->RequstConfigState()V

    .line 1553
    return-void
.end method

.method public OnUserAlarmPing(I)V
    .locals 2

    .line 1756
    const-string v0, "bo-launcher-j"

    const-string v1, "GOT USER ALARM CLOCK PING!"

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1757
    iget-object v0, p0, Lme/embodied/services/Launcher;->state_:Lme/embodied/services/Launcher$LauncherState;

    sget-object v1, Lme/embodied/services/Launcher$LauncherState;->STATE_SUSPEND:Lme/embodied/services/Launcher$LauncherState;

    if-eq v0, v1, :cond_1

    iget-object v0, p0, Lme/embodied/services/Launcher;->state_:Lme/embodied/services/Launcher$LauncherState;

    sget-object v1, Lme/embodied/services/Launcher$LauncherState;->STATE_LIGHT_SLEEP:Lme/embodied/services/Launcher$LauncherState;

    if-ne v0, v1, :cond_0

    goto :goto_0

    .line 1763
    :cond_0
    invoke-static {p1}, Lme/embodied/services/Launcher;->SignalUserAlarm(I)V

    goto :goto_1

    .line 1758
    :cond_1
    :goto_0
    invoke-virtual {p0}, Lme/embodied/services/Launcher;->OnStayAwake()V

    .line 1759
    invoke-virtual {p0, p1}, Lme/embodied/services/Launcher;->queuePendingAlarm(I)V

    .line 1760
    sget-object p1, Lme/embodied/services/Launcher$LauncherState;->STATE_RUNNING:Lme/embodied/services/Launcher$LauncherState;

    invoke-direct {p0, p1}, Lme/embodied/services/Launcher;->RequestState(Lme/embodied/services/Launcher$LauncherState;)V

    .line 1765
    :goto_1
    return-void
.end method

.method public OnUserAlarmRequest(JIJ)V
    .locals 13

    .line 1679
    move-object v0, p0

    move/from16 v1, p3

    iget-object v2, v0, Lme/embodied/services/Launcher;->context_:Landroid/content/Context;

    const-string v3, "alarm"

    invoke-virtual {v2, v3}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Landroid/app/AlarmManager;

    .line 1680
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v3

    .line 1681
    const-wide/16 v5, 0x0

    cmp-long v7, p1, v5

    const-string v8, "USER ALARM - "

    const-string v9, "bo-launcher-j"

    if-eqz v7, :cond_3

    .line 1682
    cmp-long v7, p1, v3

    const-string v10, "USER REPEAT ALARM - "

    if-gtz v7, :cond_1

    .line 1685
    cmp-long v7, p4, v5

    if-lez v7, :cond_0

    .line 1687
    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v7, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v11, " Requested for a time in the past.  Adjusting for the future"

    invoke-virtual {v7, v11}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    invoke-static {v9, v7}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1690
    sub-long/2addr v3, p1

    add-long v3, v3, p4

    const-wide/16 v11, 0x1

    sub-long/2addr v3, v11

    div-long v3, v3, p4

    mul-long v3, v3, p4

    add-long/2addr v3, p1

    goto :goto_0

    .line 1694
    :cond_0
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v3, " Requested for a time in the past.  Executing immediately."

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v9, v2}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1695
    invoke-virtual {p0, v1}, Lme/embodied/services/Launcher;->OnUserAlarmPing(I)V

    .line 1696
    return-void

    .line 1682
    :cond_1
    move-wide v3, p1

    .line 1701
    :goto_0
    cmp-long v5, p4, v5

    const-string v6, " - Wake me up at "

    if-lez v5, :cond_2

    .line 1703
    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v5, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    new-instance v6, Ljava/util/Date;

    invoke-direct {v6, v3, v4}, Ljava/util/Date;-><init>(J)V

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v6, " and repeat every: "

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-static/range {p4 .. p5}, Lme/embodied/Formatter;->formatTimeInterval(J)Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-static {v9, v5}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1704
    const/4 v5, 0x0

    iget-object v6, v0, Lme/embodied/services/Launcher;->service_:Lme/embodied/services/ServiceLauncher;

    .line 1705
    invoke-virtual {v6, v1}, Lme/embodied/services/ServiceLauncher;->makeUserAlarmIntent(I)Landroid/app/PendingIntent;

    move-result-object v7

    .line 1704
    move-object v1, v2

    move v2, v5

    move-wide/from16 v5, p4

    invoke-virtual/range {v1 .. v7}, Landroid/app/AlarmManager;->setRepeating(IJJLandroid/app/PendingIntent;)V

    goto :goto_1

    .line 1709
    :cond_2
    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v5, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    new-instance v6, Ljava/util/Date;

    invoke-direct {v6, v3, v4}, Ljava/util/Date;-><init>(J)V

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v6, " please!"

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-static {v9, v5}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1710
    const/4 v5, 0x0

    iget-object v6, v0, Lme/embodied/services/Launcher;->service_:Lme/embodied/services/ServiceLauncher;

    .line 1711
    invoke-virtual {v6, v1}, Lme/embodied/services/ServiceLauncher;->makeUserAlarmIntent(I)Landroid/app/PendingIntent;

    move-result-object v1

    .line 1710
    invoke-virtual {v2, v5, v3, v4, v1}, Landroid/app/AlarmManager;->setExact(IJLandroid/app/PendingIntent;)V

    goto :goto_1

    .line 1714
    :cond_3
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v4, " - Canceling"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-static {v9, v3}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1715
    iget-object v3, v0, Lme/embodied/services/Launcher;->service_:Lme/embodied/services/ServiceLauncher;

    invoke-virtual {v3, v1}, Lme/embodied/services/ServiceLauncher;->makeUserAlarmIntent(I)Landroid/app/PendingIntent;

    move-result-object v1

    invoke-virtual {v2, v1}, Landroid/app/AlarmManager;->cancel(Landroid/app/PendingIntent;)V

    .line 1718
    :goto_1
    return-void
.end method

.method public OnUserDataStatus(I)V
    .locals 4

    .line 1461
    const/4 v0, 0x0

    iput-boolean v0, p0, Lme/embodied/services/Launcher;->user_data_recover_active_:Z

    .line 1462
    iget-object v0, p0, Lme/embodied/services/Launcher;->state_:Lme/embodied/services/Launcher$LauncherState;

    sget-object v1, Lme/embodied/services/Launcher$LauncherState;->STATE_RECOVERY:Lme/embodied/services/Launcher$LauncherState;

    if-eq v0, v1, :cond_0

    iget-object v0, p0, Lme/embodied/services/Launcher;->state_:Lme/embodied/services/Launcher$LauncherState;

    sget-object v1, Lme/embodied/services/Launcher$LauncherState;->STATE_SILENT_RECOVERY:Lme/embodied/services/Launcher$LauncherState;

    if-ne v0, v1, :cond_5

    .line 1463
    :cond_0
    const/4 v0, 0x5

    const-string v1, "bo-launcher-j"

    if-eq p1, v0, :cond_2

    const/4 v2, 0x6

    if-ne p1, v2, :cond_1

    goto :goto_0

    .line 1472
    :cond_1
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Unexpected user data state during recovery: "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v1, p1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_1

    .line 1464
    :cond_2
    :goto_0
    iget-boolean p1, p0, Lme/embodied/services/Launcher;->user_data_dirty_:Z

    if-eqz p1, :cond_3

    .line 1465
    const-string p1, "Data recovery has completed successfully, but another update is pending.  Running again."

    invoke-static {v1, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1466
    invoke-static {}, Lme/embodied/services/Launcher;->StartUserRecovery()V

    .line 1467
    return-void

    .line 1469
    :cond_3
    const-string p1, "User data update completed."

    invoke-static {v1, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1475
    :goto_1
    const/4 p1, 0x1

    iput-boolean p1, p0, Lme/embodied/services/Launcher;->brain_update_ready_:Z

    .line 1476
    iget-object p1, p0, Lme/embodied/services/Launcher;->state_:Lme/embodied/services/Launcher$LauncherState;

    sget-object v2, Lme/embodied/services/Launcher$LauncherState;->STATE_RECOVERY:Lme/embodied/services/Launcher$LauncherState;

    if-ne p1, v2, :cond_4

    .line 1478
    const-string p1, "User Data Update Complete.  NotifyingResume and Resuming primary run state."

    invoke-static {v1, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1479
    invoke-direct {p0}, Lme/embodied/services/Launcher;->RequestPrimaryRunState()V

    .line 1480
    invoke-static {v0}, Lme/embodied/services/Launcher;->NotifyResume(I)V

    goto :goto_2

    .line 1484
    :cond_4
    const-string p1, "User Data Update Completed silently.  Resuming sleep."

    invoke-static {v1, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1485
    invoke-virtual {p0}, Lme/embodied/services/Launcher;->OnStayAwake()V

    .line 1486
    sget-object p1, Lme/embodied/services/Launcher$LauncherState;->STATE_LIGHT_SLEEP:Lme/embodied/services/Launcher$LauncherState;

    invoke-direct {p0, p1}, Lme/embodied/services/Launcher;->RequestState(Lme/embodied/services/Launcher$LauncherState;)V

    .line 1487
    invoke-static {v0}, Lme/embodied/services/Launcher;->NotifyResume(I)V

    .line 1490
    :cond_5
    :goto_2
    return-void
.end method

.method public OnUserUpdateNeeded(Z)V
    .locals 1

    .line 1527
    const-string v0, "bo-launcher-j"

    if-eqz p1, :cond_2

    .line 1529
    const-string p1, "User Data Status has transitioned to UPDATE_NEEDED"

    invoke-static {v0, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1530
    const/4 p1, 0x1

    iput-boolean p1, p0, Lme/embodied/services/Launcher;->user_data_dirty_:Z

    .line 1532
    sget-object p1, Lme/embodied/services/Launcher$UserDataRecoveryMode;->UPDATE_CLOUD:Lme/embodied/services/Launcher$UserDataRecoveryMode;

    iput-object p1, p0, Lme/embodied/services/Launcher;->user_data_recovery_mode_:Lme/embodied/services/Launcher$UserDataRecoveryMode;

    .line 1533
    iget-object p1, p0, Lme/embodied/services/Launcher;->state_:Lme/embodied/services/Launcher$LauncherState;

    sget-object v0, Lme/embodied/services/Launcher$LauncherState;->STATE_RUNNING:Lme/embodied/services/Launcher$LauncherState;

    if-ne p1, v0, :cond_0

    .line 1534
    sget-object p1, Lme/embodied/services/Launcher$LauncherState;->STATE_RECOVERY:Lme/embodied/services/Launcher$LauncherState;

    sget-object v0, Lme/embodied/services/Launcher$DisengageReason;->USER_DATA_UPDATE:Lme/embodied/services/Launcher$DisengageReason;

    invoke-direct {p0, p1, v0}, Lme/embodied/services/Launcher;->RequestConfigRetarget(Lme/embodied/services/Launcher$LauncherState;Lme/embodied/services/Launcher$DisengageReason;)V

    goto :goto_0

    .line 1536
    :cond_0
    iget-object p1, p0, Lme/embodied/services/Launcher;->state_:Lme/embodied/services/Launcher$LauncherState;

    sget-object v0, Lme/embodied/services/Launcher$LauncherState;->STATE_LIGHT_SLEEP:Lme/embodied/services/Launcher$LauncherState;

    if-ne p1, v0, :cond_1

    .line 1537
    sget-object p1, Lme/embodied/services/Launcher$LauncherState;->STATE_SILENT_RECOVERY:Lme/embodied/services/Launcher$LauncherState;

    sget-object v0, Lme/embodied/services/Launcher$DisengageReason;->USER_DATA_UPDATE:Lme/embodied/services/Launcher$DisengageReason;

    invoke-direct {p0, p1, v0}, Lme/embodied/services/Launcher;->RequestConfigRetarget(Lme/embodied/services/Launcher$LauncherState;Lme/embodied/services/Launcher$DisengageReason;)V

    goto :goto_0

    .line 1538
    :cond_1
    iget-object p1, p0, Lme/embodied/services/Launcher;->state_:Lme/embodied/services/Launcher$LauncherState;

    sget-object v0, Lme/embodied/services/Launcher$LauncherState;->STATE_STARTUP:Lme/embodied/services/Launcher$LauncherState;

    if-ne p1, v0, :cond_3

    .line 1540
    invoke-direct {p0}, Lme/embodied/services/Launcher;->RequstConfigState()V

    goto :goto_0

    .line 1543
    :cond_2
    const-string p1, "User Data Status has transitioned from UPDATE_NEEDED to READY"

    invoke-static {v0, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1544
    const/4 p1, 0x0

    iput-boolean p1, p0, Lme/embodied/services/Launcher;->user_data_dirty_:Z

    .line 1546
    :cond_3
    :goto_0
    return-void
.end method

.method public OnWakeSignal(Z)V
    .locals 2

    .line 865
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Received "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    if-eqz p1, :cond_0

    const-string p1, "SOFT"

    goto :goto_0

    :cond_0
    const-string p1, "HARD"

    :goto_0
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p1, " WAKE signal"

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const-string v0, "bo-launcher-j"

    invoke-static {v0, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 866
    iget-object p1, p0, Lme/embodied/services/Launcher;->state_:Lme/embodied/services/Launcher$LauncherState;

    invoke-virtual {p1}, Lme/embodied/services/Launcher$LauncherState;->screenActive()Z

    move-result p1

    if-nez p1, :cond_3

    .line 867
    iget-object p1, p0, Lme/embodied/services/Launcher;->state_:Lme/embodied/services/Launcher$LauncherState;

    sget-object v1, Lme/embodied/services/Launcher$LauncherState;->STATE_SILENT_RECOVERY:Lme/embodied/services/Launcher$LauncherState;

    if-ne p1, v1, :cond_1

    .line 870
    const-string p1, "Waking up during silent recovery, adding recovery icon manually."

    invoke-static {v0, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 871
    invoke-static {}, Lme/embodied/services/Launcher;->RecoveryAgency()V

    .line 872
    sget-object p1, Lme/embodied/services/Launcher$LauncherState;->STATE_RECOVERY:Lme/embodied/services/Launcher$LauncherState;

    invoke-direct {p0, p1}, Lme/embodied/services/Launcher;->RequestState(Lme/embodied/services/Launcher$LauncherState;)V

    goto :goto_1

    .line 874
    :cond_1
    invoke-static {}, Lme/embodied/services/Launcher;->PendingBrainUpdate()Z

    move-result p1

    if-eqz p1, :cond_2

    .line 877
    const-string p1, "Waking up after silent recovery and before RB startup ready, adding recovery icon manually."

    invoke-static {v0, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 878
    invoke-static {}, Lme/embodied/services/Launcher;->RecoveryAgency()V

    .line 880
    :cond_2
    invoke-direct {p0}, Lme/embodied/services/Launcher;->RequestPrimaryRunState()V

    .line 883
    :cond_3
    :goto_1
    return-void
.end method

.method public StartConfigActivity()V
    .locals 2

    .line 834
    const/4 v0, 0x1

    iput-boolean v0, p0, Lme/embodied/services/Launcher;->self_managed_:Z

    .line 836
    iget-object v0, p0, Lme/embodied/services/Launcher;->state_:Lme/embodied/services/Launcher$LauncherState;

    sget-object v1, Lme/embodied/services/Launcher$LauncherState;->STATE_INIT:Lme/embodied/services/Launcher$LauncherState;

    if-ne v0, v1, :cond_0

    .line 838
    sget-object v0, Lme/embodied/services/Launcher$LauncherState;->STATE_CONFIG:Lme/embodied/services/Launcher$LauncherState;

    invoke-direct {p0, v0}, Lme/embodied/services/Launcher;->RequestState(Lme/embodied/services/Launcher$LauncherState;)V

    goto :goto_0

    .line 842
    :cond_0
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Ignoring repeated start, already in state "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lme/embodied/services/Launcher;->state_:Lme/embodied/services/Launcher$LauncherState;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "bo-launcher-j"

    invoke-static {v1, v0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 844
    :goto_0
    return-void
.end method

.method public Stop()V
    .locals 4

    .line 472
    iget-object v0, p0, Lme/embodied/services/Launcher;->monitor_:Lme/embodied/services/Launcher$ServiceMonitor;

    invoke-virtual {v0}, Lme/embodied/services/Launcher$ServiceMonitor;->Stop()V

    .line 473
    iget-object v0, p0, Lme/embodied/services/Launcher;->monitor_:Lme/embodied/services/Launcher$ServiceMonitor;

    invoke-virtual {v0}, Lme/embodied/services/Launcher$ServiceMonitor;->interrupt()V

    .line 475
    :try_start_0
    iget-object v0, p0, Lme/embodied/services/Launcher;->monitor_:Lme/embodied/services/Launcher$ServiceMonitor;

    invoke-virtual {v0}, Lme/embodied/services/Launcher$ServiceMonitor;->join()V
    :try_end_0
    .catch Ljava/lang/InterruptedException; {:try_start_0 .. :try_end_0} :catch_0

    .line 478
    goto :goto_0

    .line 476
    :catch_0
    move-exception v0

    .line 477
    const-string v0, "bo-launcher-j"

    const-string v1, "INTERRUPTED?"

    invoke-static {v0, v1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 480
    :goto_0
    iget-object v0, p0, Lme/embodied/services/Launcher;->components_:Ljava/util/Vector;

    const/4 v1, 0x0

    new-array v2, v1, [Landroid/content/ComponentName;

    invoke-virtual {v0, v2}, Ljava/util/Vector;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Landroid/content/ComponentName;

    .line 481
    array-length v2, v0

    :goto_1
    if-ge v1, v2, :cond_0

    aget-object v3, v0, v1

    .line 482
    invoke-direct {p0, v3}, Lme/embodied/services/Launcher;->StopComponent(Landroid/content/ComponentName;)Z

    .line 481
    add-int/lit8 v1, v1, 0x1

    goto :goto_1

    .line 484
    :cond_0
    const/4 v0, 0x0

    sput-object v0, Lme/embodied/services/Launcher;->instance_:Lme/embodied/services/Launcher;

    .line 485
    return-void
.end method

.method SupportsLightSleep()Z
    .locals 2

    .line 488
    iget v0, p0, Lme/embodied/services/Launcher;->os_revision_:I

    const/16 v1, 0x75f8

    if-lt v0, v1, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public UnitTestServices(Ljava/lang/String;)V
    .locals 12

    .line 917
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Unit Signal - "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "bo-launcher-j"

    invoke-static {v1, v0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 918
    if-eqz p1, :cond_1f

    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result v0

    const/4 v2, 0x2

    if-ge v0, v2, :cond_0

    goto/16 :goto_9

    .line 921
    :cond_0
    const/4 v0, 0x0

    invoke-virtual {p1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v3

    .line 922
    const/16 v4, 0x2b

    const/4 v5, 0x1

    if-eq v3, v4, :cond_19

    const/16 v6, 0x2d

    if-ne v3, v6, :cond_1

    goto/16 :goto_4

    .line 962
    :cond_1
    const/16 v4, 0x73

    if-ne v3, v4, :cond_4

    .line 963
    invoke-virtual {p1, v5}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object p1

    .line 965
    :try_start_0
    invoke-static {p1}, Lme/embodied/services/Launcher$LauncherState;->valueOf(Ljava/lang/String;)Lme/embodied/services/Launcher$LauncherState;

    move-result-object v0

    .line 966
    sget-object v2, Lme/embodied/services/Launcher$LauncherState;->STATE_LIGHT_SLEEP:Lme/embodied/services/Launcher$LauncherState;

    if-ne v0, v2, :cond_3

    .line 967
    invoke-virtual {p0}, Lme/embodied/services/Launcher;->SupportsLightSleep()Z

    move-result v2

    if-eqz v2, :cond_2

    .line 968
    invoke-virtual {p0}, Lme/embodied/services/Launcher;->OnStayAwake()V

    goto :goto_0

    .line 970
    :cond_2
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Unit test unable to transition to unsupported state: "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 973
    :cond_3
    :goto_0
    invoke-direct {p0, v0}, Lme/embodied/services/Launcher;->RequestState(Lme/embodied/services/Launcher$LauncherState;)V
    :try_end_0
    .catch Ljava/lang/IllegalArgumentException; {:try_start_0 .. :try_end_0} :catch_0

    .line 976
    goto :goto_1

    .line 974
    :catch_0
    move-exception v0

    .line 975
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Unit test unable to find state for: "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v1, p1}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 977
    :goto_1
    goto/16 :goto_8

    :cond_4
    const-string v3, "xmos"

    invoke-virtual {p1, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_5

    .line 978
    const-string p1, "XMOS Reset requested by intent."

    invoke-static {v1, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 979
    invoke-static {}, Lme/embodied/services/Launcher;->RequestResetXMOS()V

    goto/16 :goto_8

    .line 980
    :cond_5
    const-string v3, "xmos_wipe"

    invoke-virtual {p1, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_6

    .line 981
    const-string p1, "Removing XMOS version SharedPreferences"

    invoke-static {v1, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 982
    iget-object p1, p0, Lme/embodied/services/Launcher;->context_:Landroid/content/Context;

    const-string v1, "com.embodied.bo_unity_preferences"

    invoke-virtual {p1, v1, v0}, Landroid/content/Context;->getSharedPreferences(Ljava/lang/String;I)Landroid/content/SharedPreferences;

    move-result-object p1

    .line 984
    invoke-interface {p1}, Landroid/content/SharedPreferences;->edit()Landroid/content/SharedPreferences$Editor;

    move-result-object p1

    .line 985
    const-string v0, "xmos_version"

    invoke-interface {p1, v0}, Landroid/content/SharedPreferences$Editor;->remove(Ljava/lang/String;)Landroid/content/SharedPreferences$Editor;

    .line 986
    invoke-interface {p1}, Landroid/content/SharedPreferences$Editor;->apply()V

    .line 987
    goto/16 :goto_8

    :cond_6
    const-string v3, "animon"

    invoke-virtual {p1, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_7

    .line 988
    iget-object p1, p0, Lme/embodied/services/Launcher;->animation_:Lme/embodied/TransitionAnimation;

    invoke-virtual {p1, v5}, Lme/embodied/TransitionAnimation;->setVisible(Z)V

    goto/16 :goto_8

    .line 989
    :cond_7
    const-string v3, "animoff"

    invoke-virtual {p1, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_8

    .line 990
    iget-object p1, p0, Lme/embodied/services/Launcher;->animation_:Lme/embodied/TransitionAnimation;

    invoke-virtual {p1, v0}, Lme/embodied/TransitionAnimation;->setVisible(Z)V

    goto/16 :goto_8

    .line 991
    :cond_8
    const-string v0, "demo-mode"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_9

    .line 992
    invoke-static {}, Lme/embodied/services/Launcher;->SendDemoSequence()V

    goto/16 :goto_8

    .line 993
    :cond_9
    const-string v0, "alarmon"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    const-wide/16 v3, 0x7530

    if-eqz v0, :cond_a

    .line 995
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    add-long v6, v0, v3

    const/16 v8, 0x64

    const-wide/16 v9, 0x0

    move-object v5, p0

    invoke-virtual/range {v5 .. v10}, Lme/embodied/services/Launcher;->OnUserAlarmRequest(JIJ)V

    goto/16 :goto_8

    .line 996
    :cond_a
    const-string v0, "alarmoff"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_b

    .line 998
    const-wide/16 v7, 0x0

    const/16 v9, 0x64

    const-wide/16 v10, 0x0

    move-object v6, p0

    invoke-virtual/range {v6 .. v11}, Lme/embodied/services/Launcher;->OnUserAlarmRequest(JIJ)V

    goto/16 :goto_8

    .line 999
    :cond_b
    const-string v0, "alarm2on"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_c

    .line 1001
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    add-long v6, v0, v3

    const/16 v8, 0x64

    const-wide/16 v9, 0x7530

    move-object v5, p0

    invoke-virtual/range {v5 .. v10}, Lme/embodied/services/Launcher;->OnUserAlarmRequest(JIJ)V

    goto/16 :goto_8

    .line 1002
    :cond_c
    const-string v0, "alarm2off"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_d

    .line 1004
    const-wide/16 v7, 0x0

    const/16 v9, 0x64

    const-wide/16 v10, 0x0

    move-object v6, p0

    invoke-virtual/range {v6 .. v11}, Lme/embodied/services/Launcher;->OnUserAlarmRequest(JIJ)V

    goto/16 :goto_8

    .line 1005
    :cond_d
    const-string v0, "unpair"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_e

    .line 1006
    const-string p1, "Signalling unpair ready by debug intent."

    invoke-static {v1, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1007
    invoke-virtual {p0}, Lme/embodied/services/Launcher;->OnUnpairUserReady()V

    goto/16 :goto_8

    .line 1008
    :cond_e
    const-string v0, "quiet-boot-done"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_f

    .line 1009
    invoke-static {}, Lme/embodied/services/Launcher;->SilentBootComplete()V

    goto/16 :goto_8

    .line 1010
    :cond_f
    const-string v0, "brick"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_10

    .line 1011
    invoke-static {}, Lme/embodied/services/Launcher;->BroadcastBrickedState()V

    goto/16 :goto_8

    .line 1012
    :cond_10
    const-string v0, "audiodump"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_11

    .line 1013
    invoke-direct {p0}, Lme/embodied/services/Launcher;->DoAudioDeviceDump()V

    goto/16 :goto_8

    .line 1014
    :cond_11
    const-string v0, "addnet"

    invoke-virtual {p1, v0}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_13

    .line 1015
    const-string v0, ":"

    invoke-virtual {p1, v0}, Ljava/lang/String;->split(Ljava/lang/String;)[Ljava/lang/String;

    move-result-object p1

    .line 1016
    array-length v0, p1

    const/4 v3, 0x3

    if-lt v0, v3, :cond_12

    .line 1017
    iget-object v6, p0, Lme/embodied/services/Launcher;->context_:Landroid/content/Context;

    aget-object v7, p1, v5

    aget-object v8, p1, v2

    const/4 v9, 0x0

    const/4 v10, 0x0

    const/4 v11, 0x1

    invoke-static/range {v6 .. v11}, Lme/embodied/NetworkUtil;->addWifiNetwork(Landroid/content/Context;Ljava/lang/String;Ljava/lang/String;IZZ)Lme/embodied/NetworkUtil$WifiResult;

    goto :goto_2

    .line 1019
    :cond_12
    const-string p1, "Not enough fields to add a network"

    invoke-static {v1, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1021
    :goto_2
    goto/16 :goto_8

    :cond_13
    const-string v0, "clearnet"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_14

    .line 1022
    iget-object p1, p0, Lme/embodied/services/Launcher;->context_:Landroid/content/Context;

    invoke-static {p1}, Lme/embodied/NetworkUtil;->removeAllNetworks(Landroid/content/Context;)Z

    goto/16 :goto_8

    .line 1023
    :cond_14
    const-string v0, "assetload="

    invoke-virtual {p1, v0}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_15

    .line 1024
    const/16 v0, 0xa

    invoke-virtual {p1, v0}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object p1

    .line 1025
    invoke-static {p1}, Lme/embodied/services/Launcher;->RequestAssetLoad(Ljava/lang/String;)V

    .line 1026
    goto/16 :goto_8

    :cond_15
    const-string v0, "assetreload="

    invoke-virtual {p1, v0}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_16

    .line 1027
    const/16 v0, 0xc

    invoke-virtual {p1, v0}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object p1

    .line 1028
    invoke-static {p1}, Lme/embodied/services/Launcher;->RequestAssetReload(Ljava/lang/String;)V

    .line 1029
    goto/16 :goto_8

    :cond_16
    const-string v0, "assetrelease="

    invoke-virtual {p1, v0}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_17

    .line 1030
    const/16 v0, 0xd

    invoke-virtual {p1, v0}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object p1

    .line 1031
    invoke-static {p1}, Lme/embodied/services/Launcher;->RequestAssetRelease(Ljava/lang/String;)V

    goto :goto_3

    .line 1032
    :cond_17
    const-string v0, "assetscan"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_18

    .line 1033
    invoke-static {}, Lme/embodied/services/Launcher;->RequestAssetScan()V

    goto/16 :goto_8

    .line 1032
    :cond_18
    :goto_3
    goto/16 :goto_8

    .line 923
    :cond_19
    :goto_4
    invoke-virtual {p1, v5}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object p1

    .line 925
    :try_start_1
    const-string v2, "all"

    invoke-virtual {p1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_1d

    .line 927
    if-ne v3, v4, :cond_1a

    .line 930
    const-class v0, Lme/embodied/services/BoDispatch;

    invoke-direct {p0, v0}, Lme/embodied/services/Launcher;->StartComponent(Ljava/lang/Class;)Z

    .line 931
    const-class v0, Lme/embodied/services/BoLogger;

    invoke-direct {p0, v0}, Lme/embodied/services/Launcher;->StartComponent(Ljava/lang/Class;)Z

    .line 932
    const-class v0, Lme/embodied/services/BoSystemMonitor;

    invoke-direct {p0, v0}, Lme/embodied/services/Launcher;->StartComponent(Ljava/lang/Class;)Z

    .line 933
    const-class v0, Lme/embodied/services/BoBrain;

    invoke-direct {p0, v0}, Lme/embodied/services/Launcher;->StartComponent(Ljava/lang/Class;)Z

    .line 934
    const-class v0, Lme/embodied/services/BoAudio;

    invoke-direct {p0, v0}, Lme/embodied/services/Launcher;->StartComponent(Ljava/lang/Class;)Z

    .line 935
    const-class v0, Lme/embodied/services/BoVision;

    invoke-direct {p0, v0}, Lme/embodied/services/Launcher;->StartComponent(Ljava/lang/Class;)Z

    .line 936
    const-class v0, Lme/embodied/services/BoFusion;

    invoke-direct {p0, v0}, Lme/embodied/services/Launcher;->StartComponent(Ljava/lang/Class;)Z

    .line 937
    const-class v0, Lme/embodied/services/BoUpdater;

    invoke-direct {p0, v0}, Lme/embodied/services/Launcher;->StartComponent(Ljava/lang/Class;)Z

    goto/16 :goto_6

    .line 940
    :cond_1a
    iget-object v2, p0, Lme/embodied/services/Launcher;->components_:Ljava/util/Vector;

    new-array v3, v0, [Landroid/content/ComponentName;

    invoke-virtual {v2, v3}, Ljava/util/Vector;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v2

    check-cast v2, [Landroid/content/ComponentName;

    .line 941
    array-length v3, v2

    :goto_5
    if-ge v0, v3, :cond_1c

    aget-object v4, v2, v0

    .line 943
    const-class v5, Landroid/app/Activity;

    invoke-virtual {v4}, Landroid/content/ComponentName;->getClassName()Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Ljava/lang/Class;->forName(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object v6

    invoke-virtual {v5, v6}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    move-result v5

    if-nez v5, :cond_1b

    .line 944
    invoke-direct {p0, v4}, Lme/embodied/services/Launcher;->StopComponent(Landroid/content/ComponentName;)Z

    .line 941
    :cond_1b
    add-int/lit8 v0, v0, 0x1

    goto :goto_5

    .line 946
    :cond_1c
    goto :goto_6

    .line 949
    :cond_1d
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "me.embodied.services."

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Ljava/lang/Class;->forName(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object v0

    .line 950
    if-ne v3, v4, :cond_1e

    .line 951
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Unit test (re)starting "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 952
    invoke-direct {p0, v0}, Lme/embodied/services/Launcher;->StartComponent(Ljava/lang/Class;)Z

    goto :goto_6

    .line 955
    :cond_1e
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Unit test stopping "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 956
    invoke-direct {p0, v0}, Lme/embodied/services/Launcher;->StopComponent(Ljava/lang/Class;)Z
    :try_end_1
    .catch Ljava/lang/ClassNotFoundException; {:try_start_1 .. :try_end_1} :catch_1

    .line 961
    :goto_6
    goto :goto_7

    .line 959
    :catch_1
    move-exception v0

    .line 960
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Unit test unable to class for: "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v1, p1, v0}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)I

    .line 962
    :goto_7
    nop

    .line 1035
    :goto_8
    return-void

    .line 919
    :cond_1f
    :goto_9
    return-void
.end method

.method cancelDelayedUnpairing()V
    .locals 1

    .line 1275
    iget-object v0, p0, Lme/embodied/services/Launcher;->unpairing_delay_timer_:Ljava/util/Timer;

    if-eqz v0, :cond_0

    .line 1276
    invoke-virtual {v0}, Ljava/util/Timer;->cancel()V

    .line 1277
    const/4 v0, 0x0

    iput-object v0, p0, Lme/embodied/services/Launcher;->unpairing_delay_timer_:Ljava/util/Timer;

    .line 1279
    :cond_0
    return-void
.end method

.method isOTAActive()Z
    .locals 4

    .line 1744
    iget-wide v0, p0, Lme/embodied/services/Launcher;->last_ota_signal_:J

    const-wide/16 v2, 0x0

    cmp-long v0, v0, v2

    if-lez v0, :cond_0

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    iget-wide v2, p0, Lme/embodied/services/Launcher;->last_ota_signal_:J

    sub-long/2addr v0, v2

    const-wide/32 v2, 0x493e0

    cmp-long v0, v0, v2

    if-gez v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method notifyPendingAlarms()V
    .locals 3

    .line 540
    iget-object v0, p0, Lme/embodied/services/Launcher;->pending_user_alarms_:Ljava/util/ArrayList;

    monitor-enter v0

    .line 541
    :try_start_0
    iget-object v1, p0, Lme/embodied/services/Launcher;->pending_user_alarms_:Ljava/util/ArrayList;

    invoke-virtual {v1}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/Integer;

    invoke-virtual {v2}, Ljava/lang/Integer;->intValue()I

    move-result v2

    .line 542
    invoke-static {v2}, Lme/embodied/services/Launcher;->SignalUserAlarm(I)V

    .line 543
    goto :goto_0

    .line 544
    :cond_0
    iget-object v1, p0, Lme/embodied/services/Launcher;->pending_user_alarms_:Ljava/util/ArrayList;

    invoke-virtual {v1}, Ljava/util/ArrayList;->clear()V

    .line 545
    monitor-exit v0

    .line 546
    return-void

    .line 545
    :catchall_0
    move-exception v1

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw v1
.end method

.method queuePendingAlarm(I)V
    .locals 2

    .line 531
    iget-object v0, p0, Lme/embodied/services/Launcher;->pending_user_alarms_:Ljava/util/ArrayList;

    monitor-enter v0

    .line 532
    :try_start_0
    iget-object v1, p0, Lme/embodied/services/Launcher;->pending_user_alarms_:Ljava/util/ArrayList;

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-virtual {v1, p1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 533
    monitor-exit v0

    .line 534
    return-void

    .line 533
    :catchall_0
    move-exception p1

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw p1
.end method

.method setAlarmWakeup(Z)V
    .locals 8

    .line 1132
    iget-object v0, p0, Lme/embodied/services/Launcher;->context_:Landroid/content/Context;

    const-string v1, "alarm"

    invoke-virtual {v0, v1}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    move-object v1, v0

    check-cast v1, Landroid/app/AlarmManager;

    .line 1133
    const-string v0, "bo-launcher-j"

    if-eqz p1, :cond_0

    .line 1134
    const-string p1, "ALARM - Wake me up in 15 minutes please!"

    invoke-static {v0, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1135
    const/4 v2, 0x2

    .line 1136
    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v3

    const-wide/32 v5, 0xdbba0

    add-long/2addr v3, v5

    iget-object v7, p0, Lme/embodied/services/Launcher;->alarm_wakeup_pending_:Landroid/app/PendingIntent;

    .line 1135
    invoke-virtual/range {v1 .. v7}, Landroid/app/AlarmManager;->setInexactRepeating(IJJLandroid/app/PendingIntent;)V

    goto :goto_0

    .line 1142
    :cond_0
    const-string p1, "ALARM - I am awake, thanks!"

    invoke-static {v0, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1143
    iget-object p1, p0, Lme/embodied/services/Launcher;->alarm_wakeup_pending_:Landroid/app/PendingIntent;

    invoke-virtual {v1, p1}, Landroid/app/AlarmManager;->cancel(Landroid/app/PendingIntent;)V

    .line 1145
    :goto_0
    return-void
.end method

.method setAnimationState(Z)V
    .locals 2

    .line 495
    new-instance v0, Landroid/os/Handler;

    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object v1

    invoke-direct {v0, v1}, Landroid/os/Handler;-><init>(Landroid/os/Looper;)V

    new-instance v1, Lme/embodied/services/Launcher$1;

    invoke-direct {v1, p0, p1}, Lme/embodied/services/Launcher$1;-><init>(Lme/embodied/services/Launcher;Z)V

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    .line 500
    return-void
.end method

.method startDelayedDisengage()V
    .locals 4

    .line 1286
    iget-object v0, p0, Lme/embodied/services/Launcher;->unpairing_delay_timer_:Ljava/util/Timer;

    if-eqz v0, :cond_0

    .line 1287
    const-string v0, "bo-launcher-j"

    const-string v1, "Unpairing delay timer is already running.  Waiting response."

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 1288
    return-void

    .line 1290
    :cond_0
    new-instance v0, Ljava/util/Timer;

    invoke-direct {v0}, Ljava/util/Timer;-><init>()V

    iput-object v0, p0, Lme/embodied/services/Launcher;->unpairing_delay_timer_:Ljava/util/Timer;

    .line 1291
    new-instance v1, Lme/embodied/services/Launcher$5;

    invoke-direct {v1, p0}, Lme/embodied/services/Launcher$5;-><init>(Lme/embodied/services/Launcher;)V

    const-wide/16 v2, 0x4e20

    invoke-virtual {v0, v1, v2, v3}, Ljava/util/Timer;->schedule(Ljava/util/TimerTask;J)V

    .line 1306
    const/16 v0, 0x14

    iget-object v1, p0, Lme/embodied/services/Launcher;->config_state_agency_:Lme/embodied/services/Launcher$DisengageReason;

    invoke-virtual {v1}, Lme/embodied/services/Launcher$DisengageReason;->ordinal()I

    move-result v1

    invoke-static {v0, v1}, Lme/embodied/services/Launcher;->RequestUserDisengage(II)V

    .line 1307
    sget-object v0, Lme/embodied/services/Launcher$DisengageReason;->UNPAIRING:Lme/embodied/services/Launcher$DisengageReason;

    iput-object v0, p0, Lme/embodied/services/Launcher;->config_state_agency_:Lme/embodied/services/Launcher$DisengageReason;

    .line 1308
    return-void
.end method
