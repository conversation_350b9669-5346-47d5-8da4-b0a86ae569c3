.class public Lme/embodied/NTPService;
.super Ljava/lang/Object;
.source "NTPService.java"


# static fields
.field private static final NPT_WARN_COUNT:I = 0xa

.field private static final NTP_DEFAULT_SERVERS:Ljava/lang/String; = "time.android.com,pool.ntp.org,time.nist.gov"

.field private static final NTP_DELAY_TIME:J = 0x3e8L

.field private static final NTP_PACKET_TIME:I = 0x1388

.field private static final NTP_REFRESH_TIME:J = 0x2932e00L

.field private static final NTP_RETRY_TIME:J = 0x3e8L

.field private static final TAG:Ljava/lang/String; = "bo-ntp-service"


# instance fields
.field context_:Landroid/content/Context;

.field ntp_commit_time_:J

.field ntp_thread_:Ljava/lang/Thread;


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 2

    .line 30
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 31
    const-wide/16 v0, 0x0

    iput-wide v0, p0, Lme/embodied/NTPService;->ntp_commit_time_:J

    .line 32
    iput-object p1, p0, Lme/embodied/NTPService;->context_:Landroid/content/Context;

    .line 33
    return-void
.end method

.method static synthetic access$000(Lme/embodied/NTPService;)V
    .locals 0

    .line 15
    invoke-direct {p0}, Lme/embodied/NTPService;->ntp_service_proc()V

    return-void
.end method

.method private ntp_service_proc()V
    .locals 10

    .line 76
    const-string v0, "bo-ntp-service"

    :try_start_0
    new-instance v1, Lme/embodied/AndroidSystemProps;

    iget-object v2, p0, Lme/embodied/NTPService;->context_:Landroid/content/Context;

    invoke-direct {v1, v2}, Lme/embodied/AndroidSystemProps;-><init>(Landroid/content/Context;)V

    .line 77
    const-string v2, "sys.embodied.ntp_servers"

    const-string v3, "time.android.com,pool.ntp.org,time.nist.gov"

    invoke-virtual {v1, v2, v3}, Lme/embodied/AndroidSystemProps;->getProperty(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    .line 78
    const-string v2, ","

    invoke-virtual {v1, v2}, Ljava/lang/String;->split(Ljava/lang/String;)[Ljava/lang/String;

    move-result-object v2

    .line 79
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "Starting NTP service using "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    array-length v4, v2

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v4, " servers: "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 80
    const-wide/16 v3, 0x3e8

    invoke-static {v3, v4}, Ljava/lang/Thread;->sleep(J)V

    .line 81
    new-instance v1, Lme/embodied/SntpClient;

    invoke-direct {v1}, Lme/embodied/SntpClient;-><init>()V

    .line 83
    const/4 v5, 0x0

    move v6, v5

    .line 85
    :goto_0
    array-length v7, v2

    rem-int v7, v6, v7

    .line 86
    new-instance v8, Ljava/lang/StringBuilder;

    invoke-direct {v8}, Ljava/lang/StringBuilder;-><init>()V

    const-string v9, "Checking NTP time, attempt "

    invoke-virtual {v8, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v8, v6}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v8}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v8

    invoke-static {v0, v8}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 87
    aget-object v8, v2, v7

    invoke-virtual {v8}, Ljava/lang/String;->trim()Ljava/lang/String;

    move-result-object v8

    const/16 v9, 0x1388

    invoke-virtual {v1, v8, v9}, Lme/embodied/SntpClient;->requestTime(Ljava/lang/String;I)Z

    move-result v8

    if-eqz v8, :cond_0

    .line 88
    invoke-virtual {v1}, Lme/embodied/SntpClient;->getNtpTime()J

    move-result-wide v3

    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v5

    add-long/2addr v3, v5

    .line 89
    invoke-virtual {v1}, Lme/embodied/SntpClient;->getNtpTimeReference()J

    move-result-wide v5

    sub-long/2addr v3, v5

    iput-wide v3, p0, Lme/embodied/NTPService;->ntp_commit_time_:J

    .line 90
    new-instance v1, Ljava/util/Date;

    iget-wide v3, p0, Lme/embodied/NTPService;->ntp_commit_time_:J

    invoke-direct {v1, v3, v4}, Ljava/util/Date;-><init>(J)V

    .line 91
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "NTP time received from "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    aget-object v2, v2, v7

    invoke-virtual {v2}, Ljava/lang/String;->trim()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v2, ", current net time is: "

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I
    :try_end_0
    .catch Ljava/lang/InterruptedException; {:try_start_0 .. :try_end_0} :catch_0

    .line 94
    :try_start_1
    iget-object v1, p0, Lme/embodied/NTPService;->context_:Landroid/content/Context;

    const-string v2, "alarm"

    invoke-virtual {v1, v2}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroid/app/AlarmManager;

    .line 95
    iget-wide v2, p0, Lme/embodied/NTPService;->ntp_commit_time_:J

    invoke-virtual {v1, v2, v3}, Landroid/app/AlarmManager;->setTime(J)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 98
    goto :goto_1

    .line 96
    :catchall_0
    move-exception v1

    .line 97
    :try_start_2
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Failed to set RTC: "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 99
    nop

    .line 109
    :goto_1
    goto :goto_2

    .line 101
    :cond_0
    const/16 v7, 0xa

    if-ne v6, v7, :cond_1

    .line 102
    const-string v7, "BO#8013 Unable to reach any NTP servers after %d attempts."

    const/4 v8, 0x1

    new-array v8, v8, [Ljava/lang/Object;

    invoke-static {v6}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v9

    aput-object v9, v8, v5

    invoke-static {v7, v8}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v7

    invoke-static {v0, v7}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 104
    :cond_1
    add-int/lit8 v6, v6, 0x1

    .line 105
    invoke-static {v3, v4}, Ljava/lang/Thread;->sleep(J)V
    :try_end_2
    .catch Ljava/lang/InterruptedException; {:try_start_2 .. :try_end_2} :catch_0

    .line 106
    goto/16 :goto_0

    .line 107
    :catch_0
    move-exception v1

    .line 108
    const-string v1, "NTP service interrupted."

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 110
    :goto_2
    return-void
.end method


# virtual methods
.method public declared-synchronized onNetworkConnect()V
    .locals 4

    monitor-enter p0

    .line 39
    :try_start_0
    iget-wide v0, p0, Lme/embodied/NTPService;->ntp_commit_time_:J

    const-wide/16 v2, 0x0

    cmp-long v0, v0, v2

    if-eqz v0, :cond_0

    .line 42
    iget-wide v0, p0, Lme/embodied/NTPService;->ntp_commit_time_:J

    const-wide/32 v2, 0x2932e00

    add-long/2addr v0, v2

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v2

    cmp-long v0, v0, v2

    if-lez v0, :cond_0

    .line 43
    const-string v0, "bo-ntp-service"

    const-string v1, "Network connected, network time is known and still fresh."

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 44
    monitor-exit p0

    return-void

    .line 49
    :cond_0
    :try_start_1
    iget-object v0, p0, Lme/embodied/NTPService;->ntp_thread_:Ljava/lang/Thread;

    if-eqz v0, :cond_1

    .line 50
    iget-object v0, p0, Lme/embodied/NTPService;->ntp_thread_:Ljava/lang/Thread;

    invoke-virtual {v0}, Ljava/lang/Thread;->interrupt()V

    .line 52
    :cond_1
    new-instance v0, Lme/embodied/NTPService$1;

    invoke-direct {v0, p0}, Lme/embodied/NTPService$1;-><init>(Lme/embodied/NTPService;)V

    iput-object v0, p0, Lme/embodied/NTPService;->ntp_thread_:Ljava/lang/Thread;

    .line 57
    invoke-virtual {v0}, Ljava/lang/Thread;->start()V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 58
    monitor-exit p0

    return-void

    .line 38
    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method public declared-synchronized onNetworkDisconnect()V
    .locals 1

    monitor-enter p0

    .line 64
    :try_start_0
    iget-object v0, p0, Lme/embodied/NTPService;->ntp_thread_:Ljava/lang/Thread;

    if-eqz v0, :cond_0

    .line 65
    iget-object v0, p0, Lme/embodied/NTPService;->ntp_thread_:Ljava/lang/Thread;

    invoke-virtual {v0}, Ljava/lang/Thread;->interrupt()V

    .line 66
    const/4 v0, 0x0

    iput-object v0, p0, Lme/embodied/NTPService;->ntp_thread_:Ljava/lang/Thread;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 68
    :cond_0
    monitor-exit p0

    return-void

    .line 63
    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method
