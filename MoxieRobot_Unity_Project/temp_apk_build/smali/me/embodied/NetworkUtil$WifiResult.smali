.class Lme/embodied/NetworkUtil$WifiResult;
.super Ljava/lang/Object;
.source "NetworkUtil.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lme/embodied/NetworkUtil;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "WifiResult"
.end annotation


# static fields
.field public static final RESULT_ADD_FAIL:I = 0x2

.field public static final RESULT_ENABLE_FAIL:I = 0x3

.field public static final RESULT_OK:I = 0x0

.field public static final RESULT_RECONNECT_FAIL:I = 0x4

.field public static final SSID_APPROXIMATE:I = 0x1

.field public static final SSID_EXACT:I = 0x0

.field public static final SSID_MISSING:I = 0x2


# instance fields
.field public bias_:F

.field public found_:I

.field public result_:I

.field public ssid_:Ljava/lang/String;


# direct methods
.method public constructor <init>(Ljava/lang/String;IF)V
    .locals 0

    .line 132
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 133
    iput-object p1, p0, Lme/embodied/NetworkUtil$WifiResult;->ssid_:Ljava/lang/String;

    iput p2, p0, Lme/embodied/NetworkUtil$WifiResult;->found_:I

    iput p3, p0, Lme/embodied/NetworkUtil$WifiResult;->bias_:F

    .line 134
    return-void
.end method
