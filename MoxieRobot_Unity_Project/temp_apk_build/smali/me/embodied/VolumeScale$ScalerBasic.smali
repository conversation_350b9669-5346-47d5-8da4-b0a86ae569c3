.class Lme/embodied/VolumeScale$ScalerBasic;
.super Ljava/lang/Object;
.source "VolumeScale.java"

# interfaces
.implements Lme/embodied/VolumeScale$Scaler;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lme/embodied/VolumeScale;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "ScalerBasic"
.end annotation


# instance fields
.field last_volume_percent_:I


# direct methods
.method private constructor <init>()V
    .locals 1

    .line 65
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 66
    const/4 v0, -0x1

    iput v0, p0, Lme/embodied/VolumeScale$ScalerBasic;->last_volume_percent_:I

    return-void
.end method

.method synthetic constructor <init>(Lme/embodied/VolumeScale$1;)V
    .locals 0

    .line 65
    invoke-direct {p0}, Lme/embodied/VolumeScale$ScalerBasic;-><init>()V

    return-void
.end method


# virtual methods
.method public final percentToSystem(I)I
    .locals 2

    .line 69
    const/4 v0, 0x0

    const/16 v1, 0x64

    invoke-static {p1, v0, v1}, Lme/embodied/VolumeScale;->access$300(III)I

    move-result p1

    .line 70
    iput p1, p0, Lme/embodied/VolumeScale$ScalerBasic;->last_volume_percent_:I

    .line 71
    if-nez p1, :cond_0

    .line 72
    return v0

    .line 74
    :cond_0
    invoke-virtual {p0, p1}, Lme/embodied/VolumeScale$ScalerBasic;->scaleToSystem(I)I

    move-result p1

    return p1
.end method

.method public scaleToPercent(I)I
    .locals 2

    .line 97
    sget v0, Lme/embodied/VolumeScale;->VOLUME_FLOOR:I

    sub-int/2addr p1, v0

    int-to-float p1, p1

    sget v0, Lme/embodied/VolumeScale;->VOLUME_CEILING:I

    sget v1, Lme/embodied/VolumeScale;->VOLUME_FLOOR:I

    sub-int/2addr v0, v1

    int-to-float v0, v0

    div-float/2addr p1, v0

    const/high16 v0, 0x42c80000    # 100.0f

    mul-float/2addr p1, v0

    invoke-static {p1}, Ljava/lang/Math;->round(F)I

    move-result p1

    return p1
.end method

.method public scaleToSystem(I)I
    .locals 3

    .line 92
    int-to-float p1, p1

    const/high16 v0, 0x42c80000    # 100.0f

    div-float/2addr p1, v0

    .line 93
    sget v0, Lme/embodied/VolumeScale;->VOLUME_FLOOR:I

    sget v1, Lme/embodied/VolumeScale;->VOLUME_CEILING:I

    sget v2, Lme/embodied/VolumeScale;->VOLUME_FLOOR:I

    sub-int/2addr v1, v2

    int-to-float v1, v1

    mul-float/2addr v1, p1

    invoke-static {v1}, Ljava/lang/Math;->round(F)I

    move-result p1

    add-int/2addr v0, p1

    return v0
.end method

.method public final systemToPercent(I)I
    .locals 2

    .line 79
    if-nez p1, :cond_0

    .line 80
    const/4 p1, 0x0

    iput p1, p0, Lme/embodied/VolumeScale$ScalerBasic;->last_volume_percent_:I

    .line 81
    return p1

    .line 83
    :cond_0
    sget v0, Lme/embodied/VolumeScale;->VOLUME_FLOOR:I

    sget v1, Lme/embodied/VolumeScale;->VOLUME_CEILING:I

    invoke-static {p1, v0, v1}, Lme/embodied/VolumeScale;->access$300(III)I

    move-result p1

    .line 85
    iget v0, p0, Lme/embodied/VolumeScale$ScalerBasic;->last_volume_percent_:I

    if-lez v0, :cond_1

    invoke-virtual {p0, v0}, Lme/embodied/VolumeScale$ScalerBasic;->scaleToSystem(I)I

    move-result v0

    if-eq v0, p1, :cond_2

    .line 86
    :cond_1
    invoke-virtual {p0, p1}, Lme/embodied/VolumeScale$ScalerBasic;->scaleToPercent(I)I

    move-result p1

    iput p1, p0, Lme/embodied/VolumeScale$ScalerBasic;->last_volume_percent_:I

    .line 88
    :cond_2
    iget p1, p0, Lme/embodied/VolumeScale$ScalerBasic;->last_volume_percent_:I

    return p1
.end method
