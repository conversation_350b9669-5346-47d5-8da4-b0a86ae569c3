.class Lme/embodied/VolumeScale$ScalerKnee25;
.super Lme/embodied/VolumeScale$ScalerBasic;
.source "VolumeScale.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lme/embodied/VolumeScale;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "ScalerKnee25"
.end annotation


# static fields
.field private static final KNEE_PERCENT:F = 0.25f

.field private static final VOLUME_KNEEPOINT:I = 0x7


# direct methods
.method private constructor <init>()V
    .locals 1

    .line 116
    const/4 v0, 0x0

    invoke-direct {p0, v0}, Lme/embodied/VolumeScale$ScalerBasic;-><init>(Lme/embodied/VolumeScale$1;)V

    return-void
.end method

.method synthetic constructor <init>(Lme/embodied/VolumeScale$1;)V
    .locals 0

    .line 116
    invoke-direct {p0}, Lme/embodied/VolumeScale$ScalerKnee25;-><init>()V

    return-void
.end method


# virtual methods
.method public scaleToPercent(I)I
    .locals 4

    .line 139
    const/high16 v0, 0x42c80000    # 100.0f

    const/4 v1, 0x1

    const/4 v2, 0x7

    if-lt p1, v2, :cond_0

    .line 141
    sub-int/2addr p1, v2

    add-int/2addr p1, v1

    int-to-float p1, p1

    sget v3, Lme/embodied/VolumeScale;->VOLUME_CEILING:I

    sub-int/2addr v3, v2

    add-int/2addr v3, v1

    int-to-float v2, v3

    div-float/2addr p1, v2

    const/high16 v2, 0x3f400000    # 0.75f

    mul-float/2addr p1, v2

    mul-float/2addr p1, v0

    const/high16 v0, 0x41c80000    # 25.0f

    add-float/2addr p1, v0

    float-to-int p1, p1

    invoke-static {v1, p1}, Ljava/lang/Math;->max(II)I

    move-result p1

    return p1

    .line 144
    :cond_0
    sget v3, Lme/embodied/VolumeScale;->VOLUME_FLOOR:I

    sub-int/2addr p1, v3

    int-to-float p1, p1

    sget v3, Lme/embodied/VolumeScale;->VOLUME_FLOOR:I

    sub-int/2addr v2, v3

    int-to-float v2, v2

    div-float/2addr p1, v2

    const/high16 v2, 0x3e800000    # 0.25f

    mul-float/2addr p1, v2

    mul-float/2addr p1, v0

    float-to-int p1, p1

    invoke-static {v1, p1}, Ljava/lang/Math;->max(II)I

    move-result p1

    return p1
.end method

.method public scaleToSystem(I)I
    .locals 2

    .line 122
    const/4 v0, 0x0

    const/16 v1, 0x64

    invoke-static {p1, v0, v1}, Lme/embodied/VolumeScale;->access$300(III)I

    move-result p1

    int-to-float p1, p1

    const/high16 v0, 0x42c80000    # 100.0f

    div-float/2addr p1, v0

    .line 123
    const/high16 v0, 0x3e800000    # 0.25f

    cmpl-float v1, p1, v0

    if-ltz v1, :cond_0

    .line 125
    sub-float/2addr p1, v0

    const/high16 v0, 0x3f400000    # 0.75f

    div-float/2addr p1, v0

    .line 126
    sget v0, Lme/embodied/VolumeScale;->VOLUME_CEILING:I

    add-int/lit8 v0, v0, -0x7

    add-int/lit8 v0, v0, 0x1

    int-to-float v0, v0

    mul-float/2addr v0, p1

    float-to-int p1, v0

    add-int/lit8 p1, p1, 0x7

    sget v0, Lme/embodied/VolumeScale;->VOLUME_FLOOR:I

    sget v1, Lme/embodied/VolumeScale;->VOLUME_CEILING:I

    invoke-static {p1, v0, v1}, Lme/embodied/VolumeScale;->access$300(III)I

    move-result p1

    .line 128
    return p1

    .line 131
    :cond_0
    const/high16 v0, 0x40800000    # 4.0f

    mul-float/2addr p1, v0

    .line 132
    sget v0, Lme/embodied/VolumeScale;->VOLUME_FLOOR:I

    sget v1, Lme/embodied/VolumeScale;->VOLUME_FLOOR:I

    rsub-int/lit8 v1, v1, 0x7

    int-to-float v1, v1

    mul-float/2addr v1, p1

    float-to-int p1, v1

    add-int/2addr v0, p1

    sget p1, Lme/embodied/VolumeScale;->VOLUME_FLOOR:I

    sget v1, Lme/embodied/VolumeScale;->VOLUME_CEILING:I

    invoke-static {v0, p1, v1}, Lme/embodied/VolumeScale;->access$300(III)I

    move-result p1

    .line 134
    return p1
.end method
