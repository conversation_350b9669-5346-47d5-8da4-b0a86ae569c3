.class public Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;
.super Ljava/lang/Object;
.source "fwUpdateLibEntry.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$RobotInfo;
    }
.end annotation


# static fields
.field static LOG_TAG:Ljava/lang/String;

.field static _progress:F


# instance fields
.field _numRecords:J

.field _sRecord:Ljava/io/InputStream;

.field _sRecordArray:[Ljava/lang/String;

.field mRecordChecksum:B

.field mRecordData:[B

.field mRecordLength:B

.field mRecordOffset:S

.field mRecordType:B

.field public robotInfo:Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$RobotInfo;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 25
    const/4 v0, 0x0

    sput v0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->_progress:F

    .line 28
    const-string v0, "Lizard_OTA"

    sput-object v0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->LOG_TAG:Ljava/lang/String;

    .line 32
    const-string v0, "native-lib"

    invoke-static {v0}, Ljava/lang/System;->loadLibrary(Ljava/lang/String;)V

    .line 33
    return-void
.end method

.method public constructor <init>()V
    .locals 2

    .line 53
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 19
    const/4 v0, 0x0

    iput-byte v0, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->mRecordLength:B

    .line 20
    iput-short v0, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->mRecordOffset:S

    .line 23
    iput-byte v0, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->mRecordChecksum:B

    .line 24
    const-wide/16 v0, 0x0

    iput-wide v0, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->_numRecords:J

    .line 54
    sget-object v0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->LOG_TAG:Ljava/lang/String;

    const-string v1, "Start firmware bootloader version GOBY"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 55
    invoke-direct {p0}, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->start()V

    .line 56
    new-instance v0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$RobotInfo;

    invoke-direct {v0, p0}, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$RobotInfo;-><init>(Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;)V

    iput-object v0, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->robotInfo:Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$RobotInfo;

    .line 57
    return-void
.end method

.method static synthetic access$000(Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;[BI)Z
    .locals 1
    .param p0, "x0"    # Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;
    .param p1, "x1"    # [B
    .param p2, "x2"    # I

    .line 16
    invoke-direct {p0, p1, p2}, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->sendBootLoaderPkt([BI)Z

    move-result v0

    return v0
.end method

.method static synthetic access$100(Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;)V
    .locals 0
    .param p0, "x0"    # Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;

    .line 16
    invoke-direct {p0}, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->resetRobotVersion()V

    return-void
.end method

.method static synthetic access$200(Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;)I
    .locals 1
    .param p0, "x0"    # Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;

    .line 16
    invoke-direct {p0}, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->getSystemInfo()I

    move-result v0

    return v0
.end method

.method private native closeUart()V
.end method

.method public static getProgress()I
    .locals 1

    .line 166
    sget v0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->_progress:F

    float-to-int v0, v0

    return v0
.end method

.method private native getSystemInfo()I
.end method

.method private native invalidateApp()Z
.end method

.method private native resetRobotVersion()V
.end method

.method private native sendBootLoaderPkt([BI)Z
.end method

.method private native start()V
.end method


# virtual methods
.method Sleep(I)V
    .locals 2
    .param p1, "milliSecs"    # I

    .line 319
    int-to-long v0, p1

    :try_start_0
    invoke-static {v0, v1}, Ljava/lang/Thread;->sleep(J)V
    :try_end_0
    .catch Ljava/lang/InterruptedException; {:try_start_0 .. :try_end_0} :catch_0

    .line 324
    goto :goto_0

    .line 321
    :catch_0
    move-exception v0

    .line 323
    .local v0, "e":Ljava/lang/InterruptedException;
    invoke-virtual {v0}, Ljava/lang/InterruptedException;->printStackTrace()V

    .line 325
    .end local v0    # "e":Ljava/lang/InterruptedException;
    :goto_0
    return-void
.end method

.method public close()V
    .locals 0

    .line 185
    invoke-direct {p0}, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->closeUart()V

    .line 186
    return-void
.end method

.method public downloadLizardApp([Ljava/lang/String;)V
    .locals 2
    .param p1, "record"    # [Ljava/lang/String;

    .line 81
    iput-object p1, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->_sRecordArray:[Ljava/lang/String;

    .line 83
    new-instance v0, Ljava/lang/Thread;

    new-instance v1, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$1;

    invoke-direct {v1, p0}, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$1;-><init>(Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;)V

    invoke-direct {v0, v1}, Ljava/lang/Thread;-><init>(Ljava/lang/Runnable;)V

    .line 149
    invoke-virtual {v0}, Ljava/lang/Thread;->start()V

    .line 151
    return-void
.end method

.method hexStringToByteArray(Ljava/lang/String;)[B
    .locals 8
    .param p1, "s"    # Ljava/lang/String;

    .line 266
    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result v0

    add-int/lit8 v0, v0, -0x1

    invoke-virtual {p1, v0}, Ljava/lang/String;->charAt(I)C

    move-result v0

    const/16 v1, 0xa

    if-ne v0, v1, :cond_0

    .line 267
    sget-object v0, Ljava/lang/System;->out:Ljava/io/PrintStream;

    const-string v1, "FW: removing new line"

    invoke-virtual {v0, v1}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 268
    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result v0

    add-int/lit8 v0, v0, -0x1

    .line 269
    .local v0, "len":I
    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result v1

    add-int/lit8 v1, v1, -0x1

    invoke-virtual {p1, v1}, Ljava/lang/String;->charAt(I)C

    move-result v1

    const/16 v2, 0xd

    if-ne v1, v2, :cond_1

    .line 270
    sget-object v1, Ljava/lang/System;->out:Ljava/io/PrintStream;

    const-string v2, "FW: removing carriage return"

    invoke-virtual {v1, v2}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 271
    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result v1

    add-int/lit8 v0, v1, -0x1

    goto :goto_0

    .line 276
    .end local v0    # "len":I
    :cond_0
    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result v0

    .line 281
    .restart local v0    # "len":I
    :cond_1
    :goto_0
    div-int/lit8 v1, v0, 0x2

    int-to-double v1, v1

    invoke-static {v1, v2}, Ljava/lang/Math;->floor(D)D

    move-result-wide v1

    double-to-int v1, v1

    .line 282
    .local v1, "numBytes":I
    div-int/lit8 v2, v0, 0x2

    new-array v2, v2, [B

    .line 283
    .local v2, "data":[B
    const/4 v3, 0x0

    .local v3, "i":I
    :goto_1
    array-length v4, v2

    mul-int/lit8 v4, v4, 0x2

    if-ge v3, v4, :cond_2

    .line 285
    div-int/lit8 v4, v3, 0x2

    invoke-virtual {p1, v3}, Ljava/lang/String;->charAt(I)C

    move-result v5

    const/16 v6, 0x10

    invoke-static {v5, v6}, Ljava/lang/Character;->digit(CI)I

    move-result v5

    shl-int/lit8 v5, v5, 0x4

    add-int/lit8 v7, v3, 0x1

    invoke-virtual {p1, v7}, Ljava/lang/String;->charAt(I)C

    move-result v7

    invoke-static {v7, v6}, Ljava/lang/Character;->digit(CI)I

    move-result v6

    add-int/2addr v5, v6

    int-to-byte v5, v5

    aput-byte v5, v2, v4

    .line 283
    add-int/lit8 v3, v3, 0x2

    goto :goto_1

    .line 287
    .end local v3    # "i":I
    :cond_2
    return-object v2
.end method

.method public invalidate()Z
    .locals 1

    .line 68
    iget-object v0, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->robotInfo:Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$RobotInfo;

    iget-boolean v0, v0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$RobotInfo;->release:Z

    if-nez v0, :cond_0

    .line 69
    const/4 v0, 0x0

    return v0

    .line 70
    :cond_0
    invoke-direct {p0}, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->invalidateApp()Z

    move-result v0

    return v0
.end method

.method parseRecord([B)V
    .locals 3
    .param p1, "dataRecord"    # [B

    .line 298
    const/4 v0, 0x0

    aget-byte v0, p1, v0

    iput-byte v0, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->mRecordLength:B

    .line 299
    const/4 v0, 0x1

    aget-byte v0, p1, v0

    shl-int/lit8 v0, v0, 0x8

    int-to-short v0, v0

    const/4 v1, 0x2

    aget-byte v1, p1, v1

    int-to-short v1, v1

    add-int/2addr v0, v1

    int-to-short v0, v0

    iput-short v0, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->mRecordOffset:S

    .line 300
    const/4 v0, 0x3

    aget-byte v0, p1, v0

    iput-byte v0, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->mRecordType:B

    .line 302
    array-length v0, p1

    add-int/lit8 v0, v0, -0x5

    new-array v0, v0, [B

    iput-object v0, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->mRecordData:[B

    .line 303
    const/4 v0, 0x0

    .local v0, "i":I
    :goto_0
    iget-object v1, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->mRecordData:[B

    array-length v2, v1

    if-ge v0, v2, :cond_0

    .line 305
    add-int/lit8 v2, v0, 0x4

    aget-byte v2, p1, v2

    aput-byte v2, v1, v0

    .line 303
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    .line 307
    .end local v0    # "i":I
    :cond_0
    array-length v0, v1

    add-int/lit8 v0, v0, 0x4

    aget-byte v0, p1, v0

    iput-byte v0, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->mRecordChecksum:B

    .line 308
    return-void
.end method

.method public restart()V
    .locals 0

    .line 175
    invoke-direct {p0}, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->start()V

    .line 176
    return-void
.end method
