.class Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$1;
.super Ljava/lang/Object;
.source "fwUpdateLibEntry.java"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->downloadLizardApp([Ljava/lang/String;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;


# direct methods
.method constructor <init>(Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;)V
    .locals 0
    .param p1, "this$0"    # Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;

    .line 84
    iput-object p1, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$1;->this$0:Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 9

    .line 89
    iget-object v0, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$1;->this$0:Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;

    iget-object v1, v0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->_sRecordArray:[Ljava/lang/String;

    array-length v1, v1

    int-to-long v1, v1

    iput-wide v1, v0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->_numRecords:J

    .line 91
    const/4 v0, 0x0

    .line 93
    .local v0, "sentRecords":I
    const/4 v1, 0x0

    .line 96
    .local v1, "line":Ljava/lang/String;
    const/4 v2, 0x1

    add-int/2addr v0, v2

    .line 97
    const/4 v3, 0x1

    .line 98
    .local v3, "recIdx":I
    :goto_0
    int-to-long v4, v0

    iget-object v6, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$1;->this$0:Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;

    iget-wide v6, v6, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->_numRecords:J

    cmp-long v4, v4, v6

    if-gez v4, :cond_3

    .line 107
    iget-object v4, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$1;->this$0:Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;

    iget-object v4, v4, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->_sRecordArray:[Ljava/lang/String;

    add-int/lit8 v5, v3, 0x1

    .end local v3    # "recIdx":I
    .local v5, "recIdx":I
    aget-object v1, v4, v3

    .line 109
    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v3

    const-string v4, ", ignoring."

    const/high16 v6, 0x42c80000    # 100.0f

    if-gtz v3, :cond_0

    .line 110
    sget-object v3, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->LOG_TAG:Ljava/lang/String;

    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    const-string v8, "Warning: found string of invalid length "

    invoke-virtual {v7, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v8

    invoke-virtual {v7, v8}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v7, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-static {v3, v4}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 111
    add-int/lit8 v0, v0, 0x1

    .line 112
    int-to-float v3, v0

    iget-object v4, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$1;->this$0:Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;

    iget-wide v7, v4, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->_numRecords:J

    long-to-float v4, v7

    div-float/2addr v3, v4

    mul-float/2addr v3, v6

    float-to-int v3, v3

    int-to-float v3, v3

    sput v3, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->_progress:F

    .line 113
    move v3, v5

    goto :goto_0

    .line 115
    :cond_0
    const/4 v3, 0x0

    invoke-virtual {v1, v3}, Ljava/lang/String;->charAt(I)C

    move-result v3

    const/16 v7, 0x3a

    if-ne v3, v7, :cond_2

    .line 117
    invoke-virtual {v1, v2}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object v1

    .line 120
    iget-object v3, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$1;->this$0:Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;

    invoke-virtual {v3, v1}, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->hexStringToByteArray(Ljava/lang/String;)[B

    move-result-object v3

    .line 124
    .local v3, "record":[B
    add-int/lit8 v0, v0, 0x1

    .line 125
    int-to-float v4, v0

    iget-object v7, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$1;->this$0:Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;

    iget-wide v7, v7, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->_numRecords:J

    long-to-float v7, v7

    div-float/2addr v4, v7

    mul-float/2addr v4, v6

    float-to-int v4, v4

    int-to-float v4, v4

    sput v4, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->_progress:F

    .line 128
    iget-object v4, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$1;->this$0:Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;

    array-length v6, v3

    invoke-static {v4, v3, v6}, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->access$000(Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;[BI)Z

    move-result v4

    if-nez v4, :cond_1

    .line 131
    iget-object v2, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$1;->this$0:Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;

    const-wide/16 v6, -0x1

    iput-wide v6, v2, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->_numRecords:J

    .line 132
    const/high16 v2, -0x40800000    # -1.0f

    sput v2, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->_progress:F

    .line 133
    move v3, v5

    goto :goto_1

    .line 136
    :cond_1
    iget-object v4, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$1;->this$0:Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;

    const/4 v6, 0x0

    iput-object v6, v4, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->mRecordData:[B

    .line 137
    .end local v3    # "record":[B
    move v3, v5

    goto/16 :goto_0

    .line 138
    :cond_2
    sget-object v3, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->LOG_TAG:Ljava/lang/String;

    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    const-string v8, "Warning: found invalid record "

    invoke-virtual {v7, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-static {v3, v4}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 139
    add-int/lit8 v0, v0, 0x1

    .line 140
    int-to-float v3, v0

    iget-object v4, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$1;->this$0:Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;

    iget-wide v7, v4, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->_numRecords:J

    long-to-float v4, v7

    div-float/2addr v3, v4

    mul-float/2addr v3, v6

    float-to-int v3, v3

    int-to-float v3, v3

    sput v3, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->_progress:F

    .line 141
    move v3, v5

    goto/16 :goto_0

    .line 144
    .end local v5    # "recIdx":I
    .local v3, "recIdx":I
    :cond_3
    :goto_1
    iget-object v2, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$1;->this$0:Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;

    invoke-static {v2}, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->access$100(Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;)V

    .line 145
    iget-object v2, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$1;->this$0:Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;

    iget-wide v4, v2, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->_numRecords:J

    const-wide/16 v6, 0x0

    cmp-long v2, v4, v6

    if-lez v2, :cond_4

    .line 146
    sget-object v2, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->LOG_TAG:Ljava/lang/String;

    const-string v4, "Success downloading fw"

    invoke-static {v2, v4}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 148
    :cond_4
    return-void
.end method
