.class public Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$RobotInfo;
.super Ljava/lang/Object;
.source "fwUpdateLibEntry.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = "RobotInfo"
.end annotation


# instance fields
.field private Hardware_Version:[Ljava/lang/String;

.field public firmwareMajorVersion:I

.field public firmwareMinorVersion:I

.field public hardwareName:Ljava/lang/String;

.field public hardwareVersion:I

.field public release:Z

.field final synthetic this$0:Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;


# direct methods
.method public constructor <init>(Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;)V
    .locals 18
    .param p1, "this$0"    # Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;

    .line 200
    move-object/from16 v0, p0

    move-object/from16 v1, p1

    iput-object v1, v0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$RobotInfo;->this$0:Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;

    invoke-direct/range {p0 .. p0}, Ljava/lang/Object;-><init>()V

    .line 208
    const-string v2, "P5B"

    const-string v3, "P5"

    const-string v4, "P6A"

    const-string v5, "P6B"

    const-string v6, "P7"

    const-string v7, "P8"

    const-string v8, "P9"

    const-string v9, "EP1"

    const-string v10, "EP2"

    const-string v11, "EP3"

    const-string v12, "FEP"

    const-string v13, "FEP2"

    const-string v14, "PP"

    const-string v15, "PS1"

    const-string v16, "PS2"

    const-string v17, "PS3"

    filled-new-array/range {v2 .. v17}, [Ljava/lang/String;

    move-result-object v2

    iput-object v2, v0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$RobotInfo;->Hardware_Version:[Ljava/lang/String;

    .line 201
    const/4 v2, -0x1

    iput v2, v0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$RobotInfo;->hardwareVersion:I

    iput v2, v0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$RobotInfo;->firmwareMinorVersion:I

    iput v2, v0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$RobotInfo;->firmwareMajorVersion:I

    .line 202
    const/4 v2, 0x0

    iput-boolean v2, v0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$RobotInfo;->release:Z

    .line 203
    const-string v2, "Unknown - Not Initialized"

    iput-object v2, v0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$RobotInfo;->hardwareName:Ljava/lang/String;

    .line 204
    invoke-virtual/range {p0 .. p0}, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$RobotInfo;->updateInfo()V

    .line 205
    return-void
.end method


# virtual methods
.method public updateInfo()V
    .locals 4

    .line 232
    iget-object v0, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$RobotInfo;->this$0:Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;

    invoke-static {v0}, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;->access$200(Lme/embodied/firmwareupdatelib/fwUpdateLibEntry;)I

    move-result v0

    .line 233
    .local v0, "info":I
    const/4 v1, 0x0

    if-gez v0, :cond_0

    .line 234
    const/4 v2, -0x1

    iput v2, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$RobotInfo;->hardwareVersion:I

    iput v2, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$RobotInfo;->firmwareMinorVersion:I

    iput v2, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$RobotInfo;->firmwareMajorVersion:I

    .line 235
    iput-boolean v1, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$RobotInfo;->release:Z

    .line 236
    const-string v1, "Unknown - No response"

    iput-object v1, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$RobotInfo;->hardwareName:Ljava/lang/String;

    .line 237
    return-void

    .line 240
    :cond_0
    and-int/lit8 v2, v0, 0x7f

    iput v2, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$RobotInfo;->firmwareMajorVersion:I

    .line 241
    shr-int/lit8 v2, v0, 0x8

    and-int/lit8 v2, v2, 0x7f

    iput v2, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$RobotInfo;->firmwareMinorVersion:I

    .line 242
    shr-int/lit8 v2, v0, 0x10

    and-int/lit8 v2, v2, 0x3f

    iput v2, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$RobotInfo;->hardwareVersion:I

    .line 243
    shr-int/lit8 v2, v0, 0x10

    and-int/lit8 v2, v2, 0x40

    if-eqz v2, :cond_1

    .line 244
    const/4 v1, 0x1

    iput-boolean v1, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$RobotInfo;->release:Z

    goto :goto_0

    .line 245
    :cond_1
    iput-boolean v1, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$RobotInfo;->release:Z

    .line 247
    :goto_0
    iget v1, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$RobotInfo;->hardwareVersion:I

    if-lez v1, :cond_2

    iget-object v2, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$RobotInfo;->Hardware_Version:[Ljava/lang/String;

    array-length v3, v2

    if-ge v1, v3, :cond_2

    .line 248
    aget-object v1, v2, v1

    iput-object v1, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$RobotInfo;->hardwareName:Ljava/lang/String;

    goto :goto_1

    .line 249
    :cond_2
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Unknown level ("

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v2, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$RobotInfo;->hardwareVersion:I

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v2, ")"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    iput-object v1, p0, Lme/embodied/firmwareupdatelib/fwUpdateLibEntry$RobotInfo;->hardwareName:Ljava/lang/String;

    .line 250
    :goto_1
    return-void
.end method
