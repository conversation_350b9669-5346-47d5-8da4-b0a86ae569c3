.class public Lme/embodied/RebootCause;
.super Ljava/lang/Object;
.source "RebootCause.java"


# static fields
.field private static final BOOT_COUNT_FILE:Ljava/io/File;

.field private static final BOOT_LOOP_TIMEOUT:J = 0x927c0L

.field private static final CODE_FILE_EXCEPTION:I = -0x1

.field private static final CODE_NO_FILE:I = -0x2

.field private static final LAST_REBOOT_CAUSE_FILE:Ljava/io/File;

.field public static final RECOVER_FORCE_OTA:I = 0x2

.field public static final RECOVER_NONE:I = 0x0

.field public static final RECOVER_REBOOT:I = 0x1

.field public static final RECOVER_S3_REBOOT:I = 0x3

.field private static final TAG:Ljava/lang/String; = "RebootCause"


# instance fields
.field private boot_count_:I

.field private last_code_:I

.field private timestamp_:J


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 35
    new-instance v0, Ljava/io/File;

    const-string v1, "/sdcard/EmbodiedData/.rebootCause"

    invoke-direct {v0, v1}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    sput-object v0, Lme/embodied/RebootCause;->LAST_REBOOT_CAUSE_FILE:Ljava/io/File;

    .line 36
    new-instance v0, Ljava/io/File;

    const-string v1, "/sdcard/EmbodiedData/.boot_count"

    invoke-direct {v0, v1}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    sput-object v0, Lme/embodied/RebootCause;->BOOT_COUNT_FILE:Ljava/io/File;

    return-void
.end method

.method private constructor <init>()V
    .locals 7

    .line 105
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 106
    sget-object v0, Lme/embodied/RebootCause;->LAST_REBOOT_CAUSE_FILE:Ljava/io/File;

    invoke-virtual {v0}, Ljava/io/File;->exists()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 107
    const/4 v0, 0x3

    new-array v1, v0, [Ljava/lang/String;

    .line 108
    :try_start_0
    new-instance v2, Ljava/io/FileReader;

    sget-object v3, Lme/embodied/RebootCause;->LAST_REBOOT_CAUSE_FILE:Ljava/io/File;

    invoke-direct {v2, v3}, Ljava/io/FileReader;-><init>(Ljava/io/File;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    :try_start_1
    new-instance v3, Ljava/io/BufferedReader;

    invoke-direct {v3, v2}, Ljava/io/BufferedReader;-><init>(Ljava/io/Reader;)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_3

    .line 109
    const/4 v4, 0x0

    move v5, v4

    :goto_0
    if-ge v5, v0, :cond_0

    .line 110
    :try_start_2
    invoke-virtual {v3}, Ljava/io/BufferedReader;->readLine()Ljava/lang/String;

    move-result-object v6

    aput-object v6, v1, v5

    .line 109
    add-int/lit8 v5, v5, 0x1

    goto :goto_0

    .line 111
    :cond_0
    aget-object v0, v1, v4

    invoke-static {v0}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v0

    iput v0, p0, Lme/embodied/RebootCause;->last_code_:I

    .line 112
    const/4 v0, 0x1

    aget-object v0, v1, v0

    invoke-static {v0}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v0

    iput v0, p0, Lme/embodied/RebootCause;->boot_count_:I

    .line 113
    const/4 v0, 0x2

    aget-object v0, v1, v0

    invoke-static {v0}, Ljava/lang/Long;->parseLong(Ljava/lang/String;)J

    move-result-wide v0

    iput-wide v0, p0, Lme/embodied/RebootCause;->timestamp_:J
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    .line 114
    :try_start_3
    invoke-virtual {v3}, Ljava/io/BufferedReader;->close()V
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_3

    :try_start_4
    invoke-virtual {v2}, Ljava/io/FileReader;->close()V
    :try_end_4
    .catch Ljava/lang/Exception; {:try_start_4 .. :try_end_4} :catch_0

    .line 117
    goto :goto_3

    .line 108
    :catchall_0
    move-exception v0

    :try_start_5
    throw v0
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_1

    .line 114
    :catchall_1
    move-exception v1

    :try_start_6
    invoke-virtual {v3}, Ljava/io/BufferedReader;->close()V
    :try_end_6
    .catchall {:try_start_6 .. :try_end_6} :catchall_2

    goto :goto_1

    :catchall_2
    move-exception v3

    :try_start_7
    invoke-virtual {v0, v3}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    :goto_1
    throw v1
    :try_end_7
    .catchall {:try_start_7 .. :try_end_7} :catchall_3

    .line 108
    :catchall_3
    move-exception v0

    :try_start_8
    throw v0
    :try_end_8
    .catchall {:try_start_8 .. :try_end_8} :catchall_4

    .line 114
    :catchall_4
    move-exception v1

    :try_start_9
    invoke-virtual {v2}, Ljava/io/FileReader;->close()V
    :try_end_9
    .catchall {:try_start_9 .. :try_end_9} :catchall_5

    goto :goto_2

    :catchall_5
    move-exception v2

    :try_start_a
    invoke-virtual {v0, v2}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    :goto_2
    throw v1
    :try_end_a
    .catch Ljava/lang/Exception; {:try_start_a .. :try_end_a} :catch_0

    :catch_0
    move-exception v0

    .line 115
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Exception reading/decoding "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v2, Lme/embodied/RebootCause;->LAST_REBOOT_CAUSE_FILE:Ljava/io/File;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const-string v2, "RebootCause"

    invoke-static {v2, v1, v0}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)I

    .line 116
    const/4 v0, -0x1

    iput v0, p0, Lme/embodied/RebootCause;->last_code_:I

    .line 118
    :goto_3
    goto :goto_4

    .line 119
    :cond_1
    const/4 v0, -0x2

    iput v0, p0, Lme/embodied/RebootCause;->last_code_:I

    .line 121
    :goto_4
    return-void
.end method

.method public static getLastCause()I
    .locals 1

    .line 72
    new-instance v0, Lme/embodied/RebootCause;

    invoke-direct {v0}, Lme/embodied/RebootCause;-><init>()V

    .line 73
    iget v0, v0, Lme/embodied/RebootCause;->last_code_:I

    if-gez v0, :cond_0

    .line 74
    const/4 v0, 0x0

    return v0

    .line 76
    :cond_0
    return v0
.end method

.method private static getSystemBootCount(Landroid/content/ContentResolver;)I
    .locals 1

    .line 125
    :try_start_0
    const-string v0, "boot_count"

    invoke-static {p0, v0}, Landroid/provider/Settings$Global;->getInt(Landroid/content/ContentResolver;Ljava/lang/String;)I

    move-result p0
    :try_end_0
    .catch Landroid/provider/Settings$SettingNotFoundException; {:try_start_0 .. :try_end_0} :catch_0

    return p0

    .line 126
    :catch_0
    move-exception p0

    .line 127
    const-string p0, "RebootCause"

    const-string v0, "Couldn\'t determine current boot count."

    invoke-static {p0, v0}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 129
    const/4 p0, -0x1

    return p0
.end method

.method public static recordCause(ILandroid/content/ContentResolver;)V
    .locals 9

    .line 80
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    .line 83
    new-instance v2, Lme/embodied/RebootCause;

    invoke-direct {v2}, Lme/embodied/RebootCause;-><init>()V

    .line 84
    const-string v3, "RebootCause"

    const/4 v4, 0x2

    if-ge p0, v4, :cond_0

    iget v5, v2, Lme/embodied/RebootCause;->last_code_:I

    if-lez v5, :cond_0

    iget-wide v5, v2, Lme/embodied/RebootCause;->timestamp_:J

    sub-long v5, v0, v5

    const-wide/32 v7, 0x927c0

    cmp-long v2, v5, v7

    if-gez v2, :cond_0

    .line 87
    const-string p0, "BO#3029 Boot loop detected.  Multiple forced reboots occurred within 10 minutes.  Forcing OTA cycle."

    invoke-static {v3, p0}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 88
    move p0, v4

    .line 91
    :cond_0
    invoke-static {p1}, Lme/embodied/RebootCause;->getSystemBootCount(Landroid/content/ContentResolver;)I

    move-result p1

    .line 92
    if-gez p1, :cond_1

    .line 93
    const-string p0, "Unable to record boot cause due to missing boot count."

    invoke-static {v3, p0}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 94
    return-void

    .line 97
    :cond_1
    :try_start_0
    new-instance v2, Ljava/io/PrintWriter;

    sget-object v5, Lme/embodied/RebootCause;->LAST_REBOOT_CAUSE_FILE:Ljava/io/File;

    invoke-direct {v2, v5}, Ljava/io/PrintWriter;-><init>(Ljava/io/File;)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    .line 98
    :try_start_1
    const-string v5, "%d\n%d\n%d\n"

    const/4 v6, 0x3

    new-array v6, v6, [Ljava/lang/Object;

    const/4 v7, 0x0

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    aput-object p0, v6, v7

    const/4 p0, 0x1

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    aput-object p1, v6, p0

    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object p0

    aput-object p0, v6, v4

    invoke-virtual {v2, v5, v6}, Ljava/io/PrintWriter;->printf(Ljava/lang/String;[Ljava/lang/Object;)Ljava/io/PrintWriter;

    .line 99
    invoke-virtual {v2}, Ljava/io/PrintWriter;->flush()V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 100
    :try_start_2
    invoke-virtual {v2}, Ljava/io/PrintWriter;->close()V
    :try_end_2
    .catch Ljava/io/IOException; {:try_start_2 .. :try_end_2} :catch_0

    .line 102
    goto :goto_1

    .line 97
    :catchall_0
    move-exception p0

    :try_start_3
    throw p0
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_1

    .line 100
    :catchall_1
    move-exception p1

    :try_start_4
    invoke-virtual {v2}, Ljava/io/PrintWriter;->close()V
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_2

    goto :goto_0

    :catchall_2
    move-exception v0

    :try_start_5
    invoke-virtual {p0, v0}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    :goto_0
    throw p1
    :try_end_5
    .catch Ljava/io/IOException; {:try_start_5 .. :try_end_5} :catch_0

    :catch_0
    move-exception p0

    .line 101
    const-string p1, "Failed to write reboot cause."

    invoke-static {v3, p1, p0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)I

    .line 103
    :goto_1
    return-void
.end method

.method public static updateValidity(Landroid/content/ContentResolver;)V
    .locals 6

    .line 49
    invoke-static {p0}, Lme/embodied/RebootCause;->getSystemBootCount(Landroid/content/ContentResolver;)I

    move-result p0

    .line 50
    new-instance v0, Lme/embodied/RebootCause;

    invoke-direct {v0}, Lme/embodied/RebootCause;-><init>()V

    .line 51
    iget v1, v0, Lme/embodied/RebootCause;->last_code_:I

    const/4 v2, 0x1

    const-string v3, "RebootCause"

    if-gtz v1, :cond_0

    .line 53
    sget-object v0, Lme/embodied/RebootCause;->LAST_REBOOT_CAUSE_FILE:Ljava/io/File;

    invoke-virtual {v0}, Ljava/io/File;->delete()Z

    goto :goto_0

    .line 54
    :cond_0
    iget v1, v0, Lme/embodied/RebootCause;->boot_count_:I

    add-int/2addr v1, v2

    if-eq p0, v1, :cond_1

    .line 56
    sget-object v0, Lme/embodied/RebootCause;->LAST_REBOOT_CAUSE_FILE:Ljava/io/File;

    invoke-virtual {v0}, Ljava/io/File;->delete()Z

    .line 57
    const-string v0, "Found reboot cause file from older boot, deleted."

    invoke-static {v3, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_0

    .line 59
    :cond_1
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "BO#3028 System boot after recovery.  LastCode="

    invoke-virtual {v1, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v4, v0, Lme/embodied/RebootCause;->last_code_:I

    invoke-virtual {v1, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v4, ", TS="

    invoke-virtual {v1, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-wide v4, v0, Lme/embodied/RebootCause;->timestamp_:J

    invoke-virtual {v1, v4, v5}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v3, v0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 63
    :goto_0
    :try_start_0
    new-instance v0, Ljava/io/PrintWriter;

    sget-object v1, Lme/embodied/RebootCause;->BOOT_COUNT_FILE:Ljava/io/File;

    invoke-direct {v0, v1}, Ljava/io/PrintWriter;-><init>(Ljava/io/File;)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    .line 64
    :try_start_1
    const-string v1, "%d"

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v4, 0x0

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    aput-object p0, v2, v4

    invoke-virtual {v0, v1, v2}, Ljava/io/PrintWriter;->printf(Ljava/lang/String;[Ljava/lang/Object;)Ljava/io/PrintWriter;

    .line 65
    invoke-virtual {v0}, Ljava/io/PrintWriter;->flush()V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 66
    :try_start_2
    invoke-virtual {v0}, Ljava/io/PrintWriter;->close()V
    :try_end_2
    .catch Ljava/io/IOException; {:try_start_2 .. :try_end_2} :catch_0

    .line 68
    goto :goto_2

    .line 63
    :catchall_0
    move-exception p0

    :try_start_3
    throw p0
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_1

    .line 66
    :catchall_1
    move-exception v1

    :try_start_4
    invoke-virtual {v0}, Ljava/io/PrintWriter;->close()V
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_2

    goto :goto_1

    :catchall_2
    move-exception v0

    :try_start_5
    invoke-virtual {p0, v0}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    :goto_1
    throw v1
    :try_end_5
    .catch Ljava/io/IOException; {:try_start_5 .. :try_end_5} :catch_0

    :catch_0
    move-exception p0

    .line 67
    const-string v0, "Failed to write boot count."

    invoke-static {v3, v0, p0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)I

    .line 69
    :goto_2
    return-void
.end method
