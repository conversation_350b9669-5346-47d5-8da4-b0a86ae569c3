.class public Lme/embodied/DiskCleanup;
.super Ljava/lang/Object;
.source "DiskCleanup.java"


# static fields
.field static final TAG:Ljava/lang/String; = "BoDiskCleaner"


# instance fields
.field currentFreeSpace_:J

.field exemptSet_:Ljava/util/HashSet;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/HashSet<",
            "Ljava/io/File;",
            ">;"
        }
    .end annotation
.end field

.field rootDir_:Ljava/io/File;


# direct methods
.method public constructor <init>(Ljava/io/File;)V
    .locals 2

    .line 36
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 37
    iput-object p1, p0, Lme/embodied/DiskCleanup;->rootDir_:Ljava/io/File;

    .line 38
    const-wide/16 v0, -0x1

    iput-wide v0, p0, Lme/embodied/DiskCleanup;->currentFreeSpace_:J

    .line 39
    new-instance p1, Ljava/util/HashSet;

    invoke-direct {p1}, Ljava/util/HashSet;-><init>()V

    iput-object p1, p0, Lme/embodied/DiskCleanup;->exemptSet_:Ljava/util/HashSet;

    .line 40
    return-void
.end method

.method protected static final getArchiveName(Ljava/io/File;I)Ljava/io/File;
    .locals 2

    .line 208
    new-instance v0, Ljava/io/File;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p0}, Ljava/io/File;->getAbsolutePath()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p0, "."

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string p0, ".gz"

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-direct {v0, p0}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    return-object v0
.end method

.method public static humanReadableByteCountBin(J)Ljava/lang/String;
    .locals 8

    .line 193
    const-wide/high16 v0, -0x8000000000000000L

    cmp-long v0, p0, v0

    if-nez v0, :cond_0

    const-wide v0, 0x7fffffffffffffffL

    goto :goto_0

    :cond_0
    invoke-static {p0, p1}, Ljava/lang/Math;->abs(J)J

    move-result-wide v0

    .line 194
    :goto_0
    const-wide/16 v2, 0x400

    cmp-long v2, v0, v2

    if-gez v2, :cond_1

    .line 195
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, p0, p1}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    const-string p0, " B"

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0

    .line 197
    :cond_1
    nop

    .line 198
    new-instance v2, Ljava/text/StringCharacterIterator;

    const-string v3, "KMGTPE"

    invoke-direct {v2, v3}, Ljava/text/StringCharacterIterator;-><init>(Ljava/lang/String;)V

    .line 199
    const/16 v3, 0x28

    move-wide v4, v0

    :goto_1
    if-ltz v3, :cond_2

    const-wide v6, 0xfffccccccccccccL

    shr-long/2addr v6, v3

    cmp-long v6, v0, v6

    if-lez v6, :cond_2

    .line 200
    const/16 v6, 0xa

    shr-long/2addr v4, v6

    .line 201
    invoke-virtual {v2}, Ljava/text/StringCharacterIterator;->next()C

    .line 199
    add-int/lit8 v3, v3, -0xa

    goto :goto_1

    .line 203
    :cond_2
    invoke-static {p0, p1}, Ljava/lang/Long;->signum(J)I

    move-result p0

    int-to-long p0, p0

    mul-long/2addr v4, p0

    .line 204
    const/4 p0, 0x2

    new-array p0, p0, [Ljava/lang/Object;

    const/4 p1, 0x0

    long-to-double v0, v4

    const-wide/high16 v3, 0x4090000000000000L    # 1024.0

    div-double/2addr v0, v3

    invoke-static {v0, v1}, Ljava/lang/Double;->valueOf(D)Ljava/lang/Double;

    move-result-object v0

    aput-object v0, p0, p1

    const/4 p1, 0x1

    invoke-virtual {v2}, Ljava/text/StringCharacterIterator;->current()C

    move-result v0

    invoke-static {v0}, Ljava/lang/Character;->valueOf(C)Ljava/lang/Character;

    move-result-object v0

    aput-object v0, p0, p1

    const-string p1, "%.1f %cB"

    invoke-static {p1, p0}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method static synthetic lambda$ensureFreeSpace$0(Ljava/util/regex/Pattern;Ljava/io/File;)Z
    .locals 1

    .line 68
    invoke-virtual {p1}, Ljava/io/File;->isDirectory()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-virtual {p1}, Ljava/io/File;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Ljava/util/regex/Pattern;->matcher(Ljava/lang/CharSequence;)Ljava/util/regex/Matcher;

    move-result-object p0

    invoke-virtual {p0}, Ljava/util/regex/Matcher;->matches()Z

    move-result p0

    if-eqz p0, :cond_0

    .line 69
    const/4 p0, 0x1

    return p0

    .line 71
    :cond_0
    const/4 p0, 0x0

    return p0
.end method

.method static synthetic lambda$ensureFreeSpace$1(Ljava/io/File;Ljava/io/File;)I
    .locals 4

    .line 84
    invoke-virtual {p1}, Ljava/io/File;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v0

    invoke-virtual {p0}, Ljava/io/File;->getName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/String;->length()I

    move-result v1

    invoke-static {v0, v1}, Ljava/lang/Integer;->compare(II)I

    move-result v0

    .line 85
    if-eqz v0, :cond_0

    .line 86
    return v0

    .line 88
    :cond_0
    invoke-virtual {p0}, Ljava/io/File;->getName()Ljava/lang/String;

    move-result-object p0

    const/4 v0, 0x0

    const/16 v1, 0xd

    invoke-virtual {p0, v0, v1}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object p0

    invoke-static {p0}, Ljava/lang/Long;->parseLong(Ljava/lang/String;)J

    move-result-wide v2

    .line 89
    invoke-virtual {p1}, Ljava/io/File;->getName()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {p0, v0, v1}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object p0

    invoke-static {p0}, Ljava/lang/Long;->parseLong(Ljava/lang/String;)J

    move-result-wide p0

    .line 91
    invoke-static {v2, v3, p0, p1}, Ljava/lang/Long;->compare(JJ)I

    move-result p0

    return p0
.end method

.method public static recursivePurge(Ljava/io/File;Z)J
    .locals 7

    .line 173
    nop

    .line 174
    invoke-virtual {p0}, Ljava/io/File;->isFile()Z

    move-result v0

    const-wide/16 v1, 0x0

    if-eqz v0, :cond_1

    .line 175
    invoke-virtual {p0}, Ljava/io/File;->length()J

    move-result-wide v3

    add-long/2addr v3, v1

    .line 176
    if-nez p1, :cond_0

    .line 177
    invoke-virtual {p0}, Ljava/io/File;->delete()Z

    .line 178
    :cond_0
    return-wide v3

    .line 180
    :cond_1
    invoke-virtual {p0}, Ljava/io/File;->listFiles()[Ljava/io/File;

    move-result-object v0

    .line 181
    if-eqz v0, :cond_2

    .line 182
    invoke-virtual {p0}, Ljava/io/File;->listFiles()[Ljava/io/File;

    move-result-object v0

    array-length v3, v0

    const/4 v4, 0x0

    :goto_0
    if-ge v4, v3, :cond_2

    aget-object v5, v0, v4

    .line 183
    invoke-static {v5, p1}, Lme/embodied/DiskCleanup;->recursivePurge(Ljava/io/File;Z)J

    move-result-wide v5

    add-long/2addr v1, v5

    .line 182
    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    .line 186
    :cond_2
    if-nez p1, :cond_3

    .line 187
    invoke-virtual {p0}, Ljava/io/File;->delete()Z

    .line 188
    :cond_3
    return-wide v1
.end method

.method public static rotateFile(Ljava/io/File;JI)V
    .locals 4

    .line 212
    const-string v0, "BoDiskCleaner"

    invoke-virtual {p0}, Ljava/io/File;->length()J

    move-result-wide v1

    cmp-long p1, v1, p1

    if-gtz p1, :cond_0

    .line 213
    return-void

    .line 216
    :cond_0
    move p1, p3

    :goto_0
    if-lez p1, :cond_3

    .line 217
    invoke-static {p0, p1}, Lme/embodied/DiskCleanup;->getArchiveName(Ljava/io/File;I)Ljava/io/File;

    move-result-object p2

    .line 218
    invoke-virtual {p2}, Ljava/io/File;->exists()Z

    move-result v1

    if-eqz v1, :cond_2

    .line 219
    if-ne p1, p3, :cond_1

    .line 220
    invoke-virtual {p2}, Ljava/io/File;->delete()Z

    goto :goto_1

    .line 222
    :cond_1
    add-int/lit8 v1, p1, 0x1

    invoke-static {p0, v1}, Lme/embodied/DiskCleanup;->getArchiveName(Ljava/io/File;I)Ljava/io/File;

    move-result-object v1

    invoke-virtual {p2, v1}, Ljava/io/File;->renameTo(Ljava/io/File;)Z

    .line 216
    :cond_2
    :goto_1
    add-int/lit8 p1, p1, -0x1

    goto :goto_0

    .line 226
    :cond_3
    const/4 p1, 0x1

    invoke-static {p0, p1}, Lme/embodied/DiskCleanup;->getArchiveName(Ljava/io/File;I)Ljava/io/File;

    move-result-object p1

    .line 227
    :try_start_0
    new-instance p2, Ljava/io/FileInputStream;

    invoke-direct {p2, p0}, Ljava/io/FileInputStream;-><init>(Ljava/io/File;)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    .line 228
    :try_start_1
    new-instance p3, Ljava/util/zip/GZIPOutputStream;

    new-instance v1, Ljava/io/FileOutputStream;

    invoke-direct {v1, p1}, Ljava/io/FileOutputStream;-><init>(Ljava/io/File;)V

    invoke-direct {p3, v1}, Ljava/util/zip/GZIPOutputStream;-><init>(Ljava/io/OutputStream;)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_3

    .line 227
    nop

    .line 230
    const/16 v1, 0x400

    :try_start_2
    new-array v1, v1, [B

    .line 232
    :goto_2
    invoke-virtual {p2, v1}, Ljava/io/FileInputStream;->read([B)I

    move-result v2

    if-lez v2, :cond_4

    .line 233
    const/4 v3, 0x0

    invoke-virtual {p3, v1, v3, v2}, Ljava/util/zip/GZIPOutputStream;->write([BII)V

    goto :goto_2

    .line 235
    :cond_4
    invoke-virtual {p0}, Ljava/io/File;->delete()Z

    .line 236
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Rotated "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v2, " to "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v0, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    .line 237
    :try_start_3
    invoke-virtual {p3}, Ljava/util/zip/GZIPOutputStream;->close()V
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_3

    :try_start_4
    invoke-virtual {p2}, Ljava/io/FileInputStream;->close()V
    :try_end_4
    .catch Ljava/io/IOException; {:try_start_4 .. :try_end_4} :catch_0

    .line 239
    goto :goto_5

    .line 227
    :catchall_0
    move-exception p1

    :try_start_5
    throw p1
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_1

    .line 237
    :catchall_1
    move-exception v1

    :try_start_6
    invoke-virtual {p3}, Ljava/util/zip/GZIPOutputStream;->close()V
    :try_end_6
    .catchall {:try_start_6 .. :try_end_6} :catchall_2

    goto :goto_3

    :catchall_2
    move-exception p3

    :try_start_7
    invoke-virtual {p1, p3}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    :goto_3
    throw v1
    :try_end_7
    .catchall {:try_start_7 .. :try_end_7} :catchall_3

    .line 227
    :catchall_3
    move-exception p1

    :try_start_8
    throw p1
    :try_end_8
    .catchall {:try_start_8 .. :try_end_8} :catchall_4

    .line 237
    :catchall_4
    move-exception p3

    :try_start_9
    invoke-virtual {p2}, Ljava/io/FileInputStream;->close()V
    :try_end_9
    .catchall {:try_start_9 .. :try_end_9} :catchall_5

    goto :goto_4

    :catchall_5
    move-exception p2

    :try_start_a
    invoke-virtual {p1, p2}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    :goto_4
    throw p3
    :try_end_a
    .catch Ljava/io/IOException; {:try_start_a .. :try_end_a} :catch_0

    :catch_0
    move-exception p1

    .line 238
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string p3, "Failed to rotated active logfile: "

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-static {v0, p0, p1}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)I

    .line 240
    :goto_5
    return-void
.end method


# virtual methods
.method public addExemption(Ljava/io/File;)V
    .locals 1

    .line 45
    iget-object v0, p0, Lme/embodied/DiskCleanup;->exemptSet_:Ljava/util/HashSet;

    invoke-virtual {v0, p1}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    .line 46
    return-void
.end method

.method public ensureFreeSpace(JZ)Z
    .locals 17

    .line 56
    move-object/from16 v0, p0

    move/from16 v1, p3

    invoke-virtual/range {p0 .. p0}, Lme/embodied/DiskCleanup;->getFreeSpace()J

    move-result-wide v2

    .line 57
    cmp-long v4, v2, p1

    const-string v5, " Have: "

    const/4 v6, 0x1

    const-string v7, "BoDiskCleaner"

    if-ltz v4, :cond_0

    .line 58
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "Disk free space check OK!  Need: "

    invoke-virtual {v1, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-static/range {p1 .. p2}, Lme/embodied/DiskCleanup;->humanReadableByteCountBin(J)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v1, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 59
    invoke-static {v2, v3}, Lme/embodied/DiskCleanup;->humanReadableByteCountBin(J)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    .line 58
    invoke-static {v7, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 60
    return v6

    .line 66
    :cond_0
    const-string v4, "\\d{13}_?"

    invoke-static {v4}, Ljava/util/regex/Pattern;->compile(Ljava/lang/String;)Ljava/util/regex/Pattern;

    move-result-object v4

    .line 67
    iget-object v8, v0, Lme/embodied/DiskCleanup;->rootDir_:Ljava/io/File;

    new-instance v9, Lme/embodied/-$$Lambda$DiskCleanup$5gey-zFDqbV1aMLXvC41JvMqtNc;

    invoke-direct {v9, v4}, Lme/embodied/-$$Lambda$DiskCleanup$5gey-zFDqbV1aMLXvC41JvMqtNc;-><init>(Ljava/util/regex/Pattern;)V

    invoke-virtual {v8, v9}, Ljava/io/File;->listFiles(Ljava/io/FileFilter;)[Ljava/io/File;

    move-result-object v8

    .line 74
    const/4 v9, 0x0

    if-nez v8, :cond_1

    .line 75
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Failed to get directory listing for root: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v2, v0, Lme/embodied/DiskCleanup;->rootDir_:Ljava/io/File;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v7, v1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 76
    return v9

    .line 82
    :cond_1
    sget-object v10, Lme/embodied/-$$Lambda$DiskCleanup$iu8_NpUMInQoK-KNYljLoIJdCdU;->INSTANCE:Lme/embodied/-$$Lambda$DiskCleanup$iu8_NpUMInQoK-KNYljLoIJdCdU;

    invoke-static {v8, v10}, Ljava/util/Arrays;->sort([Ljava/lang/Object;Ljava/util/Comparator;)V

    .line 94
    new-instance v10, Ljava/lang/StringBuilder;

    invoke-direct {v10}, Ljava/lang/StringBuilder;-><init>()V

    const-string v11, "Disk cleanup required.  Candidate dirs: "

    invoke-virtual {v10, v11}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    array-length v11, v8

    invoke-virtual {v10, v11}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v11, " Need: "

    invoke-virtual {v10, v11}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-static/range {p1 .. p2}, Lme/embodied/DiskCleanup;->humanReadableByteCountBin(J)Ljava/lang/String;

    move-result-object v11

    invoke-virtual {v10, v11}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v10, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 95
    invoke-static {v2, v3}, Lme/embodied/DiskCleanup;->humanReadableByteCountBin(J)Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v10, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v10}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    .line 94
    invoke-static {v7, v5}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 100
    nop

    .line 101
    nop

    .line 102
    array-length v5, v8

    move-wide v12, v2

    move v10, v9

    move v11, v10

    :goto_0
    const-string v14, "Purged: "

    if-ge v10, v5, :cond_3

    aget-object v12, v8, v10

    .line 103
    invoke-virtual {v0, v12, v1}, Lme/embodied/DiskCleanup;->purge(Ljava/io/File;Z)J

    move-result-wide v15

    .line 104
    add-int/lit8 v11, v11, 0x1

    .line 105
    new-instance v13, Ljava/lang/StringBuilder;

    invoke-direct {v13}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v13, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12}, Ljava/io/File;->getName()Ljava/lang/String;

    move-result-object v12

    invoke-virtual {v13, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v12

    invoke-static {v7, v12}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 106
    cmp-long v12, v15, p1

    if-ltz v12, :cond_2

    .line 107
    move-wide v12, v15

    goto :goto_1

    .line 102
    :cond_2
    add-int/lit8 v10, v10, 0x1

    move-wide v12, v15

    goto :goto_0

    .line 111
    :cond_3
    :goto_1
    const/4 v5, 0x2

    if-lez v11, :cond_4

    .line 112
    new-array v8, v5, [Ljava/lang/Object;

    .line 113
    invoke-static {v11}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v10

    aput-object v10, v8, v9

    sub-long v2, v12, v2

    invoke-static {v2, v3}, Lme/embodied/DiskCleanup;->humanReadableByteCountBin(J)Ljava/lang/String;

    move-result-object v2

    aput-object v2, v8, v6

    .line 112
    const-string v2, "BO#3020 Disk Cleanup Required.  Purged %d logging directories to recover %s."

    invoke-static {v2, v8}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v2

    invoke-static {v7, v2}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 116
    :cond_4
    cmp-long v2, v12, p1

    if-ltz v2, :cond_5

    .line 117
    return v6

    .line 122
    :cond_5
    const-string v2, "Desired free space could not be achieved by deleting session logs.  Calling on EXTREME measures."

    invoke-static {v7, v2}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 123
    iget-object v2, v0, Lme/embodied/DiskCleanup;->rootDir_:Ljava/io/File;

    new-instance v3, Lme/embodied/-$$Lambda$DiskCleanup$913QwK4FGc-sXR4sK5acXhdiJsM;

    invoke-direct {v3, v0, v4}, Lme/embodied/-$$Lambda$DiskCleanup$913QwK4FGc-sXR4sK5acXhdiJsM;-><init>(Lme/embodied/DiskCleanup;Ljava/util/regex/Pattern;)V

    invoke-virtual {v2, v3}, Ljava/io/File;->listFiles(Ljava/io/FileFilter;)[Ljava/io/File;

    move-result-object v2

    .line 131
    if-nez v2, :cond_6

    .line 132
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Failed to get directory listing for mystery files for root: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v2, v0, Lme/embodied/DiskCleanup;->rootDir_:Ljava/io/File;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v7, v1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 133
    return v9

    .line 136
    :cond_6
    nop

    .line 137
    nop

    .line 138
    array-length v3, v2

    move v4, v9

    move v8, v4

    move-wide v10, v12

    :goto_2
    if-ge v4, v3, :cond_8

    aget-object v10, v2, v4

    .line 139
    invoke-virtual {v0, v10, v1}, Lme/embodied/DiskCleanup;->purge(Ljava/io/File;Z)J

    move-result-wide v15

    .line 140
    add-int/lit8 v8, v8, 0x1

    .line 141
    new-instance v11, Ljava/lang/StringBuilder;

    invoke-direct {v11}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v11, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v10}, Ljava/io/File;->getName()Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v11, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v10

    invoke-static {v7, v10}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 142
    cmp-long v10, v15, p1

    if-ltz v10, :cond_7

    .line 143
    move-wide v10, v15

    goto :goto_3

    .line 138
    :cond_7
    add-int/lit8 v4, v4, 0x1

    move-wide v10, v15

    goto :goto_2

    .line 147
    :cond_8
    :goto_3
    if-lez v8, :cond_9

    .line 148
    new-array v1, v5, [Ljava/lang/Object;

    .line 149
    invoke-static {v8}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    aput-object v2, v1, v9

    sub-long v2, v10, v12

    invoke-static {v2, v3}, Lme/embodied/DiskCleanup;->humanReadableByteCountBin(J)Ljava/lang/String;

    move-result-object v2

    aput-object v2, v1, v6

    .line 148
    const-string v2, "BO#3021 Emergency Disk Cleanup Required.  Purged %d files/directories to recover %s."

    invoke-static {v2, v1}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    invoke-static {v7, v1}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 152
    :cond_9
    cmp-long v1, v10, p1

    if-ltz v1, :cond_a

    goto :goto_4

    :cond_a
    move v6, v9

    :goto_4
    return v6
.end method

.method public getFreeSpace()J
    .locals 5

    .line 50
    new-instance v0, Landroid/os/StatFs;

    iget-object v1, p0, Lme/embodied/DiskCleanup;->rootDir_:Ljava/io/File;

    invoke-virtual {v1}, Ljava/io/File;->getPath()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Landroid/os/StatFs;-><init>(Ljava/lang/String;)V

    .line 51
    invoke-virtual {v0}, Landroid/os/StatFs;->getBlockSizeLong()J

    move-result-wide v1

    invoke-virtual {v0}, Landroid/os/StatFs;->getAvailableBlocksLong()J

    move-result-wide v3

    mul-long/2addr v1, v3

    iput-wide v1, p0, Lme/embodied/DiskCleanup;->currentFreeSpace_:J

    .line 52
    return-wide v1
.end method

.method public synthetic lambda$ensureFreeSpace$2$DiskCleanup(Ljava/util/regex/Pattern;Ljava/io/File;)Z
    .locals 2

    .line 125
    invoke-virtual {p2}, Ljava/io/File;->getName()Ljava/lang/String;

    move-result-object v0

    const-string v1, "."

    invoke-virtual {v0, v1}, Ljava/lang/String;->startsWith(Ljava/lang/String;)Z

    move-result v0

    if-nez v0, :cond_1

    invoke-virtual {p2}, Ljava/io/File;->isFile()Z

    move-result v0

    if-nez v0, :cond_0

    invoke-virtual {p2}, Ljava/io/File;->isDirectory()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-virtual {p2}, Ljava/io/File;->getName()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Ljava/util/regex/Pattern;->matcher(Ljava/lang/CharSequence;)Ljava/util/regex/Matcher;

    move-result-object p1

    invoke-virtual {p1}, Ljava/util/regex/Matcher;->matches()Z

    move-result p1

    if-nez p1, :cond_1

    .line 126
    :cond_0
    iget-object p1, p0, Lme/embodied/DiskCleanup;->exemptSet_:Ljava/util/HashSet;

    invoke-virtual {p1, p2}, Ljava/util/HashSet;->contains(Ljava/lang/Object;)Z

    move-result p1

    xor-int/lit8 p1, p1, 0x1

    return p1

    .line 128
    :cond_1
    const/4 p1, 0x0

    return p1
.end method

.method protected purge(Ljava/io/File;Z)J
    .locals 4

    .line 157
    if-eqz p2, :cond_0

    .line 159
    const/4 p2, 0x1

    invoke-static {p1, p2}, Lme/embodied/DiskCleanup;->recursivePurge(Ljava/io/File;Z)J

    move-result-wide v0

    .line 160
    iget-wide v2, p0, Lme/embodied/DiskCleanup;->currentFreeSpace_:J

    add-long/2addr v2, v0

    iput-wide v2, p0, Lme/embodied/DiskCleanup;->currentFreeSpace_:J

    .line 161
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Dry Run: Would have purged: "

    invoke-virtual {p2, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/io/File;->getName()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p1, " to reclaim: "

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-static {v0, v1}, Lme/embodied/DiskCleanup;->humanReadableByteCountBin(J)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p1, " and increase free to: "

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-wide v0, p0, Lme/embodied/DiskCleanup;->currentFreeSpace_:J

    invoke-static {v0, v1}, Lme/embodied/DiskCleanup;->humanReadableByteCountBin(J)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const-string p2, "BoDiskCleaner"

    invoke-static {p2, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 162
    iget-wide p1, p0, Lme/embodied/DiskCleanup;->currentFreeSpace_:J

    return-wide p1

    .line 166
    :cond_0
    const/4 p2, 0x0

    invoke-static {p1, p2}, Lme/embodied/DiskCleanup;->recursivePurge(Ljava/io/File;Z)J

    .line 167
    invoke-virtual {p0}, Lme/embodied/DiskCleanup;->getFreeSpace()J

    move-result-wide p1

    return-wide p1
.end method
