.class public Lme/embodied/ServiceMetrics;
.super Ljava/lang/Object;
.source "ServiceMetrics.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lme/embodied/ServiceMetrics$ComponentRecord;
    }
.end annotation


# static fields
.field private static final COMPONENT_SUCCESS_TIME:J = 0x4e20L

.field public static final REBOOT_DELAY_SECONDS:I = 0xf

.field private static final REBOOT_INIT_SECONDS:I = 0x10

.field private static final SERVICE_FAILURE_LIMIT:I = 0x14

.field private static final TAG:Ljava/lang/String; = "ServiceMetrics"


# instance fields
.field excluded_list_:Ljava/util/HashSet;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/HashSet<",
            "Landroid/content/ComponentName;",
            ">;"
        }
    .end annotation
.end field

.field external_error_state_:I

.field monitors_:Ljava/util/HashMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/HashMap<",
            "Landroid/content/ComponentName;",
            "Lme/embodied/ServiceMetrics$ComponentRecord;",
            ">;"
        }
    .end annotation
.end field

.field seconds_until_recovery_:I

.field system_error_state_:I


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 12
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 83
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lme/embodied/ServiceMetrics;->monitors_:Ljava/util/HashMap;

    .line 84
    new-instance v0, Ljava/util/HashSet;

    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    iput-object v0, p0, Lme/embodied/ServiceMetrics;->excluded_list_:Ljava/util/HashSet;

    .line 86
    const/4 v0, 0x0

    iput v0, p0, Lme/embodied/ServiceMetrics;->system_error_state_:I

    .line 87
    iput v0, p0, Lme/embodied/ServiceMetrics;->external_error_state_:I

    return-void
.end method


# virtual methods
.method public declared-synchronized addMonitor(Landroid/content/ComponentName;I)V
    .locals 2

    monitor-enter p0

    .line 96
    :try_start_0
    iget-object v0, p0, Lme/embodied/ServiceMetrics;->monitors_:Ljava/util/HashMap;

    new-instance v1, Lme/embodied/ServiceMetrics$ComponentRecord;

    invoke-direct {v1, p1, p2}, Lme/embodied/ServiceMetrics$ComponentRecord;-><init>(Landroid/content/ComponentName;I)V

    invoke-virtual {v0, p1, v1}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 97
    const-string p2, "ServiceMetrics"

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Adding component to monitor: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {p2, p1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 98
    monitor-exit p0

    return-void

    .line 95
    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public declared-synchronized excludeComponent(Landroid/content/ComponentName;)Lme/embodied/ServiceMetrics;
    .locals 1

    monitor-enter p0

    .line 91
    :try_start_0
    iget-object v0, p0, Lme/embodied/ServiceMetrics;->excluded_list_:Ljava/util/HashSet;

    invoke-virtual {v0, p1}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 92
    monitor-exit p0

    return-object p0

    .line 90
    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method protected onComponentCriticalFail(Landroid/content/ComponentName;)V
    .locals 2

    .line 133
    iget-object v0, p0, Lme/embodied/ServiceMetrics;->excluded_list_:Ljava/util/HashSet;

    invoke-virtual {v0, p1}, Ljava/util/HashSet;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 134
    return-void

    .line 136
    :cond_0
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Component "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Landroid/content/ComponentName;->getShortClassName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, " has reached critical failures."

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "ServiceMetrics"

    invoke-static {v1, v0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 137
    const/4 v0, 0x1

    invoke-virtual {p0, v0, p1}, Lme/embodied/ServiceMetrics;->onSystemError(ILandroid/content/ComponentName;)V

    .line 138
    return-void
.end method

.method public declared-synchronized onExternalError(I)V
    .locals 2

    monitor-enter p0

    .line 124
    :try_start_0
    iget v0, p0, Lme/embodied/ServiceMetrics;->system_error_state_:I

    if-nez v0, :cond_0

    iget v0, p0, Lme/embodied/ServiceMetrics;->external_error_state_:I

    if-nez v0, :cond_0

    .line 125
    const/16 v0, 0x10

    iput v0, p0, Lme/embodied/ServiceMetrics;->seconds_until_recovery_:I

    .line 126
    const-string v0, "ServiceMetrics"

    const-string v1, "External critial error reported.  System component deemed unrecoverable.  System will attempt to recover in 15 seconds."

    invoke-static {v0, v1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 128
    :cond_0
    iget v0, p0, Lme/embodied/ServiceMetrics;->external_error_state_:I

    invoke-static {p1, v0}, Ljava/lang/Math;->max(II)I

    move-result p1

    iput p1, p0, Lme/embodied/ServiceMetrics;->external_error_state_:I
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 129
    monitor-exit p0

    return-void

    .line 123
    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public declared-synchronized onRecover(Landroid/content/ComponentName;)V
    .locals 2

    monitor-enter p0

    .line 150
    :try_start_0
    iget-object v0, p0, Lme/embodied/ServiceMetrics;->monitors_:Ljava/util/HashMap;

    invoke-virtual {v0, p1}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lme/embodied/ServiceMetrics$ComponentRecord;

    .line 151
    if-eqz v0, :cond_0

    .line 152
    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Lme/embodied/ServiceMetrics$ComponentRecord;->handleRecover(Z)Z

    move-result v0

    if-nez v0, :cond_0

    .line 153
    invoke-virtual {p0, p1}, Lme/embodied/ServiceMetrics;->onComponentCriticalFail(Landroid/content/ComponentName;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 156
    :cond_0
    monitor-exit p0

    return-void

    .line 149
    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public declared-synchronized onStartupDetect(Landroid/content/ComponentName;)V
    .locals 1

    monitor-enter p0

    .line 141
    :try_start_0
    iget-object v0, p0, Lme/embodied/ServiceMetrics;->monitors_:Ljava/util/HashMap;

    invoke-virtual {v0, p1}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lme/embodied/ServiceMetrics$ComponentRecord;

    .line 142
    if-eqz v0, :cond_0

    .line 143
    invoke-virtual {v0}, Lme/embodied/ServiceMetrics$ComponentRecord;->handleStartupDetect()Z

    move-result v0

    if-nez v0, :cond_0

    .line 144
    invoke-virtual {p0, p1}, Lme/embodied/ServiceMetrics;->onComponentCriticalFail(Landroid/content/ComponentName;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 147
    :cond_0
    monitor-exit p0

    return-void

    .line 140
    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method onSystemError(ILandroid/content/ComponentName;)V
    .locals 2

    .line 116
    iget v0, p0, Lme/embodied/ServiceMetrics;->system_error_state_:I

    if-nez v0, :cond_0

    iget v0, p0, Lme/embodied/ServiceMetrics;->external_error_state_:I

    if-nez v0, :cond_0

    .line 117
    const/16 v0, 0x10

    iput v0, p0, Lme/embodied/ServiceMetrics;->seconds_until_recovery_:I

    .line 118
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "BO#3030 Critical System component "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Landroid/content/ComponentName;->getShortClassName()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p2, " deemed unrecoverable.  System will attempt to recover in "

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/16 p2, 0xf

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string p2, " seconds."

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    const-string v0, "ServiceMetrics"

    invoke-static {v0, p2}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 120
    :cond_0
    iget p2, p0, Lme/embodied/ServiceMetrics;->system_error_state_:I

    invoke-static {p1, p2}, Ljava/lang/Math;->max(II)I

    move-result p1

    iput p1, p0, Lme/embodied/ServiceMetrics;->system_error_state_:I

    .line 121
    return-void
.end method

.method public declared-synchronized recoveryCheck()I
    .locals 2

    monitor-enter p0

    .line 106
    :try_start_0
    iget v0, p0, Lme/embodied/ServiceMetrics;->system_error_state_:I

    iget v1, p0, Lme/embodied/ServiceMetrics;->external_error_state_:I

    invoke-static {v0, v1}, Ljava/lang/Math;->max(II)I

    move-result v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return v0

    .line 106
    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method public declared-synchronized removeMonitor(Landroid/content/ComponentName;)V
    .locals 3

    monitor-enter p0

    .line 101
    :try_start_0
    iget-object v0, p0, Lme/embodied/ServiceMetrics;->monitors_:Ljava/util/HashMap;

    invoke-virtual {v0, p1}, Ljava/util/HashMap;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    .line 102
    const-string v0, "ServiceMetrics"

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Remove component from monitor: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v0, p1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 103
    monitor-exit p0

    return-void

    .line 100
    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public declared-synchronized updateSecondsRemaining()I
    .locals 1

    monitor-enter p0

    .line 110
    :try_start_0
    iget v0, p0, Lme/embodied/ServiceMetrics;->seconds_until_recovery_:I

    if-lez v0, :cond_0

    .line 111
    iget v0, p0, Lme/embodied/ServiceMetrics;->seconds_until_recovery_:I

    add-int/lit8 v0, v0, -0x1

    iput v0, p0, Lme/embodied/ServiceMetrics;->seconds_until_recovery_:I

    .line 112
    :cond_0
    iget v0, p0, Lme/embodied/ServiceMetrics;->seconds_until_recovery_:I
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return v0

    .line 109
    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method
