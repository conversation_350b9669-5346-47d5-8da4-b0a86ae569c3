.class public Lme/embodied/NetworkUtil;
.super Ljava/lang/Object;
.source "NetworkUtil.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lme/embodied/NetworkUtil$WifiResult;
    }
.end annotation


# static fields
.field private static final ACTION_OS_WIFI_PREF:Ljava/lang/String; = "com.embodied.osctrl.wifi_freq_pref"

.field private static final BAND_24G:I = 0x2

.field private static final BAND_5G:I = 0x1

.field private static final BAND_ANY:I = 0x0

.field private static final BAND_SELECT_MAP:[I

.field private static final TAG:Ljava/lang/String; = "bo-network-util"


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 186
    const/4 v0, 0x3

    new-array v0, v0, [I

    fill-array-data v0, :array_0

    sput-object v0, Lme/embodied/NetworkUtil;->BAND_SELECT_MAP:[I

    return-void

    nop

    :array_0
    .array-data 4
        0x0
        0x5
        0x18
    .end array-data
.end method

.method public constructor <init>()V
    .locals 0

    .line 22
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static addWifiNetwork(Landroid/content/Context;Ljava/lang/String;Ljava/lang/String;IZZ)Lme/embodied/NetworkUtil$WifiResult;
    .locals 6

    .line 205
    invoke-static {p0, p3}, Lme/embodied/NetworkUtil;->updateWifiBandSelection(Landroid/content/Context;I)V

    .line 206
    const-string p3, "wifi"

    invoke-virtual {p0, p3}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Landroid/net/wifi/WifiManager;

    .line 207
    const/4 p3, 0x1

    invoke-virtual {p0, p3}, Landroid/net/wifi/WifiManager;->setWifiEnabled(Z)Z

    .line 208
    invoke-virtual {p0}, Landroid/net/wifi/WifiManager;->getScanResults()Ljava/util/List;

    move-result-object v0

    const/high16 v1, 0x3f800000    # 1.0f

    invoke-static {p1, v0, v1}, Lme/embodied/NetworkUtil;->get_best_ssid(Ljava/lang/String;Ljava/util/List;F)Lme/embodied/NetworkUtil$WifiResult;

    move-result-object p1

    .line 209
    new-instance v0, Landroid/net/wifi/WifiConfiguration;

    invoke-direct {v0}, Landroid/net/wifi/WifiConfiguration;-><init>()V

    .line 211
    new-array v1, p3, [Ljava/lang/Object;

    iget-object v2, p1, Lme/embodied/NetworkUtil$WifiResult;->ssid_:Ljava/lang/String;

    const/4 v3, 0x0

    aput-object v2, v1, v3

    const-string v2, "\"%s\""

    invoke-static {v2, v1}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    iput-object v1, v0, Landroid/net/wifi/WifiConfiguration;->SSID:Ljava/lang/String;

    .line 212
    iput-boolean p4, v0, Landroid/net/wifi/WifiConfiguration;->hiddenSSID:Z

    .line 213
    new-instance v1, Ljava/util/BitSet;

    invoke-direct {v1}, Ljava/util/BitSet;-><init>()V

    iput-object v1, v0, Landroid/net/wifi/WifiConfiguration;->allowedKeyManagement:Ljava/util/BitSet;

    .line 214
    invoke-virtual {p2}, Ljava/lang/String;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_0

    .line 217
    iget-object p2, v0, Landroid/net/wifi/WifiConfiguration;->allowedKeyManagement:Ljava/util/BitSet;

    invoke-virtual {p2, v3}, Ljava/util/BitSet;->set(I)V

    goto :goto_0

    .line 221
    :cond_0
    iget-object v1, v0, Landroid/net/wifi/WifiConfiguration;->allowedKeyManagement:Ljava/util/BitSet;

    invoke-virtual {v1, p3}, Ljava/util/BitSet;->set(I)V

    .line 223
    new-array v1, p3, [Ljava/lang/Object;

    aput-object p2, v1, v3

    invoke-static {v2, v1}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p2

    iput-object p2, v0, Landroid/net/wifi/WifiConfiguration;->preSharedKey:Ljava/lang/String;

    .line 227
    :goto_0
    invoke-virtual {p0, v0}, Landroid/net/wifi/WifiManager;->addNetwork(Landroid/net/wifi/WifiConfiguration;)I

    move-result p2

    .line 228
    const/4 v0, -0x1

    const/4 v1, 0x4

    const/4 v2, 0x3

    const/4 v4, 0x2

    if-ne p2, v0, :cond_1

    .line 231
    iput v4, p1, Lme/embodied/NetworkUtil$WifiResult;->result_:I

    goto :goto_1

    .line 233
    :cond_1
    if-eqz p5, :cond_4

    .line 236
    invoke-virtual {p0}, Landroid/net/wifi/WifiManager;->disconnect()Z

    .line 237
    invoke-virtual {p0, p2, p3}, Landroid/net/wifi/WifiManager;->enableNetwork(IZ)Z

    move-result p2

    if-nez p2, :cond_2

    .line 239
    iput v2, p1, Lme/embodied/NetworkUtil$WifiResult;->result_:I

    .line 241
    :cond_2
    invoke-virtual {p0}, Landroid/net/wifi/WifiManager;->reconnect()Z

    move-result p0

    if-nez p0, :cond_3

    .line 243
    iput v1, p1, Lme/embodied/NetworkUtil$WifiResult;->result_:I

    goto :goto_1

    .line 247
    :cond_3
    iput v3, p1, Lme/embodied/NetworkUtil$WifiResult;->result_:I

    goto :goto_1

    .line 253
    :cond_4
    iput v3, p1, Lme/embodied/NetworkUtil$WifiResult;->result_:I

    .line 256
    :goto_1
    iget p0, p1, Lme/embodied/NetworkUtil$WifiResult;->result_:I

    const-string p2, "Hidden-"

    const-string v0, ""

    const-string v5, "bo-network-util"

    if-nez p0, :cond_7

    .line 258
    new-array p0, v1, [Ljava/lang/Object;

    if-eqz p5, :cond_5

    const-string p5, "Connected"

    goto :goto_2

    :cond_5
    const-string p5, "Added"

    :goto_2
    aput-object p5, p0, v3

    iget-object p5, p1, Lme/embodied/NetworkUtil$WifiResult;->ssid_:Ljava/lang/String;

    aput-object p5, p0, p3

    if-eqz p4, :cond_6

    goto :goto_3

    :cond_6
    move-object p2, v0

    :goto_3
    aput-object p2, p0, v4

    iget p2, p1, Lme/embodied/NetworkUtil$WifiResult;->found_:I

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p2

    aput-object p2, p0, v2

    const-string p2, "Wifi %s SSID %s (%s%d) successfully."

    invoke-static {p2, p0}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    invoke-static {v5, p0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_6

    .line 262
    :cond_7
    const/4 p0, 0x5

    new-array p0, p0, [Ljava/lang/Object;

    if-eqz p5, :cond_8

    const-string p5, "Connect"

    goto :goto_4

    :cond_8
    const-string p5, "Add"

    :goto_4
    aput-object p5, p0, v3

    iget-object p5, p1, Lme/embodied/NetworkUtil$WifiResult;->ssid_:Ljava/lang/String;

    aput-object p5, p0, p3

    if-eqz p4, :cond_9

    goto :goto_5

    :cond_9
    move-object p2, v0

    :goto_5
    aput-object p2, p0, v4

    iget p2, p1, Lme/embodied/NetworkUtil$WifiResult;->found_:I

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p2

    aput-object p2, p0, v2

    iget p2, p1, Lme/embodied/NetworkUtil$WifiResult;->result_:I

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p2

    aput-object p2, p0, v1

    const-string p2, "Wifi FAILED to %s SSID %s (%s%d). Error: %d"

    invoke-static {p2, p0}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    invoke-static {v5, p0}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 264
    :goto_6
    return-object p1
.end method

.method public static attemptWifiRecovery(Landroid/content/Context;)V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/InterruptedException;
        }
    .end annotation

    .line 55
    const-string v0, "bo-network-util"

    const-string v1, "BO#8011 Wifi Lost.  Attempting to reconnect/rescan."

    invoke-static {v0, v1}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 56
    const-string v1, "wifi"

    invoke-virtual {p0, v1}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Landroid/net/wifi/WifiManager;

    .line 57
    invoke-virtual {p0}, Landroid/net/wifi/WifiManager;->reconnect()Z

    move-result v1

    if-eqz v1, :cond_0

    .line 58
    const-string p0, "BO#8011 Wifi Reconnect successful."

    invoke-static {v0, p0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 59
    return-void

    .line 61
    :cond_0
    const-wide/16 v1, 0x3e8

    invoke-static {v1, v2}, Ljava/lang/Thread;->sleep(J)V

    .line 62
    const-string v1, "BO#8011 Wifi Reconnect failed.  Attempting rescan."

    invoke-static {v0, v1}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 63
    invoke-virtual {p0}, Landroid/net/wifi/WifiManager;->startScan()Z

    .line 64
    return-void
.end method

.method public static backgroundWifiScan(Landroid/content/Context;)V
    .locals 1

    .line 67
    const-string v0, "wifi"

    invoke-virtual {p0, v0}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Landroid/net/wifi/WifiManager;

    .line 68
    invoke-virtual {p0}, Landroid/net/wifi/WifiManager;->startScan()Z

    .line 69
    return-void
.end method

.method public static enableWifi(Landroid/content/Context;Z)V
    .locals 1

    .line 41
    const-string v0, "wifi"

    invoke-virtual {p0, v0}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Landroid/net/wifi/WifiManager;

    .line 42
    invoke-virtual {p0, p1}, Landroid/net/wifi/WifiManager;->setWifiEnabled(Z)Z

    .line 43
    return-void
.end method

.method public static getWifiActiveSSID(Landroid/content/Context;)Ljava/lang/String;
    .locals 2

    .line 46
    const-string v0, "wifi"

    invoke-virtual {p0, v0}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Landroid/net/wifi/WifiManager;

    .line 47
    invoke-virtual {p0}, Landroid/net/wifi/WifiManager;->getConnectionInfo()Landroid/net/wifi/WifiInfo;

    move-result-object p0

    .line 48
    invoke-virtual {p0}, Landroid/net/wifi/WifiInfo;->getSupplicantState()Landroid/net/wifi/SupplicantState;

    move-result-object v0

    sget-object v1, Landroid/net/wifi/SupplicantState;->COMPLETED:Landroid/net/wifi/SupplicantState;

    if-ne v0, v1, :cond_0

    .line 49
    invoke-virtual {p0}, Landroid/net/wifi/WifiInfo;->getSSID()Ljava/lang/String;

    move-result-object p0

    return-object p0

    .line 51
    :cond_0
    const/4 p0, 0x0

    return-object p0
.end method

.method public static get_best_ssid(Ljava/lang/String;Ljava/util/List;F)Lme/embodied/NetworkUtil$WifiResult;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Landroid/net/wifi/ScanResult;",
            ">;F)",
            "Lme/embodied/NetworkUtil$WifiResult;"
        }
    .end annotation

    .line 140
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    move-result v0

    const/4 v1, 0x0

    const/4 v2, 0x2

    if-eqz v0, :cond_0

    .line 143
    new-instance p1, Lme/embodied/NetworkUtil$WifiResult;

    invoke-direct {p1, p0, v2, v1}, Lme/embodied/NetworkUtil$WifiResult;-><init>(Ljava/lang/String;IF)V

    return-object p1

    .line 146
    :cond_0
    nop

    .line 147
    nop

    .line 148
    const v0, 0x7fffffff

    .line 149
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    move-object v3, p0

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    const/4 v5, 0x0

    const/4 v6, 0x1

    if-eqz v4, :cond_3

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Landroid/net/wifi/ScanResult;

    .line 151
    iget-object v7, v4, Landroid/net/wifi/ScanResult;->SSID:Ljava/lang/String;

    invoke-virtual {p0, v7}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v7

    if-eqz v7, :cond_1

    .line 153
    nop

    .line 154
    nop

    .line 155
    move-object v3, p0

    move p1, v5

    goto :goto_1

    .line 157
    :cond_1
    iget-object v5, v4, Landroid/net/wifi/ScanResult;->SSID:Ljava/lang/String;

    invoke-static {p0, v5}, Lme/embodied/NetworkUtil;->get_levenshtein_distance(Ljava/lang/String;Ljava/lang/String;)I

    move-result v5

    .line 158
    if-ge v5, v0, :cond_2

    .line 160
    nop

    .line 161
    iget-object v3, v4, Landroid/net/wifi/ScanResult;->SSID:Ljava/lang/String;

    move v0, v5

    .line 163
    :cond_2
    goto :goto_0

    .line 149
    :cond_3
    move p1, v6

    .line 165
    :goto_1
    if-nez p1, :cond_4

    .line 167
    new-instance p0, Lme/embodied/NetworkUtil$WifiResult;

    invoke-direct {p0, v3, v5, v1}, Lme/embodied/NetworkUtil$WifiResult;-><init>(Ljava/lang/String;IF)V

    return-object p0

    .line 171
    :cond_4
    int-to-float p1, v0

    invoke-virtual {v3}, Ljava/lang/String;->length()I

    move-result v0

    int-to-float v0, v0

    div-float/2addr p1, v0

    .line 172
    cmpg-float p2, p1, p2

    if-gtz p2, :cond_5

    .line 174
    new-instance p0, Lme/embodied/NetworkUtil$WifiResult;

    invoke-direct {p0, v3, v6, p1}, Lme/embodied/NetworkUtil$WifiResult;-><init>(Ljava/lang/String;IF)V

    return-object p0

    .line 178
    :cond_5
    new-instance p2, Lme/embodied/NetworkUtil$WifiResult;

    invoke-direct {p2, p0, v2, p1}, Lme/embodied/NetworkUtil$WifiResult;-><init>(Ljava/lang/String;IF)V

    return-object p2
.end method

.method static get_levenshtein_distance(Ljava/lang/String;Ljava/lang/String;)I
    .locals 13

    .line 73
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result v0

    .line 74
    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result v1

    .line 75
    add-int/lit8 v2, v0, 0x1

    add-int/lit8 v3, v1, 0x1

    const/4 v4, 0x2

    new-array v4, v4, [I

    const/4 v5, 0x1

    aput v3, v4, v5

    const/4 v3, 0x0

    aput v2, v4, v3

    const-class v2, I

    invoke-static {v2, v4}, Ljava/lang/reflect/Array;->newInstance(Ljava/lang/Class;[I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, [[I

    .line 78
    if-nez v0, :cond_0

    .line 80
    return v1

    .line 83
    :cond_0
    if-nez v1, :cond_1

    .line 85
    return v0

    .line 89
    :cond_1
    move v4, v3

    :goto_0
    if-gt v4, v0, :cond_2

    aget-object v6, v2, v4

    add-int/lit8 v7, v4, 0x1

    aput v4, v6, v3

    move v4, v7

    goto :goto_0

    .line 93
    :cond_2
    move v4, v3

    :goto_1
    if-gt v4, v1, :cond_3

    aget-object v6, v2, v3

    add-int/lit8 v7, v4, 0x1

    aput v4, v6, v4

    move v4, v7

    goto :goto_1

    .line 98
    :cond_3
    move v4, v5

    :goto_2
    if-gt v4, v0, :cond_6

    .line 101
    move v6, v5

    :goto_3
    if-gt v6, v1, :cond_5

    .line 104
    add-int/lit8 v7, v6, -0x1

    invoke-virtual {p1, v7}, Ljava/lang/String;->charAt(I)C

    move-result v8

    add-int/lit8 v9, v4, -0x1

    invoke-virtual {p0, v9}, Ljava/lang/String;->charAt(I)C

    move-result v10

    if-ne v8, v10, :cond_4

    move v8, v3

    goto :goto_4

    :cond_4
    move v8, v5

    .line 107
    :goto_4
    aget-object v10, v2, v4

    aget-object v11, v2, v9

    aget v11, v11, v6

    add-int/2addr v11, v5

    aget-object v12, v2, v4

    aget v12, v12, v7

    add-int/2addr v12, v5

    .line 108
    invoke-static {v11, v12}, Ljava/lang/Math;->min(II)I

    move-result v11

    aget-object v9, v2, v9

    aget v7, v9, v7

    add-int/2addr v7, v8

    .line 107
    invoke-static {v11, v7}, Ljava/lang/Math;->min(II)I

    move-result v7

    aput v7, v10, v6

    .line 101
    add-int/lit8 v6, v6, 0x1

    goto :goto_3

    .line 98
    :cond_5
    add-int/lit8 v4, v4, 0x1

    goto :goto_2

    .line 113
    :cond_6
    aget-object p0, v2, v0

    aget p0, p0, v1

    return p0
.end method

.method public static isInternetAvailable()Z
    .locals 2

    .line 33
    :try_start_0
    const-string v0, "embodied.me"

    invoke-static {v0}, Ljava/net/InetAddress;->getByName(Ljava/lang/String;)Ljava/net/InetAddress;

    move-result-object v0

    .line 34
    const-string v1, ""

    invoke-virtual {v0, v1}, Ljava/net/InetAddress;->equals(Ljava/lang/Object;)Z

    move-result v0
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    xor-int/lit8 v0, v0, 0x1

    return v0

    .line 35
    :catch_0
    move-exception v0

    .line 36
    const/4 v0, 0x0

    return v0
.end method

.method public static isNetworkConnected(Landroid/content/Context;)Z
    .locals 1

    .line 26
    const-string v0, "connectivity"

    invoke-virtual {p0, v0}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Landroid/net/ConnectivityManager;

    .line 28
    invoke-virtual {p0}, Landroid/net/ConnectivityManager;->getActiveNetworkInfo()Landroid/net/NetworkInfo;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Landroid/net/ConnectivityManager;->getActiveNetworkInfo()Landroid/net/NetworkInfo;

    move-result-object p0

    invoke-virtual {p0}, Landroid/net/NetworkInfo;->isConnected()Z

    move-result p0

    if-eqz p0, :cond_0

    const/4 p0, 0x1

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    return p0
.end method

.method public static removeAllNetworks(Landroid/content/Context;)Z
    .locals 10

    .line 269
    const-string v0, "wifi"

    invoke-virtual {p0, v0}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Landroid/net/wifi/WifiManager;

    .line 270
    invoke-virtual {p0}, Landroid/net/wifi/WifiManager;->disconnect()Z

    .line 271
    nop

    .line 272
    nop

    .line 273
    invoke-virtual {p0}, Landroid/net/wifi/WifiManager;->getConfiguredNetworks()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    const/4 v1, 0x0

    move v2, v1

    move v3, v2

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    const-string v5, "bo-network-util"

    if-eqz v4, :cond_3

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Landroid/net/wifi/WifiConfiguration;

    .line 275
    add-int/lit8 v2, v2, 0x1

    .line 276
    iget v6, v4, Landroid/net/wifi/WifiConfiguration;->networkId:I

    invoke-virtual {p0, v6}, Landroid/net/wifi/WifiManager;->disableNetwork(I)Z

    move-result v6

    .line 277
    if-nez v6, :cond_0

    .line 278
    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    const-string v8, "Unable to disable network with ssid: "

    invoke-virtual {v7, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v8, v4, Landroid/net/wifi/WifiConfiguration;->SSID:Ljava/lang/String;

    invoke-virtual {v7, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    invoke-static {v5, v7}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 279
    :cond_0
    iget v7, v4, Landroid/net/wifi/WifiConfiguration;->networkId:I

    invoke-virtual {p0, v7}, Landroid/net/wifi/WifiManager;->removeNetwork(I)Z

    move-result v7

    .line 280
    if-nez v6, :cond_1

    .line 281
    new-instance v8, Ljava/lang/StringBuilder;

    invoke-direct {v8}, Ljava/lang/StringBuilder;-><init>()V

    const-string v9, "Unable to remove network with ssid: "

    invoke-virtual {v8, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v4, v4, Landroid/net/wifi/WifiConfiguration;->SSID:Ljava/lang/String;

    invoke-virtual {v8, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v8}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-static {v5, v4}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 282
    :cond_1
    if-eqz v6, :cond_2

    if-eqz v7, :cond_2

    .line 283
    add-int/lit8 v3, v3, 0x1

    .line 284
    :cond_2
    goto :goto_0

    .line 285
    :cond_3
    const/4 p0, 0x2

    new-array p0, p0, [Ljava/lang/Object;

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    aput-object v0, p0, v1

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    const/4 v4, 0x1

    aput-object v0, p0, v4

    const-string v0, "Disconnect from all networks. Tried:%d Removed: %d"

    invoke-static {v0, p0}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    invoke-static {v5, p0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 286
    if-ne v2, v3, :cond_4

    move v1, v4

    :cond_4
    return v1
.end method

.method public static updateWifiBandSelection(Landroid/content/Context;I)V
    .locals 2

    .line 190
    if-ltz p1, :cond_0

    sget-object v0, Lme/embodied/NetworkUtil;->BAND_SELECT_MAP:[I

    array-length v0, v0

    if-lt p1, v0, :cond_1

    .line 192
    :cond_0
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Ignoring invalid band selection: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const-string v0, "bo-network-util"

    invoke-static {v0, p1}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 193
    const/4 p1, 0x0

    .line 195
    :cond_1
    new-instance v0, Landroid/content/Intent;

    invoke-direct {v0}, Landroid/content/Intent;-><init>()V

    .line 196
    const-string v1, "com.embodied.osctrl.wifi_freq_pref"

    invoke-virtual {v0, v1}, Landroid/content/Intent;->setAction(Ljava/lang/String;)Landroid/content/Intent;

    .line 197
    sget-object v1, Lme/embodied/NetworkUtil;->BAND_SELECT_MAP:[I

    aget p1, v1, p1

    const-string v1, "freq"

    invoke-virtual {v0, v1, p1}, Landroid/content/Intent;->putExtra(Ljava/lang/String;I)Landroid/content/Intent;

    .line 198
    invoke-virtual {p0, v0}, Landroid/content/Context;->sendBroadcast(Landroid/content/Intent;)V

    .line 199
    return-void
.end method
