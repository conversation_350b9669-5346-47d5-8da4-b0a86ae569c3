.class public Lme/embodied/VolumeScale;
.super Ljava/lang/Object;
.source "VolumeScale.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lme/embodied/VolumeScale$ScalerKnee25;,
        Lme/embodied/VolumeScale$ScalerTenStep;,
        Lme/embodied/VolumeScale$ScalerBasic;,
        Lme/embodied/VolumeScale$Scaler;,
        Lme/embodied/VolumeScale$Scalers;
    }
.end annotation


# static fields
.field private static final ANDROID_HIGH:I = 0xf

.field private static final ANDROID_LOW:I = 0xa

.field public static VOLUME_CEILING:I

.field public static VOLUME_FLOOR:I


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 23
    const/16 v0, 0xe

    sput v0, Lme/embodied/VolumeScale;->VOLUME_CEILING:I

    .line 24
    const/4 v0, 0x5

    sput v0, Lme/embodied/VolumeScale;->VOLUME_FLOOR:I

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 11
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static UpdateLimits()V
    .locals 1

    .line 47
    const-string v0, "sys_vol_max"

    invoke-static {v0}, Lme/embodied/DeviceSettings;->getIntS(Ljava/lang/String;)I

    move-result v0

    sput v0, Lme/embodied/VolumeScale;->VOLUME_CEILING:I

    .line 48
    add-int/lit8 v0, v0, -0x9

    sput v0, Lme/embodied/VolumeScale;->VOLUME_FLOOR:I

    .line 49
    return-void
.end method

.method static synthetic access$300(III)I
    .locals 0

    .line 11
    invoke-static {p0, p1, p2}, Lme/embodied/VolumeScale;->ensureRange(III)I

    move-result p0

    return p0
.end method

.method public static createScaler(Lme/embodied/VolumeScale$Scalers;)Lme/embodied/VolumeScale$Scaler;
    .locals 2

    .line 34
    invoke-static {}, Lme/embodied/VolumeScale;->UpdateLimits()V

    .line 35
    sget-object v0, Lme/embodied/VolumeScale$1;->$SwitchMap$me$embodied$VolumeScale$Scalers:[I

    invoke-virtual {p0}, Lme/embodied/VolumeScale$Scalers;->ordinal()I

    move-result p0

    aget p0, v0, p0

    const/4 v0, 0x1

    const/4 v1, 0x0

    if-eq p0, v0, :cond_2

    const/4 v0, 0x2

    if-eq p0, v0, :cond_1

    const/4 v0, 0x3

    if-eq p0, v0, :cond_0

    .line 43
    return-object v1

    .line 41
    :cond_0
    new-instance p0, Lme/embodied/VolumeScale$ScalerKnee25;

    invoke-direct {p0, v1}, Lme/embodied/VolumeScale$ScalerKnee25;-><init>(Lme/embodied/VolumeScale$1;)V

    return-object p0

    .line 39
    :cond_1
    new-instance p0, Lme/embodied/VolumeScale$ScalerTenStep;

    invoke-direct {p0, v1}, Lme/embodied/VolumeScale$ScalerTenStep;-><init>(Lme/embodied/VolumeScale$1;)V

    return-object p0

    .line 37
    :cond_2
    new-instance p0, Lme/embodied/VolumeScale$ScalerBasic;

    invoke-direct {p0, v1}, Lme/embodied/VolumeScale$ScalerBasic;-><init>(Lme/embodied/VolumeScale$1;)V

    return-object p0
.end method

.method private static ensureRange(III)I
    .locals 0

    .line 53
    invoke-static {p0, p1}, Ljava/lang/Math;->max(II)I

    move-result p0

    invoke-static {p0, p2}, Ljava/lang/Math;->min(II)I

    move-result p0

    return p0
.end method

.method public static validSystemVolume(I)I
    .locals 2

    .line 58
    if-nez p0, :cond_0

    goto :goto_0

    :cond_0
    sget v0, Lme/embodied/VolumeScale;->VOLUME_FLOOR:I

    sget v1, Lme/embodied/VolumeScale;->VOLUME_CEILING:I

    invoke-static {p0, v0, v1}, Lme/embodied/VolumeScale;->ensureRange(III)I

    move-result p0

    :goto_0
    return p0
.end method
