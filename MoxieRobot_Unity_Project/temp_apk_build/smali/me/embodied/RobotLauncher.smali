.class public Lme/embodied/RobotLauncher;
.super Landroid/app/Activity;
.source "RobotLauncher.java"


# static fields
.field private static final ANDROID_LAUNCHER_COMPONENT:Landroid/content/ComponentName;

.field private static final BACKGROUND_DEV_IMAGE_PATH:Ljava/io/File;

.field private static final BACKGROUND_IMAGE_PATH:Ljava/io/File;

.field public static final DEVELOPER_MODE_FILE:Ljava/io/File;

.field private static final TAG:Ljava/lang/String;


# instance fields
.field developer_mode_:Z


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 37
    new-instance v0, Ljava/io/File;

    const-string v1, "/sdcard/EmbodiedStaticData/PERSISTENT_DATA/dev_mode"

    invoke-direct {v0, v1}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    sput-object v0, Lme/embodied/RobotLauncher;->DEVELOPER_MODE_FILE:Ljava/io/File;

    .line 38
    new-instance v0, Ljava/io/File;

    const-string v1, "/vendor/staticdata/Launcher/dreamface.png"

    invoke-direct {v0, v1}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    sput-object v0, Lme/embodied/RobotLauncher;->BACKGROUND_IMAGE_PATH:Ljava/io/File;

    .line 39
    new-instance v0, Ljava/io/File;

    const-string v1, "/sdcard/EmbodiedStaticData/PERSISTENT_DATA/dreamface-dev.png"

    invoke-direct {v0, v1}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    sput-object v0, Lme/embodied/RobotLauncher;->BACKGROUND_DEV_IMAGE_PATH:Ljava/io/File;

    .line 40
    new-instance v0, Landroid/content/ComponentName;

    const-string v1, "com.android.launcher3"

    const-string v2, "com.android.launcher3.Launcher"

    invoke-direct {v0, v1, v2}, Landroid/content/ComponentName;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    sput-object v0, Lme/embodied/RobotLauncher;->ANDROID_LAUNCHER_COMPONENT:Landroid/content/ComponentName;

    .line 41
    const-class v0, Lme/embodied/RobotLauncher;

    invoke-virtual {v0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v0

    sput-object v0, Lme/embodied/RobotLauncher;->TAG:Ljava/lang/String;

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    .line 45
    invoke-direct {p0}, Landroid/app/Activity;-><init>()V

    .line 46
    sget-object v0, Lme/embodied/RobotLauncher;->DEVELOPER_MODE_FILE:Ljava/io/File;

    invoke-virtual {v0}, Ljava/io/File;->exists()Z

    move-result v0

    iput-boolean v0, p0, Lme/embodied/RobotLauncher;->developer_mode_:Z

    .line 47
    return-void
.end method

.method private StartWatchdog()V
    .locals 2

    .line 83
    sget-object v0, Lme/embodied/RobotLauncher;->TAG:Ljava/lang/String;

    const-string v1, "Requesting Watchdog service to start."

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 84
    new-instance v0, Landroid/content/Intent;

    const-class v1, Lme/embodied/services/ServiceLauncher;

    invoke-direct {v0, p0, v1}, Landroid/content/Intent;-><init>(Landroid/content/Context;Ljava/lang/Class;)V

    .line 85
    invoke-virtual {p0, v0}, Lme/embodied/RobotLauncher;->startForegroundService(Landroid/content/Intent;)Landroid/content/ComponentName;

    .line 86
    return-void
.end method

.method static synthetic access$000(Lme/embodied/RobotLauncher;)V
    .locals 0

    .line 30
    invoke-direct {p0}, Lme/embodied/RobotLauncher;->StartWatchdog()V

    return-void
.end method

.method static synthetic access$100()Landroid/content/ComponentName;
    .locals 1

    .line 30
    sget-object v0, Lme/embodied/RobotLauncher;->ANDROID_LAUNCHER_COMPONENT:Landroid/content/ComponentName;

    return-object v0
.end method

.method private createBackgroundView()Landroid/view/View;
    .locals 5

    .line 89
    iget-boolean v0, p0, Lme/embodied/RobotLauncher;->developer_mode_:Z

    if-eqz v0, :cond_0

    sget-object v0, Lme/embodied/RobotLauncher;->BACKGROUND_DEV_IMAGE_PATH:Ljava/io/File;

    goto :goto_0

    :cond_0
    sget-object v0, Lme/embodied/RobotLauncher;->BACKGROUND_IMAGE_PATH:Ljava/io/File;

    .line 90
    :goto_0
    invoke-virtual {v0}, Ljava/io/File;->exists()Z

    move-result v1

    if-eqz v1, :cond_1

    .line 91
    sget-object v1, Lme/embodied/RobotLauncher;->TAG:Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Loading custom background from: "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 93
    :try_start_0
    new-instance v1, Landroid/widget/ImageView;

    invoke-direct {v1, p0}, Landroid/widget/ImageView;-><init>(Landroid/content/Context;)V

    .line 94
    invoke-virtual {v0}, Ljava/io/File;->getAbsolutePath()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Landroid/graphics/BitmapFactory;->decodeFile(Ljava/lang/String;)Landroid/graphics/Bitmap;

    move-result-object v0

    .line 95
    sget-object v2, Lme/embodied/RobotLauncher;->TAG:Ljava/lang/String;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "Decoded background image of size "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Landroid/graphics/Bitmap;->getWidth()I

    move-result v4

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v4, "x"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Landroid/graphics/Bitmap;->getHeight()I

    move-result v4

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-static {v2, v3}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 96
    invoke-virtual {v1, v0}, Landroid/widget/ImageView;->setImageBitmap(Landroid/graphics/Bitmap;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 97
    return-object v1

    .line 98
    :catch_0
    move-exception v0

    .line 99
    sget-object v1, Lme/embodied/RobotLauncher;->TAG:Ljava/lang/String;

    const-string v2, "Failed to load background image."

    invoke-static {v1, v2, v0}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)I

    .line 101
    goto :goto_1

    .line 103
    :cond_1
    sget-object v1, Lme/embodied/RobotLauncher;->TAG:Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Can\'t load background image.  File does not exist: "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v1, v0}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 105
    :goto_1
    const/4 v0, 0x0

    return-object v0
.end method

.method private initView()V
    .locals 8

    .line 130
    const/4 v0, 0x1

    invoke-virtual {p0, v0}, Lme/embodied/RobotLauncher;->requestWindowFeature(I)Z

    .line 131
    invoke-virtual {p0}, Lme/embodied/RobotLauncher;->getWindow()Landroid/view/Window;

    move-result-object v1

    const/16 v2, 0x400

    invoke-virtual {v1, v2, v2}, Landroid/view/Window;->setFlags(II)V

    .line 133
    new-instance v1, Landroid/widget/FrameLayout;

    invoke-direct {v1, p0}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;)V

    .line 134
    new-instance v2, Landroid/widget/FrameLayout$LayoutParams;

    const/4 v3, -0x1

    const/16 v4, 0x11

    invoke-direct {v2, v3, v3, v4}, Landroid/widget/FrameLayout$LayoutParams;-><init>(III)V

    .line 135
    invoke-virtual {v1, v2}, Landroid/widget/FrameLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 136
    const/high16 v2, -0x1000000

    invoke-virtual {v1, v2}, Landroid/widget/FrameLayout;->setBackgroundColor(I)V

    .line 139
    invoke-direct {p0}, Lme/embodied/RobotLauncher;->createBackgroundView()Landroid/view/View;

    move-result-object v5

    .line 140
    if-eqz v5, :cond_0

    .line 141
    new-instance v2, Landroid/view/ViewGroup$LayoutParams;

    invoke-direct {v2, v3, v3}, Landroid/view/ViewGroup$LayoutParams;-><init>(II)V

    invoke-virtual {v5, v2}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 142
    invoke-virtual {v1, v5}, Landroid/widget/FrameLayout;->addView(Landroid/view/View;)V

    goto :goto_0

    .line 145
    :cond_0
    new-instance v5, Landroid/widget/TextView;

    invoke-direct {v5, p0}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    .line 146
    invoke-virtual {v5, v4}, Landroid/widget/TextView;->setGravity(I)V

    .line 147
    iget-boolean v4, p0, Lme/embodied/RobotLauncher;->developer_mode_:Z

    if-eqz v4, :cond_1

    .line 148
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, "DEVELOPER MODE\nUse \u2af8 to run\nUse \u2261 to access launcher.\n\nTo disable, remove "

    invoke-virtual {v4, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v6, Lme/embodied/RobotLauncher;->DEVELOPER_MODE_FILE:Ljava/io/File;

    invoke-virtual {v4, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v5, v4}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 150
    :cond_1
    invoke-virtual {v5, v3}, Landroid/widget/TextView;->setTextColor(I)V

    .line 151
    invoke-virtual {v5, v2}, Landroid/widget/TextView;->setBackgroundColor(I)V

    .line 152
    new-instance v2, Landroid/view/ViewGroup$LayoutParams;

    invoke-direct {v2, v3, v3}, Landroid/view/ViewGroup$LayoutParams;-><init>(II)V

    invoke-virtual {v5, v2}, Landroid/widget/TextView;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 153
    invoke-virtual {v1, v5}, Landroid/widget/FrameLayout;->addView(Landroid/view/View;)V

    .line 157
    :goto_0
    new-instance v2, Landroid/widget/LinearLayout;

    invoke-direct {v2, p0}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    .line 158
    new-instance v4, Landroid/widget/LinearLayout$LayoutParams;

    const/high16 v5, 0x3f800000    # 1.0f

    invoke-direct {v4, v3, v3, v5}, Landroid/widget/LinearLayout$LayoutParams;-><init>(IIF)V

    .line 160
    invoke-virtual {v2, v4}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 161
    invoke-virtual {v2, v0}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 163
    invoke-virtual {v1, v2}, Landroid/widget/FrameLayout;->addView(Landroid/view/View;)V

    .line 166
    new-instance v0, Landroid/view/View;

    invoke-direct {v0, p0}, Landroid/view/View;-><init>(Landroid/content/Context;)V

    .line 167
    new-instance v4, Landroid/widget/LinearLayout$LayoutParams;

    const/4 v6, -0x2

    invoke-direct {v4, v6, v6, v5}, Landroid/widget/LinearLayout$LayoutParams;-><init>(IIF)V

    invoke-virtual {v0, v4}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 168
    invoke-virtual {v2, v0}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 171
    new-instance v0, Landroid/widget/LinearLayout;

    invoke-direct {v0, p0}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    .line 172
    const/4 v4, 0x0

    invoke-virtual {v0, v4}, Landroid/widget/LinearLayout;->setBackgroundColor(I)V

    .line 173
    new-instance v7, Landroid/widget/LinearLayout$LayoutParams;

    invoke-direct {v7, v3, v6}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    .line 174
    invoke-virtual {v7, v4, v4, v4, v4}, Landroid/widget/LinearLayout$LayoutParams;->setMargins(IIII)V

    .line 175
    invoke-virtual {v0, v7}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 176
    invoke-virtual {v0, v4}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 177
    invoke-virtual {v0, v4, v4, v4, v4}, Landroid/widget/LinearLayout;->setPadding(IIII)V

    .line 178
    invoke-virtual {v2, v0}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 180
    new-instance v2, Landroid/widget/Button;

    invoke-direct {v2, p0}, Landroid/widget/Button;-><init>(Landroid/content/Context;)V

    .line 181
    new-instance v7, Landroid/widget/FrameLayout$LayoutParams;

    invoke-direct {v7, v6, v6}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v2, v7}, Landroid/widget/Button;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 182
    const-string v7, "\u2af8"

    invoke-virtual {v2, v7}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V

    .line 183
    invoke-virtual {v2, v4}, Landroid/widget/Button;->setBackgroundColor(I)V

    .line 184
    invoke-virtual {v2, v3}, Landroid/widget/Button;->setTextColor(I)V

    .line 185
    new-instance v7, Lme/embodied/RobotLauncher$1;

    invoke-direct {v7, p0}, Lme/embodied/RobotLauncher$1;-><init>(Lme/embodied/RobotLauncher;)V

    invoke-virtual {v2, v7}, Landroid/widget/Button;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 192
    invoke-virtual {v0, v2}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 196
    new-instance v2, Landroid/view/View;

    invoke-direct {v2, p0}, Landroid/view/View;-><init>(Landroid/content/Context;)V

    .line 197
    new-instance v7, Landroid/widget/LinearLayout$LayoutParams;

    invoke-direct {v7, v6, v3, v5}, Landroid/widget/LinearLayout$LayoutParams;-><init>(IIF)V

    invoke-virtual {v2, v7}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 198
    invoke-virtual {v0, v2}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 200
    new-instance v2, Landroid/widget/Button;

    invoke-direct {v2, p0}, Landroid/widget/Button;-><init>(Landroid/content/Context;)V

    .line 201
    new-instance v5, Landroid/widget/FrameLayout$LayoutParams;

    invoke-direct {v5, v6, v6}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v2, v5}, Landroid/widget/Button;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 202
    const-string v5, "\u2261"

    invoke-virtual {v2, v5}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V

    .line 203
    invoke-virtual {v2, v4}, Landroid/widget/Button;->setBackgroundColor(I)V

    .line 204
    invoke-virtual {v2, v3}, Landroid/widget/Button;->setTextColor(I)V

    .line 205
    new-instance v3, Lme/embodied/RobotLauncher$2;

    invoke-direct {v3, p0}, Lme/embodied/RobotLauncher$2;-><init>(Lme/embodied/RobotLauncher;)V

    invoke-virtual {v2, v3}, Landroid/widget/Button;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 220
    invoke-virtual {v0, v2}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 222
    invoke-virtual {p0, v1}, Lme/embodied/RobotLauncher;->setContentView(Landroid/view/View;)V

    .line 223
    return-void
.end method


# virtual methods
.method protected onCreate(Landroid/os/Bundle;)V
    .locals 2

    .line 51
    invoke-super {p0, p1}, Landroid/app/Activity;->onCreate(Landroid/os/Bundle;)V

    .line 53
    sget-object p1, Lme/embodied/RobotLauncher;->TAG:Ljava/lang/String;

    const-string v0, "Started."

    invoke-static {p1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 55
    nop

    .line 57
    iget-boolean p1, p0, Lme/embodied/RobotLauncher;->developer_mode_:Z

    const/4 v0, 0x0

    if-eqz p1, :cond_1

    .line 58
    new-instance p1, Lme/embodied/AndroidSystemProps;

    invoke-direct {p1, p0}, Lme/embodied/AndroidSystemProps;-><init>(Landroid/content/Context;)V

    .line 59
    invoke-virtual {p1}, Lme/embodied/AndroidSystemProps;->isDebugEnvironment()Z

    move-result v1

    if-nez v1, :cond_0

    .line 60
    iput-boolean v0, p0, Lme/embodied/RobotLauncher;->developer_mode_:Z

    .line 61
    sget-object p1, Lme/embodied/RobotLauncher;->DEVELOPER_MODE_FILE:Ljava/io/File;

    invoke-virtual {p1}, Ljava/io/File;->delete()Z

    .line 62
    sget-object p1, Lme/embodied/RobotLauncher;->TAG:Ljava/lang/String;

    const-string v1, "BO#9000 DEVELOPER MODE DETECTED IN PRODUCTION BUILD.  DEV_MODE REMOVED."

    invoke-static {p1, v1}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_0

    .line 64
    :cond_0
    invoke-virtual {p1}, Lme/embodied/AndroidSystemProps;->isSilentBoot()Z

    move-result v0

    .line 68
    :cond_1
    :goto_0
    invoke-direct {p0}, Lme/embodied/RobotLauncher;->initView()V

    .line 71
    iget-boolean p1, p0, Lme/embodied/RobotLauncher;->developer_mode_:Z

    if-nez p1, :cond_2

    .line 72
    sget-object p1, Lme/embodied/RobotLauncher;->TAG:Ljava/lang/String;

    const-string v0, "Production mode.  Automatically starting."

    invoke-static {p1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 73
    invoke-direct {p0}, Lme/embodied/RobotLauncher;->StartWatchdog()V

    goto :goto_1

    .line 74
    :cond_2
    if-eqz v0, :cond_3

    .line 75
    sget-object p1, Lme/embodied/RobotLauncher;->TAG:Ljava/lang/String;

    const-string v0, "Develop mode, but silent update active.  Automatically starting."

    invoke-static {p1, v0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 76
    invoke-direct {p0}, Lme/embodied/RobotLauncher;->StartWatchdog()V

    goto :goto_1

    .line 78
    :cond_3
    sget-object p1, Lme/embodied/RobotLauncher;->TAG:Ljava/lang/String;

    const-string v0, "Developer mode.  Manual start required."

    invoke-static {p1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 80
    :goto_1
    return-void
.end method

.method public onKeyUp(ILandroid/view/KeyEvent;)Z
    .locals 0

    .line 111
    const/16 p2, 0x45

    if-ne p1, p2, :cond_0

    .line 112
    sget-object p1, Lme/embodied/RobotLauncher;->TAG:Ljava/lang/String;

    const-string p2, "Wakeword key event detected.  Sending detect intent."

    invoke-static {p1, p2}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 113
    new-instance p1, Landroid/content/Intent;

    invoke-direct {p1}, Landroid/content/Intent;-><init>()V

    .line 114
    const-string p2, "com.embodied.wakeword_detect"

    invoke-virtual {p1, p2}, Landroid/content/Intent;->setAction(Ljava/lang/String;)Landroid/content/Intent;

    .line 115
    invoke-virtual {p0, p1}, Lme/embodied/RobotLauncher;->sendBroadcast(Landroid/content/Intent;)V

    .line 116
    const/4 p1, 0x1

    return p1

    .line 118
    :cond_0
    const/4 p1, 0x0

    return p1
.end method
