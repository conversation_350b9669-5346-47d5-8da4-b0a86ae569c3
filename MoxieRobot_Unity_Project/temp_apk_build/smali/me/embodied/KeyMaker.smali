.class public Lme/embodied/KeyMaker;
.super Ljava/lang/Object;
.source "KeyMaker.java"


# static fields
.field static final KEYROOT:Ljava/io/File;

.field static final KEY_ALGORITHM:Ljava/lang/String; = "RSA"

.field static final KEY_BITS:I = 0x1000

.field static final KEY_PRIV_MIN_SIZE:J = 0xc60L

.field static final KEY_PUB_MIN_SIZE:J = 0x2e0L

.field static final PRIVATE_KEY_FILE:Ljava/io/File;

.field static final PUBLIC_KEY_FILE:Ljava/io/File;

.field private static final TAG:Ljava/lang/String;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 21
    const-class v0, Lme/embodied/KeyMaker;

    invoke-virtual {v0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v0

    sput-object v0, Lme/embodied/KeyMaker;->TAG:Ljava/lang/String;

    .line 22
    new-instance v0, Ljava/io/File;

    const-string v1, "/sdcard/EmbodiedStaticData/PERSISTENT_DATA/rightpoint"

    invoke-direct {v0, v1}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    sput-object v0, Lme/embodied/KeyMaker;->KEYROOT:Ljava/io/File;

    .line 23
    new-instance v0, Ljava/io/File;

    const-string v1, "/sdcard/EmbodiedStaticData/PERSISTENT_DATA/rightpoint/RS256.key.pub"

    invoke-direct {v0, v1}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    sput-object v0, Lme/embodied/KeyMaker;->PUBLIC_KEY_FILE:Ljava/io/File;

    .line 24
    new-instance v0, Ljava/io/File;

    const-string v1, "/sdcard/EmbodiedStaticData/PERSISTENT_DATA/rightpoint/RS256.key"

    invoke-direct {v0, v1}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    sput-object v0, Lme/embodied/KeyMaker;->PRIVATE_KEY_FILE:Ljava/io/File;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 19
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method private static createKeysIfNeeded(Ljava/io/File;Ljava/io/File;)V
    .locals 3

    .line 49
    const-wide/16 v0, 0x2e0

    invoke-static {p0, v0, v1}, Lme/embodied/KeyMaker;->validKeyFile(Ljava/io/File;J)Z

    move-result v0

    .line 50
    const-wide/16 v1, 0xc60

    invoke-static {p1, v1, v2}, Lme/embodied/KeyMaker;->validKeyFile(Ljava/io/File;J)Z

    move-result v1

    .line 52
    if-eqz v0, :cond_0

    if-eqz v1, :cond_0

    .line 53
    sget-object p0, Lme/embodied/KeyMaker;->TAG:Ljava/lang/String;

    const-string p1, "Provisioning keys already generated."

    invoke-static {p0, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 54
    return-void

    .line 55
    :cond_0
    if-nez v0, :cond_1

    if-eqz v1, :cond_2

    .line 56
    :cond_1
    sget-object v0, Lme/embodied/KeyMaker;->TAG:Ljava/lang/String;

    const-string v1, "Partial keys detected.  Generating new keys."

    invoke-static {v0, v1}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 61
    :cond_2
    :try_start_0
    invoke-static {}, Lme/embodied/KeyMaker;->generateKeyPair()Ljava/security/KeyPair;

    move-result-object v0

    .line 63
    invoke-virtual {v0}, Ljava/security/KeyPair;->getPrivate()Ljava/security/PrivateKey;

    move-result-object v1

    const-string v2, "RSA PRIVATE"

    invoke-static {v1, v2, p1}, Lme/embodied/KeyMaker;->writePEM(Ljava/security/Key;Ljava/lang/String;Ljava/io/File;)V

    .line 64
    invoke-virtual {v0}, Ljava/security/KeyPair;->getPublic()Ljava/security/PublicKey;

    move-result-object p1

    const-string v0, "PUBLIC"

    invoke-static {p1, v0, p0}, Lme/embodied/KeyMaker;->writePEM(Ljava/security/Key;Ljava/lang/String;Ljava/io/File;)V

    .line 65
    sget-object p0, Lme/embodied/KeyMaker;->TAG:Ljava/lang/String;

    const-string p1, "Generated and saved new provisioning keys."

    invoke-static {p0, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 68
    goto :goto_0

    .line 66
    :catch_0
    move-exception p0

    .line 67
    sget-object p1, Lme/embodied/KeyMaker;->TAG:Ljava/lang/String;

    const-string v0, "Failed to generate provisioning keys."

    invoke-static {p1, v0, p0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)I

    .line 69
    :goto_0
    return-void
.end method

.method private static generateKeyPair()Ljava/security/KeyPair;
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/security/GeneralSecurityException;
        }
    .end annotation

    .line 82
    const-string v0, "RSA"

    invoke-static {v0}, Ljava/security/KeyPairGenerator;->getInstance(Ljava/lang/String;)Ljava/security/KeyPairGenerator;

    move-result-object v0

    .line 83
    new-instance v1, Ljava/security/SecureRandom;

    invoke-direct {v1}, Ljava/security/SecureRandom;-><init>()V

    const/16 v2, 0x1000

    invoke-virtual {v0, v2, v1}, Ljava/security/KeyPairGenerator;->initialize(ILjava/security/SecureRandom;)V

    .line 84
    invoke-virtual {v0}, Ljava/security/KeyPairGenerator;->generateKeyPair()Ljava/security/KeyPair;

    move-result-object v0

    return-object v0
.end method

.method public static provisionKeysCheck()V
    .locals 2

    .line 37
    sget-object v0, Lme/embodied/KeyMaker;->KEYROOT:Ljava/io/File;

    invoke-virtual {v0}, Ljava/io/File;->exists()Z

    move-result v0

    if-eqz v0, :cond_0

    sget-object v0, Lme/embodied/KeyMaker;->KEYROOT:Ljava/io/File;

    invoke-virtual {v0}, Ljava/io/File;->isDirectory()Z

    move-result v0

    if-nez v0, :cond_1

    :cond_0
    sget-object v0, Lme/embodied/KeyMaker;->KEYROOT:Ljava/io/File;

    invoke-virtual {v0}, Ljava/io/File;->mkdirs()Z

    move-result v0

    if-eqz v0, :cond_2

    .line 38
    :cond_1
    sget-object v0, Lme/embodied/KeyMaker;->PUBLIC_KEY_FILE:Ljava/io/File;

    sget-object v1, Lme/embodied/KeyMaker;->PRIVATE_KEY_FILE:Ljava/io/File;

    invoke-static {v0, v1}, Lme/embodied/KeyMaker;->createKeysIfNeeded(Ljava/io/File;Ljava/io/File;)V

    goto :goto_0

    .line 40
    :cond_2
    sget-object v0, Lme/embodied/KeyMaker;->TAG:Ljava/lang/String;

    const-string v1, "Failed to locate or create key root directory.  Cannot bootstrap keys."

    invoke-static {v0, v1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 42
    :goto_0
    return-void
.end method

.method private static validKeyFile(Ljava/io/File;J)Z
    .locals 2

    .line 45
    invoke-virtual {p0}, Ljava/io/File;->exists()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Ljava/io/File;->length()J

    move-result-wide v0

    cmp-long p0, v0, p1

    if-ltz p0, :cond_0

    const/4 p0, 0x1

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    return p0
.end method

.method private static writePEM(Ljava/security/Key;Ljava/lang/String;Ljava/io/File;)V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 72
    const-string v0, " KEY-----\n"

    invoke-static {}, Ljava/util/Base64;->getEncoder()Ljava/util/Base64$Encoder;

    move-result-object v1

    .line 73
    new-instance v2, Ljava/io/FileWriter;

    invoke-direct {v2, p2}, Ljava/io/FileWriter;-><init>(Ljava/io/File;)V

    .line 74
    :try_start_0
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "-----BEGIN "

    invoke-virtual {p2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {v2, p2}, Ljava/io/FileWriter;->write(Ljava/lang/String;)V

    .line 75
    invoke-interface {p0}, Ljava/security/Key;->getEncoded()[B

    move-result-object p0

    invoke-virtual {v1, p0}, Ljava/util/Base64$Encoder;->encodeToString([B)Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v2, p0}, Ljava/io/FileWriter;->write(Ljava/lang/String;)V

    .line 76
    new-instance p0, Ljava/lang/StringBuilder;

    invoke-direct {p0}, Ljava/lang/StringBuilder;-><init>()V

    const-string p2, "\n-----END "

    invoke-virtual {p0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p0, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v2, p0}, Ljava/io/FileWriter;->write(Ljava/lang/String;)V

    .line 77
    invoke-virtual {v2}, Ljava/io/FileWriter;->close()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 78
    invoke-virtual {v2}, Ljava/io/FileWriter;->close()V

    .line 79
    return-void

    .line 73
    :catchall_0
    move-exception p0

    :try_start_1
    throw p0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    .line 78
    :catchall_1
    move-exception p1

    :try_start_2
    invoke-virtual {v2}, Ljava/io/FileWriter;->close()V
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_2

    goto :goto_0

    :catchall_2
    move-exception p2

    invoke-virtual {p0, p2}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    :goto_0
    throw p1
.end method
