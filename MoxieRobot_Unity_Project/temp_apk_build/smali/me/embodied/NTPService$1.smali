.class Lme/embodied/NTPService$1;
.super Ljava/lang/Thread;
.source "NTPService.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lme/embodied/NTPService;->onNetworkConnect()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lme/embodied/NTPService;


# direct methods
.method constructor <init>(Lme/embodied/NTPService;)V
    .locals 0

    .line 52
    iput-object p1, p0, Lme/embodied/NTPService$1;->this$0:Lme/embodied/NTPService;

    invoke-direct {p0}, Ljava/lang/Thread;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 1

    .line 54
    iget-object v0, p0, Lme/embodied/NTPService$1;->this$0:Lme/embodied/NTPService;

    invoke-static {v0}, Lme/embodied/NTPService;->access$000(Lme/embodied/NTPService;)V

    .line 55
    return-void
.end method
