.class Lme/embodied/RobotLauncher$1;
.super Ljava/lang/Object;
.source "RobotLauncher.java"

# interfaces
.implements Landroid/view/View$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lme/embodied/RobotLauncher;->initView()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lme/embodied/RobotLauncher;


# direct methods
.method constructor <init>(Lme/embodied/RobotLauncher;)V
    .locals 0

    .line 185
    iput-object p1, p0, Lme/embodied/RobotLauncher$1;->this$0:Lme/embodied/RobotLauncher;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onClick(Landroid/view/View;)V
    .locals 0

    .line 188
    iget-object p1, p0, Lme/embodied/RobotLauncher$1;->this$0:Lme/embodied/RobotLauncher;

    invoke-static {p1}, Lme/embodied/RobotLauncher;->access$000(Lme/embodied/RobotLauncher;)V

    .line 189
    return-void
.end method
