.class public final enum Lme/embodied/VolumeScale$Scalers;
.super Ljava/lang/Enum;
.source "VolumeScale.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lme/embodied/VolumeScale;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4019
    name = "Scalers"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lme/embodied/VolumeScale$Scalers;",
        ">;"
    }
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lme/embodied/VolumeScale$Scalers;

.field public static final enum SCALER_KNEE:Lme/embodied/VolumeScale$Scalers;

.field public static final enum SCALER_LINEAR:Lme/embodied/VolumeScale$Scalers;

.field public static final enum SCALER_TENSTEP:Lme/embodied/VolumeScale$Scalers;


# direct methods
.method static constructor <clinit>()V
    .locals 6

    .line 14
    new-instance v0, Lme/embodied/VolumeScale$Scalers;

    const-string v1, "SCALER_LINEAR"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lme/embodied/VolumeScale$Scalers;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lme/embodied/VolumeScale$Scalers;->SCALER_LINEAR:Lme/embodied/VolumeScale$Scalers;

    .line 15
    new-instance v0, Lme/embodied/VolumeScale$Scalers;

    const-string v1, "SCALER_TENSTEP"

    const/4 v3, 0x1

    invoke-direct {v0, v1, v3}, Lme/embodied/VolumeScale$Scalers;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lme/embodied/VolumeScale$Scalers;->SCALER_TENSTEP:Lme/embodied/VolumeScale$Scalers;

    .line 16
    new-instance v0, Lme/embodied/VolumeScale$Scalers;

    const-string v1, "SCALER_KNEE"

    const/4 v4, 0x2

    invoke-direct {v0, v1, v4}, Lme/embodied/VolumeScale$Scalers;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lme/embodied/VolumeScale$Scalers;->SCALER_KNEE:Lme/embodied/VolumeScale$Scalers;

    .line 13
    const/4 v1, 0x3

    new-array v1, v1, [Lme/embodied/VolumeScale$Scalers;

    sget-object v5, Lme/embodied/VolumeScale$Scalers;->SCALER_LINEAR:Lme/embodied/VolumeScale$Scalers;

    aput-object v5, v1, v2

    sget-object v2, Lme/embodied/VolumeScale$Scalers;->SCALER_TENSTEP:Lme/embodied/VolumeScale$Scalers;

    aput-object v2, v1, v3

    aput-object v0, v1, v4

    sput-object v1, Lme/embodied/VolumeScale$Scalers;->$VALUES:[Lme/embodied/VolumeScale$Scalers;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 13
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lme/embodied/VolumeScale$Scalers;
    .locals 1

    .line 13
    const-class v0, Lme/embodied/VolumeScale$Scalers;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lme/embodied/VolumeScale$Scalers;

    return-object p0
.end method

.method public static values()[Lme/embodied/VolumeScale$Scalers;
    .locals 1

    .line 13
    sget-object v0, Lme/embodied/VolumeScale$Scalers;->$VALUES:[Lme/embodied/VolumeScale$Scalers;

    invoke-virtual {v0}, [Lme/embodied/VolumeScale$Scalers;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lme/embodied/VolumeScale$Scalers;

    return-object v0
.end method
