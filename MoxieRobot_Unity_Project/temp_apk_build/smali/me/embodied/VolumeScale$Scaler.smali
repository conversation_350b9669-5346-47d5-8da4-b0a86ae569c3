.class public interface abstract Lme/embodied/VolumeScale$Scaler;
.super Ljava/lang/Object;
.source "VolumeScale.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lme/embodied/VolumeScale;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "Scaler"
.end annotation


# virtual methods
.method public abstract percentToSystem(I)I
.end method

.method public abstract systemToPercent(I)I
.end method
