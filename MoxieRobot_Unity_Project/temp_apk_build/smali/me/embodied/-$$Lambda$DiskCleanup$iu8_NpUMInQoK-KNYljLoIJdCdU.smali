.class public final synthetic Lme/embodied/-$$Lambda$DiskCleanup$iu8_NpUMInQoK-KNYljLoIJdCdU;
.super Ljava/lang/Object;
.source "lambda"

# interfaces
.implements Ljava/util/Comparator;


# static fields
.field public static final synthetic INSTANCE:Lme/embodied/-$$Lambda$DiskCleanup$iu8_NpUMInQoK-KNYljLoIJdCdU;


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Lme/embodied/-$$Lambda$DiskCleanup$iu8_NpUMInQoK-KNYljLoIJdCdU;

    invoke-direct {v0}, Lme/embodied/-$$Lambda$DiskCleanup$iu8_NpUMInQoK-KNYljLoIJdCdU;-><init>()V

    sput-object v0, Lme/embodied/-$$Lambda$DiskCleanup$iu8_NpUMInQoK-KNYljLoIJdCdU;->INSTANCE:Lme/embodied/-$$Lambda$DiskCleanup$iu8_NpUMInQoK-KNYljLoIJdCdU;

    return-void
.end method

.method private synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final compare(Ljava/lang/Object;Ljava/lang/Object;)I
    .locals 0

    check-cast p1, Ljava/io/File;

    check-cast p2, Ljava/io/File;

    invoke-static {p1, p2}, Lme/embodied/DiskCleanup;->lambda$ensureFreeSpace$1(Ljava/io/File;Ljava/io/File;)I

    move-result p1

    return p1
.end method
