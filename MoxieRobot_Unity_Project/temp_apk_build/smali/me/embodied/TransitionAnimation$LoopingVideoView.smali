.class public Lme/embodied/TransitionAnimation$LoopingVideoView;
.super Landroid/view/TextureView;
.source "TransitionAnimation.java"

# interfaces
.implements Landroid/view/TextureView$SurfaceTextureListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lme/embodied/TransitionAnimation;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = "LoopingVideoView"
.end annotation


# instance fields
.field private mediaPlayer_:Landroid/media/MediaPlayer;

.field private source_:Ljava/lang/String;

.field final synthetic this$0:Lme/embodied/TransitionAnimation;


# direct methods
.method public constructor <init>(Lme/embodied/TransitionAnimation;Landroid/content/Context;Ljava/lang/String;)V
    .locals 1

    .line 120
    iput-object p1, p0, Lme/embodied/TransitionAnimation$LoopingVideoView;->this$0:Lme/embodied/TransitionAnimation;

    .line 121
    const/4 p1, 0x0

    const/4 v0, 0x0

    invoke-direct {p0, p2, p1, v0}, Landroid/view/TextureView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 122
    invoke-virtual {p0, p0}, Lme/embodied/TransitionAnimation$LoopingVideoView;->setSurfaceTextureListener(Landroid/view/TextureView$SurfaceTextureListener;)V

    .line 123
    iput-object p3, p0, Lme/embodied/TransitionAnimation$LoopingVideoView;->source_:Ljava/lang/String;

    .line 124
    return-void
.end method


# virtual methods
.method protected onDetachedFromWindow()V
    .locals 2

    .line 141
    iget-object v0, p0, Lme/embodied/TransitionAnimation$LoopingVideoView;->mediaPlayer_:Landroid/media/MediaPlayer;

    if-eqz v0, :cond_0

    .line 142
    invoke-virtual {v0}, Landroid/media/MediaPlayer;->release()V

    .line 143
    const/4 v0, 0x0

    iput-object v0, p0, Lme/embodied/TransitionAnimation$LoopingVideoView;->mediaPlayer_:Landroid/media/MediaPlayer;

    .line 144
    const-string v0, "TransitionAnimation"

    const-string v1, "Released media player"

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 146
    :cond_0
    invoke-super {p0}, Landroid/view/TextureView;->onDetachedFromWindow()V

    .line 147
    return-void
.end method

.method public onSurfaceTextureAvailable(Landroid/graphics/SurfaceTexture;II)V
    .locals 0

    .line 154
    new-instance p2, Landroid/view/Surface;

    invoke-direct {p2, p1}, Landroid/view/Surface;-><init>(Landroid/graphics/SurfaceTexture;)V

    .line 156
    :try_start_0
    const-string p1, "TransitionAnimation"

    const-string p3, "Created media player"

    invoke-static {p1, p3}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 157
    new-instance p1, Landroid/media/MediaPlayer;

    invoke-direct {p1}, Landroid/media/MediaPlayer;-><init>()V

    iput-object p1, p0, Lme/embodied/TransitionAnimation$LoopingVideoView;->mediaPlayer_:Landroid/media/MediaPlayer;

    .line 158
    const/4 p3, 0x1

    invoke-virtual {p1, p3}, Landroid/media/MediaPlayer;->setLooping(Z)V

    .line 159
    iget-object p1, p0, Lme/embodied/TransitionAnimation$LoopingVideoView;->mediaPlayer_:Landroid/media/MediaPlayer;

    iget-object p3, p0, Lme/embodied/TransitionAnimation$LoopingVideoView;->source_:Ljava/lang/String;

    invoke-virtual {p1, p3}, Landroid/media/MediaPlayer;->setDataSource(Ljava/lang/String;)V

    .line 160
    iget-object p1, p0, Lme/embodied/TransitionAnimation$LoopingVideoView;->mediaPlayer_:Landroid/media/MediaPlayer;

    invoke-virtual {p1, p2}, Landroid/media/MediaPlayer;->setSurface(Landroid/view/Surface;)V

    .line 161
    iget-object p1, p0, Lme/embodied/TransitionAnimation$LoopingVideoView;->mediaPlayer_:Landroid/media/MediaPlayer;

    invoke-virtual {p1}, Landroid/media/MediaPlayer;->prepare()V

    .line 162
    iget-object p1, p0, Lme/embodied/TransitionAnimation$LoopingVideoView;->mediaPlayer_:Landroid/media/MediaPlayer;

    invoke-virtual {p1}, Landroid/media/MediaPlayer;->start()V
    :try_end_0
    .catch Ljava/lang/IllegalArgumentException; {:try_start_0 .. :try_end_0} :catch_3
    .catch Ljava/lang/SecurityException; {:try_start_0 .. :try_end_0} :catch_2
    .catch Ljava/lang/IllegalStateException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    .line 170
    :catch_0
    move-exception p1

    .line 171
    invoke-virtual {p1}, Ljava/io/IOException;->printStackTrace()V

    goto :goto_1

    .line 167
    :catch_1
    move-exception p1

    .line 168
    invoke-virtual {p1}, Ljava/lang/IllegalStateException;->printStackTrace()V

    .line 169
    iget-object p1, p0, Lme/embodied/TransitionAnimation$LoopingVideoView;->mediaPlayer_:Landroid/media/MediaPlayer;

    invoke-virtual {p1}, Landroid/media/MediaPlayer;->reset()V

    goto :goto_0

    .line 165
    :catch_2
    move-exception p1

    .line 166
    invoke-virtual {p1}, Ljava/lang/SecurityException;->printStackTrace()V

    goto :goto_0

    .line 163
    :catch_3
    move-exception p1

    .line 164
    invoke-virtual {p1}, Ljava/lang/IllegalArgumentException;->printStackTrace()V

    .line 172
    :goto_0
    nop

    .line 173
    :goto_1
    return-void
.end method

.method public onSurfaceTextureDestroyed(Landroid/graphics/SurfaceTexture;)Z
    .locals 2

    .line 180
    const-string v0, "TransitionAnimation"

    const-string v1, "Surface Texture Destroyed"

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 181
    invoke-virtual {p1}, Landroid/graphics/SurfaceTexture;->release()V

    .line 182
    const/4 p1, 0x1

    return p1
.end method

.method public onSurfaceTextureSizeChanged(Landroid/graphics/SurfaceTexture;II)V
    .locals 0

    .line 176
    return-void
.end method

.method public onSurfaceTextureUpdated(Landroid/graphics/SurfaceTexture;)V
    .locals 0

    .line 186
    return-void
.end method

.method public pausePlayback()V
    .locals 1

    .line 127
    iget-object v0, p0, Lme/embodied/TransitionAnimation$LoopingVideoView;->mediaPlayer_:Landroid/media/MediaPlayer;

    if-eqz v0, :cond_0

    .line 128
    invoke-virtual {v0}, Landroid/media/MediaPlayer;->pause()V

    .line 130
    :cond_0
    return-void
.end method

.method public resumePlayback()V
    .locals 1

    .line 133
    iget-object v0, p0, Lme/embodied/TransitionAnimation$LoopingVideoView;->mediaPlayer_:Landroid/media/MediaPlayer;

    if-eqz v0, :cond_0

    .line 134
    invoke-virtual {v0}, Landroid/media/MediaPlayer;->start()V

    .line 136
    :cond_0
    return-void
.end method
