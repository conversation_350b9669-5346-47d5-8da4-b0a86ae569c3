.class public Lme/embodied/AndroidSystemProps;
.super Ljava/lang/Object;
.source "AndroidSystemProps.java"


# static fields
.field public static final OS_REV_2_5:I = 0x5014

.field public static final OS_REV_2_6:I = 0x5078

.field public static final OS_REV_3_2:I = 0x75f8

.field public static final OS_REV_UNKNOWN:I = 0x7fffffff

.field private static final TAG:Ljava/lang/String;

.field private static final TARGET_CLASS:Ljava/lang/String; = "android.os.SystemProperties"


# instance fields
.field private getMethod_:Ljava/lang/reflect/Method;

.field private has_adb_:Z

.field private min_ota_os_rev_:I

.field private os_rev_:I

.field private proxyClass_:Ljava/lang/Class;

.field private silent_boot_:Z


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 27
    const-class v0, Lme/embodied/AndroidSystemProps;

    invoke-virtual {v0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v0

    sput-object v0, Lme/embodied/AndroidSystemProps;->TAG:Ljava/lang/String;

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 3

    .line 37
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 39
    :try_start_0
    invoke-virtual {p1}, Landroid/content/Context;->getClassLoader()Ljava/lang/ClassLoader;

    move-result-object p1

    .line 40
    const-string v0, "android.os.SystemProperties"

    invoke-virtual {p1, v0}, Ljava/lang/ClassLoader;->loadClass(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object p1

    iput-object p1, p0, Lme/embodied/AndroidSystemProps;->proxyClass_:Ljava/lang/Class;

    .line 41
    const/4 v0, 0x2

    new-array v0, v0, [Ljava/lang/Class;

    .line 42
    const/4 v1, 0x0

    const-class v2, Ljava/lang/String;

    aput-object v2, v0, v1

    .line 43
    const/4 v1, 0x1

    const-class v2, Ljava/lang/String;

    aput-object v2, v0, v1

    .line 45
    const-string v1, "get"

    invoke-virtual {p1, v1, v0}, Ljava/lang/Class;->getMethod(Ljava/lang/String;[Ljava/lang/Class;)Ljava/lang/reflect/Method;

    move-result-object p1

    iput-object p1, p0, Lme/embodied/AndroidSystemProps;->getMethod_:Ljava/lang/reflect/Method;
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 48
    goto :goto_0

    .line 46
    :catch_0
    move-exception p1

    .line 47
    sget-object v0, Lme/embodied/AndroidSystemProps;->TAG:Ljava/lang/String;

    const-string v1, "Can\'t read android properties.  Failed to get android.os.SystemProperties"

    invoke-static {v0, v1, p1}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)I

    .line 49
    :goto_0
    invoke-direct {p0}, Lme/embodied/AndroidSystemProps;->readOSRevision()V

    .line 50
    invoke-direct {p0}, Lme/embodied/AndroidSystemProps;->readMinOTAOSRevision()V

    .line 51
    invoke-direct {p0}, Lme/embodied/AndroidSystemProps;->readADBState()V

    .line 52
    invoke-direct {p0}, Lme/embodied/AndroidSystemProps;->readBootState()V

    .line 53
    return-void
.end method

.method public static ParseOSVersion(Ljava/lang/String;)I
    .locals 3

    .line 74
    nop

    .line 76
    const-string v0, "v(\\d+)\\.(\\d+)(\\.(\\d+))?(.*)"

    invoke-static {v0}, Ljava/util/regex/Pattern;->compile(Ljava/lang/String;)Ljava/util/regex/Pattern;

    move-result-object v0

    .line 77
    invoke-virtual {v0, p0}, Ljava/util/regex/Pattern;->matcher(Ljava/lang/CharSequence;)Ljava/util/regex/Matcher;

    move-result-object p0

    .line 78
    invoke-virtual {p0}, Ljava/util/regex/Matcher;->matches()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 79
    const/4 v0, 0x1

    invoke-virtual {p0, v0}, Ljava/util/regex/Matcher;->group(I)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v0

    mul-int/lit16 v0, v0, 0x2710

    const/4 v1, 0x2

    invoke-virtual {p0, v1}, Ljava/util/regex/Matcher;->group(I)Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v1

    mul-int/lit8 v1, v1, 0x64

    add-int/2addr v0, v1

    .line 80
    const/4 v1, 0x4

    invoke-virtual {p0, v1}, Ljava/util/regex/Matcher;->group(I)Ljava/lang/String;

    move-result-object v2

    if-eqz v2, :cond_1

    .line 81
    invoke-virtual {p0, v1}, Ljava/util/regex/Matcher;->group(I)Ljava/lang/String;

    move-result-object p0

    invoke-static {p0}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result p0

    add-int/2addr v0, p0

    goto :goto_0

    .line 78
    :cond_0
    const/4 v0, 0x0

    .line 84
    :cond_1
    :goto_0
    return v0
.end method

.method private readADBState()V
    .locals 2

    .line 109
    const-string v0, "ro.build.type"

    const/4 v1, 0x0

    invoke-virtual {p0, v0, v1}, Lme/embodied/AndroidSystemProps;->getProperty(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    .line 110
    if-eqz v0, :cond_0

    const-string v1, "user"

    invoke-virtual {v0, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 111
    const/4 v0, 0x0

    iput-boolean v0, p0, Lme/embodied/AndroidSystemProps;->has_adb_:Z

    goto :goto_0

    .line 113
    :cond_0
    const/4 v0, 0x1

    iput-boolean v0, p0, Lme/embodied/AndroidSystemProps;->has_adb_:Z

    .line 115
    :goto_0
    return-void
.end method

.method private readBootState()V
    .locals 5

    .line 119
    const-string v0, "sys.embodied.quiet_boot_done"

    const/4 v1, 0x0

    invoke-virtual {p0, v0, v1}, Lme/embodied/AndroidSystemProps;->getProperty(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    .line 120
    const/4 v2, 0x0

    if-eqz v0, :cond_0

    const-string v3, "1"

    invoke-virtual {v0, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 121
    sget-object v0, Lme/embodied/AndroidSystemProps;->TAG:Ljava/lang/String;

    const-string v1, "Silent boot already complete."

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 122
    iput-boolean v2, p0, Lme/embodied/AndroidSystemProps;->silent_boot_:Z

    .line 123
    return-void

    .line 126
    :cond_0
    const-string v0, "sys.embodied.boot_reason"

    invoke-virtual {p0, v0, v1}, Lme/embodied/AndroidSystemProps;->getProperty(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    .line 127
    sget-object v1, Lme/embodied/AndroidSystemProps;->TAG:Ljava/lang/String;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "Read boot property: "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-static {v1, v3}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 128
    if-eqz v0, :cond_1

    const-string v1, "quiet"

    invoke-virtual {v0, v1}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 129
    sget-object v0, Lme/embodied/AndroidSystemProps;->TAG:Ljava/lang/String;

    const-string v1, "Silent Boot is TRUE"

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 130
    const/4 v0, 0x1

    iput-boolean v0, p0, Lme/embodied/AndroidSystemProps;->silent_boot_:Z

    goto :goto_0

    .line 132
    :cond_1
    sget-object v0, Lme/embodied/AndroidSystemProps;->TAG:Ljava/lang/String;

    const-string v1, "Silent Boot is FALSE"

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 133
    iput-boolean v2, p0, Lme/embodied/AndroidSystemProps;->silent_boot_:Z

    .line 135
    :goto_0
    return-void
.end method

.method private readMinOTAOSRevision()V
    .locals 2

    .line 100
    const-string v0, "sys.embodied.min_ota_os_version"

    const/4 v1, 0x0

    invoke-virtual {p0, v0, v1}, Lme/embodied/AndroidSystemProps;->getProperty(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    .line 101
    if-nez v0, :cond_0

    .line 102
    const/4 v0, 0x0

    iput v0, p0, Lme/embodied/AndroidSystemProps;->min_ota_os_rev_:I

    goto :goto_0

    .line 104
    :cond_0
    invoke-static {v0}, Lme/embodied/AndroidSystemProps;->ParseOSVersion(Ljava/lang/String;)I

    move-result v0

    iput v0, p0, Lme/embodied/AndroidSystemProps;->min_ota_os_rev_:I

    .line 106
    :goto_0
    return-void
.end method

.method private readOSRevision()V
    .locals 2

    .line 88
    const-string v0, "sys.embodied.version"

    const/4 v1, 0x0

    invoke-virtual {p0, v0, v1}, Lme/embodied/AndroidSystemProps;->getProperty(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    .line 89
    const v1, 0x7fffffff

    if-nez v0, :cond_0

    .line 90
    iput v1, p0, Lme/embodied/AndroidSystemProps;->os_rev_:I

    goto :goto_0

    .line 92
    :cond_0
    invoke-static {v0}, Lme/embodied/AndroidSystemProps;->ParseOSVersion(Ljava/lang/String;)I

    move-result v0

    iput v0, p0, Lme/embodied/AndroidSystemProps;->os_rev_:I

    .line 93
    if-nez v0, :cond_1

    .line 94
    iput v1, p0, Lme/embodied/AndroidSystemProps;->os_rev_:I

    .line 97
    :cond_1
    :goto_0
    return-void
.end method


# virtual methods
.method public getMinOTAOSRevision()I
    .locals 1

    .line 142
    iget v0, p0, Lme/embodied/AndroidSystemProps;->min_ota_os_rev_:I

    return v0
.end method

.method public getOSRevision()I
    .locals 1

    .line 138
    iget v0, p0, Lme/embodied/AndroidSystemProps;->os_rev_:I

    return v0
.end method

.method public getProperty(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
    .locals 3

    .line 56
    iget-object v0, p0, Lme/embodied/AndroidSystemProps;->getMethod_:Ljava/lang/reflect/Method;

    if-nez v0, :cond_0

    .line 57
    return-object p2

    .line 59
    :cond_0
    const/4 v1, 0x2

    new-array v1, v1, [Ljava/lang/Object;

    .line 60
    const/4 v2, 0x0

    aput-object p1, v1, v2

    .line 61
    const/4 p1, 0x1

    aput-object p2, v1, p1

    .line 63
    const/4 p1, 0x0

    .line 65
    :try_start_0
    iget-object v2, p0, Lme/embodied/AndroidSystemProps;->proxyClass_:Ljava/lang/Class;

    invoke-virtual {v0, v2, v1}, Ljava/lang/reflect/Method;->invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/String;
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 68
    move-object p1, v0

    goto :goto_0

    .line 66
    :catch_0
    move-exception v0

    .line 67
    sget-object v1, Lme/embodied/AndroidSystemProps;->TAG:Ljava/lang/String;

    const-string v2, "Failed to getProperty"

    invoke-static {v1, v2, v0}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)I

    .line 69
    :goto_0
    if-eqz p1, :cond_1

    move-object p2, p1

    :cond_1
    return-object p2
.end method

.method public isDebugEnvironment()Z
    .locals 1

    .line 146
    iget-boolean v0, p0, Lme/embodied/AndroidSystemProps;->has_adb_:Z

    return v0
.end method

.method public isSilentBoot()Z
    .locals 1

    .line 150
    iget-boolean v0, p0, Lme/embodied/AndroidSystemProps;->silent_boot_:Z

    return v0
.end method
