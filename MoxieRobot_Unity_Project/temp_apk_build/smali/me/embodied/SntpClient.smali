.class public Lme/embodied/SntpClient;
.super Ljava/lang/Object;
.source "SntpClient.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lme/embodied/SntpClient$InvalidServerReplyException;
    }
.end annotation


# static fields
.field private static final DBG:Z = true

.field private static final NTP_LEAP_NOSYNC:I = 0x3

.field private static final NTP_MODE_BROADCAST:I = 0x5

.field private static final NTP_MODE_CLIENT:I = 0x3

.field private static final NTP_MODE_SERVER:I = 0x4

.field private static final NTP_PACKET_SIZE:I = 0x30

.field private static final NTP_PORT:I = 0x7b

.field private static final NTP_STRATUM_DEATH:I = 0x0

.field private static final NTP_STRATUM_MAX:I = 0xf

.field private static final NTP_VERSION:I = 0x3

.field private static final OFFSET_1900_TO_1970:J = 0x83aa7e80L

.field private static final ORIGINATE_TIME_OFFSET:I = 0x18

.field private static final RECEIVE_TIME_OFFSET:I = 0x20

.field private static final REFERENCE_TIME_OFFSET:I = 0x10

.field private static final TAG:Ljava/lang/String; = "EBSntpClient"

.field private static final TRANSMIT_TIME_OFFSET:I = 0x28


# instance fields
.field private mNtpTime:J

.field private mNtpTimeReference:J

.field private mRoundTripTime:J


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 42
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method private static checkValidServerReply(BBIJ)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lme/embodied/SntpClient$InvalidServerReplyException;
        }
    .end annotation

    .line 201
    const/4 v0, 0x3

    if-eq p0, v0, :cond_4

    .line 204
    const/4 p0, 0x4

    if-eq p1, p0, :cond_1

    const/4 p0, 0x5

    if-ne p1, p0, :cond_0

    goto :goto_0

    .line 205
    :cond_0
    new-instance p0, Lme/embodied/SntpClient$InvalidServerReplyException;

    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string p3, "untrusted mode: "

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p0, p1}, Lme/embodied/SntpClient$InvalidServerReplyException;-><init>(Ljava/lang/String;)V

    throw p0

    .line 207
    :cond_1
    :goto_0
    if-eqz p2, :cond_3

    const/16 p0, 0xf

    if-gt p2, p0, :cond_3

    .line 210
    const-wide/16 p0, 0x0

    cmp-long p0, p3, p0

    if-eqz p0, :cond_2

    .line 213
    return-void

    .line 211
    :cond_2
    new-instance p0, Lme/embodied/SntpClient$InvalidServerReplyException;

    const-string p1, "zero transmitTime"

    invoke-direct {p0, p1}, Lme/embodied/SntpClient$InvalidServerReplyException;-><init>(Ljava/lang/String;)V

    throw p0

    .line 208
    :cond_3
    new-instance p0, Lme/embodied/SntpClient$InvalidServerReplyException;

    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string p3, "untrusted stratum: "

    invoke-virtual {p1, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p0, p1}, Lme/embodied/SntpClient$InvalidServerReplyException;-><init>(Ljava/lang/String;)V

    throw p0

    .line 202
    :cond_4
    new-instance p0, Lme/embodied/SntpClient$InvalidServerReplyException;

    const-string p1, "unsynchronized server"

    invoke-direct {p0, p1}, Lme/embodied/SntpClient$InvalidServerReplyException;-><init>(Ljava/lang/String;)V

    throw p0
.end method

.method private read32([BI)J
    .locals 5

    .line 219
    aget-byte v0, p1, p2

    .line 220
    add-int/lit8 v1, p2, 0x1

    aget-byte v1, p1, v1

    .line 221
    add-int/lit8 v2, p2, 0x2

    aget-byte v2, p1, v2

    .line 222
    add-int/lit8 p2, p2, 0x3

    aget-byte p1, p1, p2

    .line 225
    and-int/lit16 p2, v0, 0x80

    const/16 v3, 0x80

    if-ne p2, v3, :cond_0

    and-int/lit8 p2, v0, 0x7f

    add-int/lit16 v0, p2, 0x80

    .line 226
    :cond_0
    and-int/lit16 p2, v1, 0x80

    if-ne p2, v3, :cond_1

    and-int/lit8 p2, v1, 0x7f

    add-int/lit16 v1, p2, 0x80

    .line 227
    :cond_1
    and-int/lit16 p2, v2, 0x80

    if-ne p2, v3, :cond_2

    and-int/lit8 p2, v2, 0x7f

    add-int/lit16 v2, p2, 0x80

    .line 228
    :cond_2
    and-int/lit16 p2, p1, 0x80

    if-ne p2, v3, :cond_3

    and-int/lit8 p1, p1, 0x7f

    add-int/2addr p1, v3

    .line 230
    :cond_3
    int-to-long v3, v0

    const/16 p2, 0x18

    shl-long/2addr v3, p2

    int-to-long v0, v1

    const/16 p2, 0x10

    shl-long/2addr v0, p2

    add-long/2addr v3, v0

    int-to-long v0, v2

    const/16 p2, 0x8

    shl-long/2addr v0, p2

    add-long/2addr v3, v0

    int-to-long p1, p1

    add-long/2addr v3, p1

    return-wide v3
.end method

.method private readTimeStamp([BI)J
    .locals 5

    .line 238
    invoke-direct {p0, p1, p2}, Lme/embodied/SntpClient;->read32([BI)J

    move-result-wide v0

    .line 239
    add-int/lit8 p2, p2, 0x4

    invoke-direct {p0, p1, p2}, Lme/embodied/SntpClient;->read32([BI)J

    move-result-wide p1

    .line 241
    const-wide/16 v2, 0x0

    cmp-long v4, v0, v2

    if-nez v4, :cond_0

    cmp-long v4, p1, v2

    if-nez v4, :cond_0

    .line 242
    return-wide v2

    .line 244
    :cond_0
    const-wide v2, 0x83aa7e80L

    sub-long/2addr v0, v2

    const-wide/16 v2, 0x3e8

    mul-long/2addr v0, v2

    mul-long/2addr p1, v2

    const-wide v2, 0x100000000L

    div-long/2addr p1, v2

    add-long/2addr v0, p1

    return-wide v0
.end method

.method private writeTimeStamp([BIJ)V
    .locals 11

    .line 253
    const-wide/16 v0, 0x0

    cmp-long v0, p3, v0

    const/4 v1, 0x0

    if-nez v0, :cond_0

    .line 254
    add-int/lit8 p3, p2, 0x8

    invoke-static {p1, p2, p3, v1}, Ljava/util/Arrays;->fill([BIIB)V

    .line 255
    return-void

    .line 258
    :cond_0
    const-wide/16 v2, 0x3e8

    div-long v4, p3, v2

    .line 259
    mul-long v6, v4, v2

    sub-long/2addr p3, v6

    .line 260
    const-wide v6, 0x83aa7e80L

    add-long/2addr v4, v6

    .line 263
    add-int/lit8 v0, p2, 0x1

    const/16 v6, 0x18

    shr-long v7, v4, v6

    long-to-int v7, v7

    int-to-byte v7, v7

    aput-byte v7, p1, p2

    .line 264
    add-int/lit8 p2, v0, 0x1

    const/16 v7, 0x10

    shr-long v8, v4, v7

    long-to-int v8, v8

    int-to-byte v8, v8

    aput-byte v8, p1, v0

    .line 265
    add-int/lit8 v0, p2, 0x1

    const/16 v8, 0x8

    shr-long v9, v4, v8

    long-to-int v9, v9

    int-to-byte v9, v9

    aput-byte v9, p1, p2

    .line 266
    add-int/lit8 p2, v0, 0x1

    shr-long/2addr v4, v1

    long-to-int v1, v4

    int-to-byte v1, v1

    aput-byte v1, p1, v0

    .line 268
    const-wide v0, 0x100000000L

    mul-long/2addr p3, v0

    div-long/2addr p3, v2

    .line 270
    add-int/lit8 v0, p2, 0x1

    shr-long v1, p3, v6

    long-to-int v1, v1

    int-to-byte v1, v1

    aput-byte v1, p1, p2

    .line 271
    add-int/lit8 p2, v0, 0x1

    shr-long v1, p3, v7

    long-to-int v1, v1

    int-to-byte v1, v1

    aput-byte v1, p1, v0

    .line 272
    add-int/lit8 v0, p2, 0x1

    shr-long/2addr p3, v8

    long-to-int p3, p3

    int-to-byte p3, p3

    aput-byte p3, p1, p2

    .line 274
    invoke-static {}, Ljava/lang/Math;->random()D

    move-result-wide p2

    const-wide v1, 0x406fe00000000000L    # 255.0

    mul-double/2addr p2, v1

    double-to-int p2, p2

    int-to-byte p2, p2

    aput-byte p2, p1, v0

    .line 275
    return-void
.end method


# virtual methods
.method public getNtpTime()J
    .locals 2

    .line 176
    iget-wide v0, p0, Lme/embodied/SntpClient;->mNtpTime:J

    return-wide v0
.end method

.method public getNtpTimeReference()J
    .locals 2

    .line 186
    iget-wide v0, p0, Lme/embodied/SntpClient;->mNtpTimeReference:J

    return-wide v0
.end method

.method public getRoundTripTime()J
    .locals 2

    .line 195
    iget-wide v0, p0, Lme/embodied/SntpClient;->mRoundTripTime:J

    return-wide v0
.end method

.method public requestTime(Ljava/lang/String;I)Z
    .locals 1

    .line 90
    nop

    .line 92
    :try_start_0
    invoke-static {p1}, Ljava/net/InetAddress;->getByName(Ljava/lang/String;)Ljava/net/InetAddress;

    move-result-object p1
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 96
    nop

    .line 97
    const/16 v0, 0x7b

    invoke-virtual {p0, p1, v0, p2}, Lme/embodied/SntpClient;->requestTime(Ljava/net/InetAddress;II)Z

    move-result p1

    return p1

    .line 93
    :catch_0
    move-exception p1

    .line 94
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "request time failed: "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const-string p2, "EBSntpClient"

    invoke-static {p2, p1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 95
    const/4 p1, 0x0

    return p1
.end method

.method public requestTime(Ljava/net/InetAddress;II)Z
    .locals 22

    .line 101
    move-object/from16 v1, p0

    const-string v2, "EBSntpClient"

    .line 103
    const/4 v3, 0x0

    const/4 v4, 0x0

    :try_start_0
    new-instance v5, Ljava/net/DatagramSocket;

    invoke-direct {v5}, Ljava/net/DatagramSocket;-><init>()V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_1
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    .line 104
    move/from16 v0, p3

    :try_start_1
    invoke-virtual {v5, v0}, Ljava/net/DatagramSocket;->setSoTimeout(I)V

    .line 105
    const/16 v0, 0x30

    new-array v4, v0, [B

    .line 106
    new-instance v6, Ljava/net/DatagramPacket;

    move-object/from16 v7, p1

    move/from16 v8, p2

    invoke-direct {v6, v4, v0, v7, v8}, Ljava/net/DatagramPacket;-><init>([BILjava/net/InetAddress;I)V

    .line 111
    const/16 v7, 0x1b

    aput-byte v7, v4, v3

    .line 114
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v7

    .line 115
    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v9

    .line 116
    const/16 v11, 0x28

    invoke-direct {v1, v4, v11, v7, v8}, Lme/embodied/SntpClient;->writeTimeStamp([BIJ)V

    .line 118
    invoke-virtual {v5, v6}, Ljava/net/DatagramSocket;->send(Ljava/net/DatagramPacket;)V

    .line 121
    new-instance v6, Ljava/net/DatagramPacket;

    invoke-direct {v6, v4, v0}, Ljava/net/DatagramPacket;-><init>([BI)V

    .line 122
    invoke-virtual {v5, v6}, Ljava/net/DatagramSocket;->receive(Ljava/net/DatagramPacket;)V

    .line 123
    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v12

    .line 124
    sub-long v9, v12, v9

    add-long/2addr v7, v9

    .line 127
    aget-byte v0, v4, v3

    shr-int/lit8 v0, v0, 0x6

    and-int/lit8 v0, v0, 0x3

    int-to-byte v0, v0

    .line 128
    aget-byte v6, v4, v3

    and-int/lit8 v6, v6, 0x7

    int-to-byte v6, v6

    .line 129
    const/4 v14, 0x1

    aget-byte v15, v4, v14

    and-int/lit16 v15, v15, 0xff

    .line 130
    const/16 v3, 0x18

    invoke-direct {v1, v4, v3}, Lme/embodied/SntpClient;->readTimeStamp([BI)J

    move-result-wide v16

    .line 131
    const/16 v3, 0x20

    invoke-direct {v1, v4, v3}, Lme/embodied/SntpClient;->readTimeStamp([BI)J

    move-result-wide v18

    .line 132
    invoke-direct {v1, v4, v11}, Lme/embodied/SntpClient;->readTimeStamp([BI)J

    move-result-wide v3

    .line 136
    invoke-static {v0, v6, v15, v3, v4}, Lme/embodied/SntpClient;->checkValidServerReply(BBIJ)V

    .line 138
    sub-long v20, v3, v18

    sub-long v9, v9, v20

    .line 147
    sub-long v18, v18, v16

    sub-long/2addr v3, v7

    add-long v18, v18, v3

    const-wide/16 v3, 0x2

    div-long v3, v18, v3

    .line 149
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, "round trip: "

    invoke-virtual {v0, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v9, v10}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    const-string v6, "ms, clock offset: "

    invoke-virtual {v0, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v3, v4}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    const-string v6, "ms"

    invoke-virtual {v0, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v2, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 155
    add-long/2addr v7, v3

    iput-wide v7, v1, Lme/embodied/SntpClient;->mNtpTime:J

    .line 156
    iput-wide v12, v1, Lme/embodied/SntpClient;->mNtpTimeReference:J

    .line 157
    iput-wide v9, v1, Lme/embodied/SntpClient;->mRoundTripTime:J
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_0
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 162
    nop

    .line 163
    invoke-virtual {v5}, Ljava/net/DatagramSocket;->close()V

    .line 167
    return v14

    .line 162
    :catchall_0
    move-exception v0

    move-object v4, v5

    goto :goto_1

    .line 158
    :catch_0
    move-exception v0

    move-object v4, v5

    goto :goto_0

    .line 162
    :catchall_1
    move-exception v0

    goto :goto_1

    .line 158
    :catch_1
    move-exception v0

    .line 159
    :goto_0
    :try_start_2
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "request time failed: "

    invoke-virtual {v3, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v2, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    .line 160
    nop

    .line 162
    if-eqz v4, :cond_0

    .line 163
    invoke-virtual {v4}, Ljava/net/DatagramSocket;->close()V

    .line 160
    :cond_0
    const/4 v2, 0x0

    return v2

    .line 162
    :goto_1
    if-eqz v4, :cond_1

    .line 163
    invoke-virtual {v4}, Ljava/net/DatagramSocket;->close()V

    .line 165
    :cond_1
    throw v0
.end method
