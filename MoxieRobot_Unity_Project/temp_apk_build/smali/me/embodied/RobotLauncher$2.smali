.class Lme/embodied/RobotLauncher$2;
.super Ljava/lang/Object;
.source "RobotLauncher.java"

# interfaces
.implements Landroid/view/View$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lme/embodied/RobotLauncher;->initView()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lme/embodied/RobotLauncher;


# direct methods
.method constructor <init>(Lme/embodied/RobotLauncher;)V
    .locals 0

    .line 205
    iput-object p1, p0, Lme/embodied/RobotLauncher$2;->this$0:Lme/embodied/RobotLauncher;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onClick(Landroid/view/View;)V
    .locals 1

    .line 209
    :try_start_0
    new-instance p1, Landroid/content/Intent;

    invoke-direct {p1}, Landroid/content/Intent;-><init>()V

    .line 210
    invoke-static {}, Lme/embodied/RobotLauncher;->access$100()Landroid/content/ComponentName;

    move-result-object v0

    invoke-virtual {p1, v0}, Landroid/content/Intent;->setComponent(Landroid/content/ComponentName;)Landroid/content/Intent;

    .line 211
    const-string v0, "android.intent.action.MAIN"

    invoke-virtual {p1, v0}, Landroid/content/Intent;->setAction(Ljava/lang/String;)Landroid/content/Intent;

    .line 212
    const/high16 v0, 0x10000

    invoke-virtual {p1, v0}, Landroid/content/Intent;->setFlags(I)Landroid/content/Intent;

    .line 213
    iget-object v0, p0, Lme/embodied/RobotLauncher$2;->this$0:Lme/embodied/RobotLauncher;

    invoke-virtual {v0, p1}, Lme/embodied/RobotLauncher;->startActivity(Landroid/content/Intent;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 216
    goto :goto_0

    .line 214
    :catch_0
    move-exception p1

    .line 215
    const-string v0, "Error launching launcher"

    invoke-static {v0, p1}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/Throwable;)I

    .line 217
    :goto_0
    return-void
.end method
