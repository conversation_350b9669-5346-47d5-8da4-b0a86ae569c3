.class public Lme/embodied/TransitionAnimation;
.super Ljava/lang/Object;
.source "TransitionAnimation.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lme/embodied/TransitionAnimation$LoopingVideoView;
    }
.end annotation


# static fields
.field static final FADE_TIME_MS:J = 0x3e8L

.field static final OVERRIDE_SPLASH_PATH:Ljava/lang/String; = "/sdcard/EmbodiedStaticData/PERSISTENT_DATA/S3/Launcher/splash.mp4"

.field static final SPLASH_PATH:Ljava/lang/String; = "/vendor/staticdata/Launcher/splash.mp4"

.field static final TAG:Ljava/lang/String; = "TransitionAnimation"


# instance fields
.field animWindow_:Landroid/widget/FrameLayout;

.field targetState_:Z

.field videoReady_:Z

.field videoView_:Lme/embodied/TransitionAnimation$LoopingVideoView;

.field visiblePending_:Z


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 7

    .line 53
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 55
    invoke-static {}, Lme/embodied/TransitionAnimation;->findSplashPath()Ljava/lang/String;

    move-result-object v0

    .line 56
    new-instance v1, Ljava/io/File;

    invoke-direct {v1, v0}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-virtual {v1}, Ljava/io/File;->exists()Z

    move-result v1

    const-string v2, "TransitionAnimation"

    if-nez v1, :cond_0

    .line 57
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Could not locate transition video from path: "

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, ".  Will not show."

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v2, p1}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 58
    return-void

    .line 62
    :cond_0
    :try_start_0
    const-class v1, Landroid/view/WindowManager;

    invoke-virtual {p1, v1}, Landroid/content/Context;->getSystemService(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroid/view/WindowManager;

    .line 64
    new-instance v3, Landroid/view/WindowManager$LayoutParams;

    const/16 v4, 0x7f6

    const/16 v5, 0x8

    const/4 v6, -0x3

    invoke-direct {v3, v4, v5, v6}, Landroid/view/WindowManager$LayoutParams;-><init>(III)V

    .line 68
    const/16 v4, 0x35

    iput v4, v3, Landroid/view/WindowManager$LayoutParams;->gravity:I

    .line 70
    new-instance v4, Landroid/widget/FrameLayout;

    invoke-direct {v4, p1}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;)V

    iput-object v4, p0, Lme/embodied/TransitionAnimation;->animWindow_:Landroid/widget/FrameLayout;

    .line 71
    const/4 v5, 0x0

    invoke-virtual {v4, v5}, Landroid/widget/FrameLayout;->setBackgroundColor(I)V

    .line 73
    new-instance v4, Lme/embodied/TransitionAnimation$LoopingVideoView;

    invoke-direct {v4, p0, p1, v0}, Lme/embodied/TransitionAnimation$LoopingVideoView;-><init>(Lme/embodied/TransitionAnimation;Landroid/content/Context;Ljava/lang/String;)V

    iput-object v4, p0, Lme/embodied/TransitionAnimation;->videoView_:Lme/embodied/TransitionAnimation$LoopingVideoView;

    .line 74
    iget-object p1, p0, Lme/embodied/TransitionAnimation;->animWindow_:Landroid/widget/FrameLayout;

    invoke-virtual {p1, v4}, Landroid/widget/FrameLayout;->addView(Landroid/view/View;)V

    .line 75
    iget-object p1, p0, Lme/embodied/TransitionAnimation;->animWindow_:Landroid/widget/FrameLayout;

    const/4 v0, 0x4

    invoke-virtual {p1, v0}, Landroid/widget/FrameLayout;->setVisibility(I)V

    .line 77
    iget-object p1, p0, Lme/embodied/TransitionAnimation;->animWindow_:Landroid/widget/FrameLayout;

    invoke-interface {v1, p1, v3}, Landroid/view/WindowManager;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 81
    goto :goto_0

    .line 78
    :catch_0
    move-exception p1

    .line 79
    const-string v0, "Failed to create animation overlay window."

    invoke-static {v2, v0, p1}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)I

    .line 80
    const/4 p1, 0x0

    iput-object p1, p0, Lme/embodied/TransitionAnimation;->animWindow_:Landroid/widget/FrameLayout;

    .line 82
    :goto_0
    return-void
.end method

.method private static findSplashPath()Ljava/lang/String;
    .locals 2

    .line 47
    new-instance v0, Ljava/io/File;

    const-string v1, "/sdcard/EmbodiedStaticData/PERSISTENT_DATA/S3/Launcher/splash.mp4"

    invoke-direct {v0, v1}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-virtual {v0}, Ljava/io/File;->exists()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 48
    return-object v1

    .line 50
    :cond_0
    const-string v0, "/vendor/staticdata/Launcher/splash.mp4"

    return-object v0
.end method


# virtual methods
.method public setVisible(Z)V
    .locals 4

    .line 85
    iget-object v0, p0, Lme/embodied/TransitionAnimation;->animWindow_:Landroid/widget/FrameLayout;

    const-string v1, "TransitionAnimation"

    if-nez v0, :cond_0

    .line 86
    const-string p1, "Can\'t adjust null animation."

    invoke-static {v1, p1}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 87
    return-void

    .line 90
    :cond_0
    iget-boolean v0, p0, Lme/embodied/TransitionAnimation;->targetState_:Z

    if-eq v0, p1, :cond_2

    .line 91
    iput-boolean p1, p0, Lme/embodied/TransitionAnimation;->targetState_:Z

    .line 93
    const-wide/16 v2, 0x3e8

    const/4 v0, 0x0

    if-eqz p1, :cond_1

    .line 94
    const-string p1, "Showing animation!"

    invoke-static {v1, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 95
    iget-object p1, p0, Lme/embodied/TransitionAnimation;->videoView_:Lme/embodied/TransitionAnimation$LoopingVideoView;

    invoke-virtual {p1, v0}, Lme/embodied/TransitionAnimation$LoopingVideoView;->setAlpha(F)V

    .line 96
    iget-object p1, p0, Lme/embodied/TransitionAnimation;->videoView_:Lme/embodied/TransitionAnimation$LoopingVideoView;

    invoke-virtual {p1}, Lme/embodied/TransitionAnimation$LoopingVideoView;->animate()Landroid/view/ViewPropertyAnimator;

    move-result-object p1

    const/high16 v0, 0x3f800000    # 1.0f

    invoke-virtual {p1, v0}, Landroid/view/ViewPropertyAnimator;->alpha(F)Landroid/view/ViewPropertyAnimator;

    move-result-object p1

    invoke-virtual {p1, v2, v3}, Landroid/view/ViewPropertyAnimator;->setDuration(J)Landroid/view/ViewPropertyAnimator;

    move-result-object p1

    const/4 v0, 0x0

    invoke-virtual {p1, v0}, Landroid/view/ViewPropertyAnimator;->setListener(Landroid/animation/Animator$AnimatorListener;)Landroid/view/ViewPropertyAnimator;

    .line 97
    iget-object p1, p0, Lme/embodied/TransitionAnimation;->videoView_:Lme/embodied/TransitionAnimation$LoopingVideoView;

    invoke-virtual {p1}, Lme/embodied/TransitionAnimation$LoopingVideoView;->resumePlayback()V

    .line 98
    iget-object p1, p0, Lme/embodied/TransitionAnimation;->animWindow_:Landroid/widget/FrameLayout;

    const/4 v0, 0x0

    invoke-virtual {p1, v0}, Landroid/widget/FrameLayout;->setVisibility(I)V

    goto :goto_0

    .line 100
    :cond_1
    const-string p1, "Hiding animation!"

    invoke-static {v1, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 101
    iget-object p1, p0, Lme/embodied/TransitionAnimation;->videoView_:Lme/embodied/TransitionAnimation$LoopingVideoView;

    invoke-virtual {p1}, Lme/embodied/TransitionAnimation$LoopingVideoView;->animate()Landroid/view/ViewPropertyAnimator;

    move-result-object p1

    invoke-virtual {p1, v0}, Landroid/view/ViewPropertyAnimator;->alpha(F)Landroid/view/ViewPropertyAnimator;

    move-result-object p1

    invoke-virtual {p1, v2, v3}, Landroid/view/ViewPropertyAnimator;->setDuration(J)Landroid/view/ViewPropertyAnimator;

    move-result-object p1

    new-instance v0, Lme/embodied/TransitionAnimation$1;

    invoke-direct {v0, p0}, Lme/embodied/TransitionAnimation$1;-><init>(Lme/embodied/TransitionAnimation;)V

    invoke-virtual {p1, v0}, Landroid/view/ViewPropertyAnimator;->setListener(Landroid/animation/Animator$AnimatorListener;)Landroid/view/ViewPropertyAnimator;

    .line 110
    :cond_2
    :goto_0
    return-void
.end method
