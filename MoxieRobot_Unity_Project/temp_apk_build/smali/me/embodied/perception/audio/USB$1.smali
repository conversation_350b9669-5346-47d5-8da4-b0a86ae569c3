.class Lme/embodied/perception/audio/USB$1;
.super Landroid/content/BroadcastReceiver;
.source "USB.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lme/embodied/perception/audio/USB;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lme/embodied/perception/audio/USB;


# direct methods
.method constructor <init>(Lme/embodied/perception/audio/USB;)V
    .locals 0

    .line 148
    iput-object p1, p0, Lme/embodied/perception/audio/USB$1;->this$0:Lme/embodied/perception/audio/USB;

    invoke-direct {p0}, Landroid/content/BroadcastReceiver;-><init>()V

    return-void
.end method


# virtual methods
.method public onReceive(Landroid/content/Context;Landroid/content/Intent;)V
    .locals 4

    .line 151
    invoke-virtual {p2}, Landroid/content/Intent;->getAction()Ljava/lang/String;

    move-result-object p1

    .line 152
    const-string v0, "me.embodied.perception.audio.USB_PERMISSION"

    invoke-virtual {v0, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_2

    .line 154
    monitor-enter p0

    .line 156
    :try_start_0
    iget-object p1, p0, Lme/embodied/perception/audio/USB$1;->this$0:Lme/embodied/perception/audio/USB;

    iget-wide v0, p1, Lme/embodied/perception/audio/USB;->usb_handle_:J

    const-wide/16 v2, 0x0

    cmp-long p1, v0, v2

    if-nez p1, :cond_0

    .line 157
    const-string p1, "EBUSB"

    const-string p2, "Received callback will null handler."

    invoke-static {p1, p2}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 158
    monitor-exit p0

    return-void

    .line 160
    :cond_0
    const-string p1, "device"

    invoke-virtual {p2, p1}, Landroid/content/Intent;->getParcelableExtra(Ljava/lang/String;)Landroid/os/Parcelable;

    move-result-object p1

    check-cast p1, Landroid/hardware/usb/UsbDevice;

    .line 162
    const-string v0, "permission"

    const/4 v1, 0x0

    invoke-virtual {p2, v0, v1}, Landroid/content/Intent;->getBooleanExtra(Ljava/lang/String;Z)Z

    move-result p2

    if-eqz p2, :cond_1

    if-eqz p1, :cond_1

    .line 164
    const-string p2, "EBUSB"

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Permission granted: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Landroid/hardware/usb/UsbDevice;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {p2, v0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 167
    iget-object p2, p0, Lme/embodied/perception/audio/USB$1;->this$0:Lme/embodied/perception/audio/USB;

    iget-object v0, p0, Lme/embodied/perception/audio/USB$1;->this$0:Lme/embodied/perception/audio/USB;

    invoke-static {v0}, Lme/embodied/perception/audio/USB;->access$000(Lme/embodied/perception/audio/USB;)Landroid/hardware/usb/UsbManager;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/hardware/usb/UsbManager;->openDevice(Landroid/hardware/usb/UsbDevice;)Landroid/hardware/usb/UsbDeviceConnection;

    move-result-object p1

    iput-object p1, p2, Lme/embodied/perception/audio/USB;->connection:Landroid/hardware/usb/UsbDeviceConnection;

    .line 168
    iget-object p1, p0, Lme/embodied/perception/audio/USB$1;->this$0:Lme/embodied/perception/audio/USB;

    iget-object p2, p0, Lme/embodied/perception/audio/USB$1;->this$0:Lme/embodied/perception/audio/USB;

    iget-object p2, p2, Lme/embodied/perception/audio/USB;->connection:Landroid/hardware/usb/UsbDeviceConnection;

    invoke-virtual {p2}, Landroid/hardware/usb/UsbDeviceConnection;->getFileDescriptor()I

    move-result p2

    iput p2, p1, Lme/embodied/perception/audio/USB;->xmos_fd_:I

    .line 170
    const-string p1, "EBUSB"

    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "XMOS FD: "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v0, p0, Lme/embodied/perception/audio/USB$1;->this$0:Lme/embodied/perception/audio/USB;

    iget v0, v0, Lme/embodied/perception/audio/USB;->xmos_fd_:I

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-static {p1, p2}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 173
    iget-object p1, p0, Lme/embodied/perception/audio/USB$1;->this$0:Lme/embodied/perception/audio/USB;

    iget-object p2, p0, Lme/embodied/perception/audio/USB$1;->this$0:Lme/embodied/perception/audio/USB;

    iget-wide v0, p2, Lme/embodied/perception/audio/USB;->usb_handle_:J

    iget-object p2, p0, Lme/embodied/perception/audio/USB$1;->this$0:Lme/embodied/perception/audio/USB;

    iget p2, p2, Lme/embodied/perception/audio/USB;->xmos_fd_:I

    invoke-static {p1, v0, v1, p2}, Lme/embodied/perception/audio/USB;->access$100(Lme/embodied/perception/audio/USB;JI)V

    goto :goto_0

    .line 177
    :cond_1
    const-string p2, "EBUSB"

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Permission denied: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Landroid/hardware/usb/UsbDevice;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {p2, p1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 179
    :goto_0
    monitor-exit p0

    goto :goto_1

    :catchall_0
    move-exception p1

    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw p1

    .line 181
    :cond_2
    :goto_1
    return-void
.end method
