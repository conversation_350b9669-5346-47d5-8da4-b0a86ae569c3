.class Lme/embodied/perception/audio/STTWebClient$1;
.super Ljava/lang/Thread;
.source "STTWebClient.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lme/embodied/perception/audio/STTWebClient;->checkWorkers()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lme/embodied/perception/audio/STTWebClient;


# direct methods
.method constructor <init>(Lme/embodied/perception/audio/STTWebClient;)V
    .locals 0

    .line 173
    iput-object p1, p0, Lme/embodied/perception/audio/STTWebClient$1;->this$0:Lme/embodied/perception/audio/STTWebClient;

    invoke-direct {p0}, Ljava/lang/Thread;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 4

    .line 176
    const-string v0, "STTWebClient"

    :try_start_0
    const-string v1, "Running client maker thread... creating client..."

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 177
    iget-object v1, p0, Lme/embodied/perception/audio/STTWebClient$1;->this$0:Lme/embodied/perception/audio/STTWebClient;

    iget-object v2, p0, Lme/embodied/perception/audio/STTWebClient$1;->this$0:Lme/embodied/perception/audio/STTWebClient;

    iget-object v3, p0, Lme/embodied/perception/audio/STTWebClient$1;->this$0:Lme/embodied/perception/audio/STTWebClient;

    iget-object v3, v3, Lme/embodied/perception/audio/STTWebClient;->currentBuffer_:Lme/embodied/perception/audio/STTWebClient$AudioBuffer;

    invoke-virtual {v2, v3}, Lme/embodied/perception/audio/STTWebClient;->createClient(Lme/embodied/perception/audio/STTWebClient$AudioBuffer;)Lme/embodied/perception/audio/STTWorker;

    move-result-object v2

    iput-object v2, v1, Lme/embodied/perception/audio/STTWebClient;->client_:Lme/embodied/perception/audio/STTWorker;

    .line 178
    iget-object v1, p0, Lme/embodied/perception/audio/STTWebClient$1;->this$0:Lme/embodied/perception/audio/STTWebClient;

    iget-object v1, v1, Lme/embodied/perception/audio/STTWebClient;->client_:Lme/embodied/perception/audio/STTWorker;

    invoke-virtual {v1}, Lme/embodied/perception/audio/STTWorker;->connect()V
    :try_end_0
    .catch Ljava/net/URISyntaxException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/io/UnsupportedEncodingException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_2

    .line 182
    :catch_0
    move-exception v1

    goto :goto_0

    .line 179
    :catch_1
    move-exception v1

    goto :goto_1

    .line 183
    :goto_0
    invoke-virtual {v1}, Ljava/io/UnsupportedEncodingException;->printStackTrace()V

    .line 184
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Error encoding client parameters: "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_3

    .line 180
    :goto_1
    invoke-virtual {v1}, Ljava/net/URISyntaxException;->printStackTrace()V

    .line 181
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Error creating client: "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 185
    :goto_2
    nop

    .line 186
    :goto_3
    iget-object v0, p0, Lme/embodied/perception/audio/STTWebClient$1;->this$0:Lme/embodied/perception/audio/STTWebClient;

    const/4 v1, 0x0

    iput-object v1, v0, Lme/embodied/perception/audio/STTWebClient;->client_maker_:Ljava/lang/Thread;

    .line 187
    return-void
.end method
