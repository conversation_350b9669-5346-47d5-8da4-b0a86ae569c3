.class Lme/embodied/perception/audio/STTWorker$1;
.super Ljava/lang/Thread;
.source "STTWorker.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lme/embodied/perception/audio/STTWorker;->onOpen(Lorg/java_websocket/handshake/ServerHandshake;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lme/embodied/perception/audio/STTWorker;


# direct methods
.method constructor <init>(Lme/embodied/perception/audio/STTWorker;)V
    .locals 0

    .line 114
    iput-object p1, p0, Lme/embodied/perception/audio/STTWorker$1;->this$0:Lme/embodied/perception/audio/STTWorker;

    invoke-direct {p0}, Ljava/lang/Thread;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 5

    .line 117
    const-string v0, "STTWorker"

    const/4 v1, 0x0

    :try_start_0
    const-string v2, "Worker thread running..."

    invoke-static {v0, v2}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 118
    :goto_0
    iget-object v2, p0, Lme/embodied/perception/audio/STTWorker$1;->this$0:Lme/embodied/perception/audio/STTWorker;

    iget-boolean v2, v2, Lme/embodied/perception/audio/STTWorker;->active_:Z

    if-eqz v2, :cond_1

    .line 119
    iget-object v2, p0, Lme/embodied/perception/audio/STTWorker$1;->this$0:Lme/embodied/perception/audio/STTWorker;

    invoke-virtual {v2}, Lme/embodied/perception/audio/STTWorker;->getBufferOrStart()[B

    move-result-object v2

    .line 120
    if-eqz v2, :cond_0

    .line 121
    iget-object v3, p0, Lme/embodied/perception/audio/STTWorker$1;->this$0:Lme/embodied/perception/audio/STTWorker;

    invoke-virtual {v3, v2}, Lme/embodied/perception/audio/STTWorker;->send([B)V

    goto :goto_1

    .line 123
    :cond_0
    const-string v2, "Timed out, no frame"

    invoke-static {v0, v2}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 125
    :goto_1
    goto :goto_0

    .line 126
    :cond_1
    const-string v2, "Worker thread exiting"

    invoke-static {v0, v2}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I
    :try_end_0
    .catch Ljava/lang/InterruptedException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_4

    .line 132
    :catchall_0
    move-exception v0

    goto :goto_5

    .line 129
    :catch_0
    move-exception v2

    goto :goto_2

    .line 127
    :catch_1
    move-exception v2

    goto :goto_3

    .line 130
    :goto_2
    :try_start_1
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "Worker exception: "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v0, v2}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_4

    .line 128
    :goto_3
    const-string v2, "Worker interrupted"

    invoke-static {v0, v2}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 132
    :goto_4
    iget-object v0, p0, Lme/embodied/perception/audio/STTWorker$1;->this$0:Lme/embodied/perception/audio/STTWorker;

    iput-object v1, v0, Lme/embodied/perception/audio/STTWorker;->worker_:Ljava/lang/Thread;

    .line 133
    iget-object v0, p0, Lme/embodied/perception/audio/STTWorker$1;->this$0:Lme/embodied/perception/audio/STTWorker;

    invoke-virtual {v0}, Lme/embodied/perception/audio/STTWorker;->endMe()V

    .line 134
    nop

    .line 136
    return-void

    .line 132
    :goto_5
    iget-object v2, p0, Lme/embodied/perception/audio/STTWorker$1;->this$0:Lme/embodied/perception/audio/STTWorker;

    iput-object v1, v2, Lme/embodied/perception/audio/STTWorker;->worker_:Ljava/lang/Thread;

    .line 133
    iget-object v1, p0, Lme/embodied/perception/audio/STTWorker$1;->this$0:Lme/embodied/perception/audio/STTWorker;

    invoke-virtual {v1}, Lme/embodied/perception/audio/STTWorker;->endMe()V

    .line 134
    throw v0
.end method
