.class Lme/embodied/perception/audio/STTWebClient$2;
.super Ljava/lang/Object;
.source "STTWebClient.java"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lme/embodied/perception/audio/STTWebClient;->spawnWorker(Lme/embodied/perception/audio/STTWebClient$AudioBuffer;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lme/embodied/perception/audio/STTWebClient;

.field final synthetic val$buffer:Lme/embodied/perception/audio/STTWebClient$AudioBuffer;


# direct methods
.method constructor <init>(Lme/embodied/perception/audio/STTWebClient;Lme/embodied/perception/audio/STTWebClient$AudioBuffer;)V
    .locals 0

    .line 196
    iput-object p1, p0, Lme/embodied/perception/audio/STTWebClient$2;->this$0:Lme/embodied/perception/audio/STTWebClient;

    iput-object p2, p0, Lme/embodied/perception/audio/STTWebClient$2;->val$buffer:Lme/embodied/perception/audio/STTWebClient$AudioBuffer;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 4

    .line 199
    const-string v0, "STTWebClient"

    :try_start_0
    const-string v1, "Running client maker task, creating client..."

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 200
    iget-object v1, p0, Lme/embodied/perception/audio/STTWebClient$2;->this$0:Lme/embodied/perception/audio/STTWebClient;

    iget-object v2, p0, Lme/embodied/perception/audio/STTWebClient$2;->val$buffer:Lme/embodied/perception/audio/STTWebClient$AudioBuffer;

    invoke-virtual {v1, v2}, Lme/embodied/perception/audio/STTWebClient;->createClient(Lme/embodied/perception/audio/STTWebClient$AudioBuffer;)Lme/embodied/perception/audio/STTWorker;

    move-result-object v1

    .line 201
    invoke-virtual {v1}, Lme/embodied/perception/audio/STTWorker;->connect()V
    :try_end_0
    .catch Ljava/net/URISyntaxException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/io/UnsupportedEncodingException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_2

    .line 205
    :catch_0
    move-exception v1

    goto :goto_0

    .line 202
    :catch_1
    move-exception v1

    goto :goto_1

    .line 206
    :goto_0
    invoke-virtual {v1}, Ljava/io/UnsupportedEncodingException;->printStackTrace()V

    .line 207
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Error encoding client parameters: "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_3

    .line 203
    :goto_1
    invoke-virtual {v1}, Ljava/net/URISyntaxException;->printStackTrace()V

    .line 204
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Error creating client: "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 208
    :goto_2
    nop

    .line 209
    :goto_3
    return-void
.end method
