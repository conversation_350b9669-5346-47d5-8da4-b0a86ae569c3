.class public Lme/embodied/perception/audio/USB;
.super Ljava/lang/Object;
.source "USB.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lme/embodied/perception/audio/USB$Device;
    }
.end annotation


# static fields
.field private static final ACTION_USB_PERMISSION:Ljava/lang/String; = "me.embodied.perception.audio.USB_PERMISSION"

.field private static final INVALID_FD:I = -0x1

.field private static final TAG:Ljava/lang/String; = "EBUSB"

.field private static final USB_NOT_READY:I = -0x2


# instance fields
.field private _usbManager:Landroid/hardware/usb/UsbManager;

.field connection:Landroid/hardware/usb/UsbDeviceConnection;

.field context_:Landroid/content/Context;

.field private final mUsbReceiver:Landroid/content/BroadcastReceiver;

.field usb_handle_:J

.field private usb_ready_:Z

.field xmos_fd_:I


# direct methods
.method public constructor <init>(JLandroid/content/Context;)V
    .locals 2

    .line 46
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 147
    new-instance v0, Lme/embodied/perception/audio/USB$1;

    invoke-direct {v0, p0}, Lme/embodied/perception/audio/USB$1;-><init>(Lme/embodied/perception/audio/USB;)V

    iput-object v0, p0, Lme/embodied/perception/audio/USB;->mUsbReceiver:Landroid/content/BroadcastReceiver;

    .line 47
    if-nez p3, :cond_0

    .line 48
    const-string v0, "EBUSB"

    const-string v1, "CONTEXT IS NULL! THIS IS GOING TO CRASH"

    invoke-static {v0, v1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 50
    :cond_0
    iput-object p3, p0, Lme/embodied/perception/audio/USB;->context_:Landroid/content/Context;

    .line 51
    iput-wide p1, p0, Lme/embodied/perception/audio/USB;->usb_handle_:J

    .line 52
    const/4 p1, 0x0

    iput-boolean p1, p0, Lme/embodied/perception/audio/USB;->usb_ready_:Z

    .line 53
    invoke-virtual {p0}, Lme/embodied/perception/audio/USB;->GetFD()V

    .line 54
    return-void
.end method

.method private GetUSBPermission()V
    .locals 4

    .line 133
    iget-object v0, p0, Lme/embodied/perception/audio/USB;->context_:Landroid/content/Context;

    new-instance v1, Landroid/content/Intent;

    const-string v2, "me.embodied.perception.audio.USB_PERMISSION"

    invoke-direct {v1, v2}, Landroid/content/Intent;-><init>(Ljava/lang/String;)V

    const/4 v3, 0x0

    invoke-static {v0, v3, v1, v3}, Landroid/app/PendingIntent;->getBroadcast(Landroid/content/Context;ILandroid/content/Intent;I)Landroid/app/PendingIntent;

    move-result-object v0

    .line 134
    new-instance v1, Landroid/content/IntentFilter;

    invoke-direct {v1, v2}, Landroid/content/IntentFilter;-><init>(Ljava/lang/String;)V

    .line 135
    iget-object v2, p0, Lme/embodied/perception/audio/USB;->context_:Landroid/content/Context;

    iget-object v3, p0, Lme/embodied/perception/audio/USB;->mUsbReceiver:Landroid/content/BroadcastReceiver;

    invoke-virtual {v2, v3, v1}, Landroid/content/Context;->registerReceiver(Landroid/content/BroadcastReceiver;Landroid/content/IntentFilter;)Landroid/content/Intent;

    .line 137
    iget-object v1, p0, Lme/embodied/perception/audio/USB;->context_:Landroid/content/Context;

    const-string v2, "usb"

    invoke-virtual {v1, v2}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroid/hardware/usb/UsbManager;

    iput-object v1, p0, Lme/embodied/perception/audio/USB;->_usbManager:Landroid/hardware/usb/UsbManager;

    .line 139
    invoke-virtual {v1}, Landroid/hardware/usb/UsbManager;->getDeviceList()Ljava/util/HashMap;

    move-result-object v1

    invoke-virtual {v1}, Ljava/util/HashMap;->values()Ljava/util/Collection;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Landroid/hardware/usb/UsbDevice;

    .line 140
    iget-object v3, p0, Lme/embodied/perception/audio/USB;->_usbManager:Landroid/hardware/usb/UsbManager;

    invoke-virtual {v3, v2, v0}, Landroid/hardware/usb/UsbManager;->requestPermission(Landroid/hardware/usb/UsbDevice;Landroid/app/PendingIntent;)V

    .line 141
    goto :goto_0

    .line 142
    :cond_0
    return-void
.end method

.method private native XMOSFDCallback(JI)V
.end method

.method static synthetic access$000(Lme/embodied/perception/audio/USB;)Landroid/hardware/usb/UsbManager;
    .locals 0

    .line 27
    iget-object p0, p0, Lme/embodied/perception/audio/USB;->_usbManager:Landroid/hardware/usb/UsbManager;

    return-object p0
.end method

.method static synthetic access$100(Lme/embodied/perception/audio/USB;JI)V
    .locals 0

    .line 27
    invoke-direct {p0, p1, p2, p3}, Lme/embodied/perception/audio/USB;->XMOSFDCallback(JI)V

    return-void
.end method

.method private sysReadInt(Ljava/lang/String;)I
    .locals 3

    .line 122
    const-string v0, "EBUSB"

    :try_start_0
    new-instance v1, Ljava/io/BufferedReader;

    new-instance v2, Ljava/io/FileReader;

    invoke-direct {v2, p1}, Ljava/io/FileReader;-><init>(Ljava/lang/String;)V

    invoke-direct {v1, v2}, Ljava/io/BufferedReader;-><init>(Ljava/io/Reader;)V

    .line 123
    invoke-virtual {v1}, Ljava/io/BufferedReader;->readLine()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result p1
    :try_end_0
    .catch Ljava/io/FileNotFoundException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    return p1

    .line 126
    :catch_0
    move-exception p1

    .line 127
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "IOException: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v0, p1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_0

    .line 124
    :catch_1
    move-exception v1

    .line 125
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "File not found: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v0, p1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 128
    nop

    .line 129
    :goto_0
    const/4 p1, -0x1

    return p1
.end method

.method private sysWriteInt(Ljava/lang/String;I)I
    .locals 2

    .line 109
    :try_start_0
    new-instance v0, Ljava/io/BufferedWriter;

    new-instance v1, Ljava/io/FileWriter;

    invoke-direct {v1, p1}, Ljava/io/FileWriter;-><init>(Ljava/lang/String;)V

    invoke-direct {v0, v1}, Ljava/io/BufferedWriter;-><init>(Ljava/io/Writer;)V

    .line 110
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-static {p2}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p2, "\n"

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/io/BufferedWriter;->write(Ljava/lang/String;)V

    .line 111
    invoke-virtual {v0}, Ljava/io/BufferedWriter;->flush()V

    .line 112
    invoke-virtual {v0}, Ljava/io/BufferedWriter;->close()V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    .line 113
    const/4 p1, 0x0

    return p1

    .line 114
    :catch_0
    move-exception p1

    .line 115
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "IOException: "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const-string p2, "EBUSB"

    invoke-static {p2, p1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 117
    const/4 p1, -0x1

    return p1
.end method


# virtual methods
.method GetFD()V
    .locals 7

    .line 73
    iget-object v0, p0, Lme/embodied/perception/audio/USB;->context_:Landroid/content/Context;

    const-string v1, "usb"

    invoke-virtual {v0, v1}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/hardware/usb/UsbManager;

    iput-object v0, p0, Lme/embodied/perception/audio/USB;->_usbManager:Landroid/hardware/usb/UsbManager;

    .line 74
    invoke-virtual {v0}, Landroid/hardware/usb/UsbManager;->getDeviceList()Ljava/util/HashMap;

    move-result-object v0

    .line 75
    invoke-virtual {v0}, Ljava/util/HashMap;->values()Ljava/util/Collection;

    move-result-object v0

    .line 77
    iget-object v1, p0, Lme/embodied/perception/audio/USB;->context_:Landroid/content/Context;

    new-instance v2, Landroid/content/Intent;

    const-string v3, "me.embodied.perception.audio.USB_PERMISSION"

    invoke-direct {v2, v3}, Landroid/content/Intent;-><init>(Ljava/lang/String;)V

    const/4 v4, 0x0

    invoke-static {v1, v4, v2, v4}, Landroid/app/PendingIntent;->getBroadcast(Landroid/content/Context;ILandroid/content/Intent;I)Landroid/app/PendingIntent;

    move-result-object v1

    .line 78
    new-instance v2, Landroid/content/IntentFilter;

    invoke-direct {v2, v3}, Landroid/content/IntentFilter;-><init>(Ljava/lang/String;)V

    .line 79
    iget-object v3, p0, Lme/embodied/perception/audio/USB;->context_:Landroid/content/Context;

    iget-object v5, p0, Lme/embodied/perception/audio/USB;->mUsbReceiver:Landroid/content/BroadcastReceiver;

    invoke-virtual {v3, v5, v2}, Landroid/content/Context;->registerReceiver(Landroid/content/BroadcastReceiver;Landroid/content/IntentFilter;)Landroid/content/Intent;

    .line 80
    invoke-interface {v0}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v0

    .line 81
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_5

    .line 82
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Landroid/hardware/usb/UsbDevice;

    .line 83
    invoke-virtual {v2}, Landroid/hardware/usb/UsbDevice;->getVendorId()I

    move-result v3

    const/16 v5, 0x20b1

    if-ne v3, v5, :cond_4

    .line 84
    const-string v0, "EBUSB"

    const-string v3, "Found xmos"

    invoke-static {v0, v3}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 86
    const-string v3, "/sys/devices/platform/xmos-usb/speaker_en"

    invoke-direct {p0, v3}, Lme/embodied/perception/audio/USB;->sysReadInt(Ljava/lang/String;)I

    move-result v5

    .line 87
    const/4 v6, 0x1

    if-ne v5, v6, :cond_1

    .line 88
    invoke-direct {p0, v3, v4}, Lme/embodied/perception/audio/USB;->sysWriteInt(Ljava/lang/String;I)I

    move-result v3

    .line 89
    if-eqz v3, :cond_0

    .line 90
    const-string v3, "BO#4009 Failed to write speaker_en"

    invoke-static {v0, v3}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_1

    .line 92
    :cond_0
    const-string v3, "XMOS speaker enabled"

    invoke-static {v0, v3}, Landroid/util/Log;->v(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_1

    .line 94
    :cond_1
    if-nez v5, :cond_2

    .line 95
    const-string v3, "XMOS speaker already enabled"

    invoke-static {v0, v3}, Landroid/util/Log;->v(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_1

    .line 96
    :cond_2
    if-gez v5, :cond_3

    .line 97
    const-string v3, "BO#4009 Failed to read speaker_en"

    invoke-static {v0, v3}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 101
    :cond_3
    :goto_1
    iget-object v0, p0, Lme/embodied/perception/audio/USB;->_usbManager:Landroid/hardware/usb/UsbManager;

    invoke-virtual {v0, v2, v1}, Landroid/hardware/usb/UsbManager;->requestPermission(Landroid/hardware/usb/UsbDevice;Landroid/app/PendingIntent;)V

    .line 102
    goto :goto_2

    .line 104
    :cond_4
    goto :goto_0

    .line 105
    :cond_5
    :goto_2
    return-void
.end method

.method public GetXMOS()I
    .locals 1

    .line 57
    iget-boolean v0, p0, Lme/embodied/perception/audio/USB;->usb_ready_:Z

    if-eqz v0, :cond_0

    .line 58
    iget v0, p0, Lme/embodied/perception/audio/USB;->xmos_fd_:I

    return v0

    .line 60
    :cond_0
    const/4 v0, -0x2

    return v0
.end method

.method public declared-synchronized ReleaseSystem()V
    .locals 2

    monitor-enter p0

    .line 65
    :try_start_0
    iget-object v0, p0, Lme/embodied/perception/audio/USB;->context_:Landroid/content/Context;

    if-eqz v0, :cond_0

    .line 66
    iget-object v0, p0, Lme/embodied/perception/audio/USB;->context_:Landroid/content/Context;

    iget-object v1, p0, Lme/embodied/perception/audio/USB;->mUsbReceiver:Landroid/content/BroadcastReceiver;

    invoke-virtual {v0, v1}, Landroid/content/Context;->unregisterReceiver(Landroid/content/BroadcastReceiver;)V

    .line 67
    const-string v0, "EBUSB"

    const-string v1, "Released system receiver."

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 69
    :cond_0
    const-wide/16 v0, 0x0

    iput-wide v0, p0, Lme/embodied/perception/audio/USB;->usb_handle_:J
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 70
    monitor-exit p0

    return-void

    .line 64
    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method
