.class Lme/embodied/perception/audio/STTWebClient$AudioBuffer;
.super Ljava/lang/Object;
.source "STTWebClient.java"

# interfaces
.implements Lme/embodied/perception/audio/AudioQueue;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lme/embodied/perception/audio/STTWebClient;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = "AudioBuffer"
.end annotation


# instance fields
.field audioBuffers_:Ljava/util/LinkedList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/LinkedList<",
            "[B>;"
        }
    .end annotation
.end field

.field audioRootTs_:J

.field final synthetic this$0:Lme/embodied/perception/audio/STTWebClient;


# direct methods
.method public constructor <init>(Lme/embodied/perception/audio/STTWebClient;J)V
    .locals 0

    .line 53
    iput-object p1, p0, Lme/embodied/perception/audio/STTWebClient$AudioBuffer;->this$0:Lme/embodied/perception/audio/STTWebClient;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 51
    new-instance p1, Ljava/util/LinkedList;

    invoke-direct {p1}, Ljava/util/LinkedList;-><init>()V

    iput-object p1, p0, Lme/embodied/perception/audio/STTWebClient$AudioBuffer;->audioBuffers_:Ljava/util/LinkedList;

    .line 54
    iput-wide p2, p0, Lme/embodied/perception/audio/STTWebClient$AudioBuffer;->audioRootTs_:J

    .line 55
    return-void
.end method


# virtual methods
.method public firstBuffer(J)[Ljava/lang/Object;
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/InterruptedException;
        }
    .end annotation

    .line 71
    monitor-enter p0

    .line 72
    :try_start_0
    iget-object v0, p0, Lme/embodied/perception/audio/STTWebClient$AudioBuffer;->audioBuffers_:Ljava/util/LinkedList;

    invoke-virtual {v0}, Ljava/util/LinkedList;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 73
    invoke-virtual {p0, p1, p2}, Ljava/lang/Object;->wait(J)V

    .line 74
    :cond_0
    iget-object p1, p0, Lme/embodied/perception/audio/STTWebClient$AudioBuffer;->audioBuffers_:Ljava/util/LinkedList;

    invoke-virtual {p1}, Ljava/util/LinkedList;->isEmpty()Z

    move-result p1

    if-eqz p1, :cond_1

    .line 75
    const/4 p1, 0x0

    monitor-exit p0

    return-object p1

    .line 77
    :cond_1
    const-string p1, "STTWebClient"

    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "Starting stream with TS "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-wide v0, p0, Lme/embodied/perception/audio/STTWebClient$AudioBuffer;->audioRootTs_:J

    invoke-virtual {p2, v0, v1}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    const-string v0, ", CurrBuffers: "

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v0, p0, Lme/embodied/perception/audio/STTWebClient$AudioBuffer;->audioBuffers_:Ljava/util/LinkedList;

    invoke-virtual {v0}, Ljava/util/LinkedList;->size()I

    move-result v0

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-static {p1, p2}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 78
    const/4 p1, 0x2

    new-array p1, p1, [Ljava/lang/Object;

    const/4 p2, 0x0

    iget-wide v0, p0, Lme/embodied/perception/audio/STTWebClient$AudioBuffer;->audioRootTs_:J

    .line 79
    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v0

    aput-object v0, p1, p2

    const/4 p2, 0x1

    iget-object v0, p0, Lme/embodied/perception/audio/STTWebClient$AudioBuffer;->audioBuffers_:Ljava/util/LinkedList;

    .line 80
    invoke-virtual {v0}, Ljava/util/LinkedList;->removeFirst()Ljava/lang/Object;

    move-result-object v0

    aput-object v0, p1, p2

    .line 82
    iget-wide v0, p0, Lme/embodied/perception/audio/STTWebClient$AudioBuffer;->audioRootTs_:J

    const-wide/16 v2, 0x78

    add-long/2addr v0, v2

    iput-wide v0, p0, Lme/embodied/perception/audio/STTWebClient$AudioBuffer;->audioRootTs_:J

    .line 83
    monitor-exit p0

    return-object p1

    .line 84
    :catchall_0
    move-exception p1

    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw p1
.end method

.method public nextBuffer(J)[B
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/InterruptedException;
        }
    .end annotation

    .line 58
    monitor-enter p0

    .line 59
    :try_start_0
    iget-object v0, p0, Lme/embodied/perception/audio/STTWebClient$AudioBuffer;->audioBuffers_:Ljava/util/LinkedList;

    invoke-virtual {v0}, Ljava/util/LinkedList;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 60
    invoke-virtual {p0, p1, p2}, Ljava/lang/Object;->wait(J)V

    .line 61
    :cond_0
    iget-object p1, p0, Lme/embodied/perception/audio/STTWebClient$AudioBuffer;->audioBuffers_:Ljava/util/LinkedList;

    invoke-virtual {p1}, Ljava/util/LinkedList;->isEmpty()Z

    move-result p1

    if-eqz p1, :cond_1

    .line 62
    const/4 p1, 0x0

    monitor-exit p0

    return-object p1

    .line 64
    :cond_1
    iget-wide p1, p0, Lme/embodied/perception/audio/STTWebClient$AudioBuffer;->audioRootTs_:J

    const-wide/16 v0, 0x78

    add-long/2addr p1, v0

    iput-wide p1, p0, Lme/embodied/perception/audio/STTWebClient$AudioBuffer;->audioRootTs_:J

    .line 65
    iget-object p1, p0, Lme/embodied/perception/audio/STTWebClient$AudioBuffer;->audioBuffers_:Ljava/util/LinkedList;

    invoke-virtual {p1}, Ljava/util/LinkedList;->removeFirst()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [B

    monitor-exit p0

    return-object p1

    .line 66
    :catchall_0
    move-exception p1

    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw p1
.end method
