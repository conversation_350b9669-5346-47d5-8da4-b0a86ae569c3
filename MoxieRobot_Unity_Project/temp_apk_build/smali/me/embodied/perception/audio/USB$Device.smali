.class final enum Lme/embodied/perception/audio/USB$Device;
.super Ljava/lang/Enum;
.source "USB.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lme/embodied/perception/audio/USB;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x401a
    name = "Device"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lme/embodied/perception/audio/USB$Device;",
        ">;"
    }
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lme/embodied/perception/audio/USB$Device;

.field public static final enum XMOS:Lme/embodied/perception/audio/USB$Device;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 43
    new-instance v0, Lme/embodied/perception/audio/USB$Device;

    const-string v1, "XMOS"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lme/embodied/perception/audio/USB$Device;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lme/embodied/perception/audio/USB$Device;->XMOS:Lme/embodied/perception/audio/USB$Device;

    .line 42
    const/4 v1, 0x1

    new-array v1, v1, [Lme/embodied/perception/audio/USB$Device;

    aput-object v0, v1, v2

    sput-object v1, Lme/embodied/perception/audio/USB$Device;->$VALUES:[Lme/embodied/perception/audio/USB$Device;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 42
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lme/embodied/perception/audio/USB$Device;
    .locals 1

    .line 42
    const-class v0, Lme/embodied/perception/audio/USB$Device;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lme/embodied/perception/audio/USB$Device;

    return-object p0
.end method

.method public static values()[Lme/embodied/perception/audio/USB$Device;
    .locals 1

    .line 42
    sget-object v0, Lme/embodied/perception/audio/USB$Device;->$VALUES:[Lme/embodied/perception/audio/USB$Device;

    invoke-virtual {v0}, [Lme/embodied/perception/audio/USB$Device;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lme/embodied/perception/audio/USB$Device;

    return-object v0
.end method
