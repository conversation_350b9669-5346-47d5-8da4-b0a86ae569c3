.class public interface abstract Lme/embodied/perception/audio/AudioQueue;
.super Ljava/lang/Object;
.source "AudioQueue.java"


# virtual methods
.method public abstract firstBuffer(J)[Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/InterruptedException;
        }
    .end annotation
.end method

.method public abstract nextBuffer(J)[B
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/InterruptedException;
        }
    .end annotation
.end method
