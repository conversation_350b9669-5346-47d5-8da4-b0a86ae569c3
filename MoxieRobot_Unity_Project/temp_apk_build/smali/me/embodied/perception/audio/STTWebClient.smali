.class Lme/embodied/perception/audio/STTWebClient;
.super Ljava/lang/Object;
.source "STTWebClient.java"

# interfaces
.implements Lme/embodied/perception/audio/STTController;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lme/embodied/perception/audio/STTWebClient$URIMaker;,
        Lme/embodied/perception/audio/STTWebClient$AudioBuffer;
    }
.end annotation


# static fields
.field static final MAX_BUFFERS:I = 0x64

.field private static final SERVICE_URL:Ljava/lang/String; = "wss://deepgram-test.embodied.com/v2/listen/stream"

.field private static final TAG:Ljava/lang/String; = "STTWebClient"

.field static final TMP_SAMPLE_DURATION:J = 0x78L


# instance fields
.field client_:Lme/embodied/perception/audio/STTWorker;

.field private client_creator_:Ljava/util/concurrent/ExecutorService;

.field client_maker_:Ljava/lang/Thread;

.field continuous_mode_:Z

.field currentBuffer_:Lme/embodied/perception/audio/STTWebClient$AudioBuffer;

.field highWater_:I

.field native_:J

.field phraseHints_:[Ljava/lang/String;


# direct methods
.method public constructor <init>(JZ)V
    .locals 1

    .line 41
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 28
    const/4 v0, 0x0

    iput v0, p0, Lme/embodied/perception/audio/STTWebClient;->highWater_:I

    .line 33
    new-array v0, v0, [Ljava/lang/String;

    iput-object v0, p0, Lme/embodied/perception/audio/STTWebClient;->phraseHints_:[Ljava/lang/String;

    .line 87
    const/4 v0, 0x0

    iput-object v0, p0, Lme/embodied/perception/audio/STTWebClient;->currentBuffer_:Lme/embodied/perception/audio/STTWebClient$AudioBuffer;

    .line 43
    iput-wide p1, p0, Lme/embodied/perception/audio/STTWebClient;->native_:J

    .line 44
    iput-boolean p3, p0, Lme/embodied/perception/audio/STTWebClient;->continuous_mode_:Z

    .line 45
    const/4 p1, 0x2

    invoke-static {p1}, Ljava/util/concurrent/Executors;->newFixedThreadPool(I)Ljava/util/concurrent/ExecutorService;

    move-result-object p1

    iput-object p1, p0, Lme/embodied/perception/audio/STTWebClient;->client_creator_:Ljava/util/concurrent/ExecutorService;

    .line 46
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string p2, "Starting Java WS code in "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-boolean p2, p0, Lme/embodied/perception/audio/STTWebClient;->continuous_mode_:Z

    if-eqz p2, :cond_0

    const-string p2, "CONTINUOUS mode"

    goto :goto_0

    :cond_0
    const-string p2, "SPEECH mode"

    :goto_0
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const-string p2, "STTWebClient"

    invoke-static {p2, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 47
    return-void
.end method


# virtual methods
.method public OnSpeechAudio(J[B)V
    .locals 0

    .line 140
    iget-boolean p1, p0, Lme/embodied/perception/audio/STTWebClient;->continuous_mode_:Z

    if-eqz p1, :cond_0

    .line 141
    return-void

    .line 143
    :cond_0
    iget-object p1, p0, Lme/embodied/perception/audio/STTWebClient;->currentBuffer_:Lme/embodied/perception/audio/STTWebClient$AudioBuffer;

    if-eqz p1, :cond_1

    .line 145
    monitor-enter p1

    .line 147
    :try_start_0
    iget-object p2, p0, Lme/embodied/perception/audio/STTWebClient;->currentBuffer_:Lme/embodied/perception/audio/STTWebClient$AudioBuffer;

    iget-object p2, p2, Lme/embodied/perception/audio/STTWebClient$AudioBuffer;->audioBuffers_:Ljava/util/LinkedList;

    invoke-virtual {p2, p3}, Ljava/util/LinkedList;->add(Ljava/lang/Object;)Z

    .line 148
    iget-object p2, p0, Lme/embodied/perception/audio/STTWebClient;->currentBuffer_:Lme/embodied/perception/audio/STTWebClient$AudioBuffer;

    invoke-virtual {p2}, Ljava/lang/Object;->notifyAll()V

    .line 149
    monitor-exit p1

    goto :goto_0

    :catchall_0
    move-exception p2

    monitor-exit p1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw p2

    .line 151
    :cond_1
    :goto_0
    return-void
.end method

.method public OnSpeechChange(ZJ)V
    .locals 1

    .line 114
    iget-boolean v0, p0, Lme/embodied/perception/audio/STTWebClient;->continuous_mode_:Z

    if-eqz v0, :cond_0

    .line 115
    return-void

    .line 117
    :cond_0
    if-eqz p1, :cond_1

    .line 121
    new-instance p1, Lme/embodied/perception/audio/STTWebClient$AudioBuffer;

    invoke-direct {p1, p0, p2, p3}, Lme/embodied/perception/audio/STTWebClient$AudioBuffer;-><init>(Lme/embodied/perception/audio/STTWebClient;J)V

    iput-object p1, p0, Lme/embodied/perception/audio/STTWebClient;->currentBuffer_:Lme/embodied/perception/audio/STTWebClient$AudioBuffer;

    .line 122
    invoke-virtual {p0, p1}, Lme/embodied/perception/audio/STTWebClient;->spawnWorker(Lme/embodied/perception/audio/STTWebClient$AudioBuffer;)V

    goto :goto_0

    .line 124
    :cond_1
    iget-object p1, p0, Lme/embodied/perception/audio/STTWebClient;->currentBuffer_:Lme/embodied/perception/audio/STTWebClient$AudioBuffer;

    if-eqz p1, :cond_2

    .line 127
    monitor-enter p1

    .line 129
    :try_start_0
    iget-object p2, p0, Lme/embodied/perception/audio/STTWebClient;->currentBuffer_:Lme/embodied/perception/audio/STTWebClient$AudioBuffer;

    iget-object p2, p2, Lme/embodied/perception/audio/STTWebClient$AudioBuffer;->audioBuffers_:Ljava/util/LinkedList;

    sget-object p3, Lme/embodied/perception/audio/STTWorker;->INPUT_COMPLETE:[B

    invoke-virtual {p2, p3}, Ljava/util/LinkedList;->add(Ljava/lang/Object;)Z

    .line 130
    iget-object p2, p0, Lme/embodied/perception/audio/STTWebClient;->currentBuffer_:Lme/embodied/perception/audio/STTWebClient$AudioBuffer;

    invoke-virtual {p2}, Ljava/lang/Object;->notifyAll()V

    .line 131
    monitor-exit p1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 133
    const/4 p1, 0x0

    iput-object p1, p0, Lme/embodied/perception/audio/STTWebClient;->currentBuffer_:Lme/embodied/perception/audio/STTWebClient$AudioBuffer;

    .line 134
    iput-object p1, p0, Lme/embodied/perception/audio/STTWebClient;->client_:Lme/embodied/perception/audio/STTWorker;

    goto :goto_0

    .line 131
    :catchall_0
    move-exception p2

    :try_start_1
    monitor-exit p1
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw p2

    .line 136
    :cond_2
    :goto_0
    return-void
.end method

.method public OnVADAudio(IJ[B)V
    .locals 0

    .line 90
    iget-boolean p1, p0, Lme/embodied/perception/audio/STTWebClient;->continuous_mode_:Z

    if-nez p1, :cond_0

    .line 91
    return-void

    .line 93
    :cond_0
    iget-object p1, p0, Lme/embodied/perception/audio/STTWebClient;->currentBuffer_:Lme/embodied/perception/audio/STTWebClient$AudioBuffer;

    if-nez p1, :cond_1

    .line 94
    new-instance p1, Lme/embodied/perception/audio/STTWebClient$AudioBuffer;

    invoke-direct {p1, p0, p2, p3}, Lme/embodied/perception/audio/STTWebClient$AudioBuffer;-><init>(Lme/embodied/perception/audio/STTWebClient;J)V

    iput-object p1, p0, Lme/embodied/perception/audio/STTWebClient;->currentBuffer_:Lme/embodied/perception/audio/STTWebClient$AudioBuffer;

    .line 96
    :cond_1
    iget-object p1, p0, Lme/embodied/perception/audio/STTWebClient;->currentBuffer_:Lme/embodied/perception/audio/STTWebClient$AudioBuffer;

    monitor-enter p1

    .line 97
    :try_start_0
    iget-object p2, p0, Lme/embodied/perception/audio/STTWebClient;->currentBuffer_:Lme/embodied/perception/audio/STTWebClient$AudioBuffer;

    iget-object p2, p2, Lme/embodied/perception/audio/STTWebClient$AudioBuffer;->audioBuffers_:Ljava/util/LinkedList;

    invoke-virtual {p2, p4}, Ljava/util/LinkedList;->add(Ljava/lang/Object;)Z

    .line 98
    iget-object p2, p0, Lme/embodied/perception/audio/STTWebClient;->currentBuffer_:Lme/embodied/perception/audio/STTWebClient$AudioBuffer;

    iget-object p2, p2, Lme/embodied/perception/audio/STTWebClient$AudioBuffer;->audioBuffers_:Ljava/util/LinkedList;

    invoke-virtual {p2}, Ljava/util/LinkedList;->size()I

    move-result p2

    const/16 p3, 0x64

    if-le p2, p3, :cond_2

    .line 100
    const-string p2, "STTWebClient"

    const-string p3, "Overflow detected."

    invoke-static {p2, p3}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 102
    :cond_2
    iget-object p2, p0, Lme/embodied/perception/audio/STTWebClient;->currentBuffer_:Lme/embodied/perception/audio/STTWebClient$AudioBuffer;

    iget-object p2, p2, Lme/embodied/perception/audio/STTWebClient$AudioBuffer;->audioBuffers_:Ljava/util/LinkedList;

    invoke-virtual {p2}, Ljava/util/LinkedList;->size()I

    move-result p2

    iget p3, p0, Lme/embodied/perception/audio/STTWebClient;->highWater_:I

    if-le p2, p3, :cond_3

    .line 104
    iget-object p2, p0, Lme/embodied/perception/audio/STTWebClient;->currentBuffer_:Lme/embodied/perception/audio/STTWebClient$AudioBuffer;

    iget-object p2, p2, Lme/embodied/perception/audio/STTWebClient$AudioBuffer;->audioBuffers_:Ljava/util/LinkedList;

    invoke-virtual {p2}, Ljava/util/LinkedList;->size()I

    move-result p2

    iput p2, p0, Lme/embodied/perception/audio/STTWebClient;->highWater_:I

    .line 105
    const-string p2, "STTWebClient"

    new-instance p3, Ljava/lang/StringBuilder;

    invoke-direct {p3}, Ljava/lang/StringBuilder;-><init>()V

    const-string p4, "High water now UP to "

    invoke-virtual {p3, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget p4, p0, Lme/embodied/perception/audio/STTWebClient;->highWater_:I

    invoke-virtual {p3, p4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {p3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p3

    invoke-static {p2, p3}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 107
    :cond_3
    invoke-virtual {p0}, Lme/embodied/perception/audio/STTWebClient;->checkWorkers()V

    .line 108
    iget-object p2, p0, Lme/embodied/perception/audio/STTWebClient;->currentBuffer_:Lme/embodied/perception/audio/STTWebClient$AudioBuffer;

    invoke-virtual {p2}, Ljava/lang/Object;->notifyAll()V

    .line 109
    monitor-exit p1

    .line 110
    return-void

    .line 109
    :catchall_0
    move-exception p2

    monitor-exit p1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw p2
.end method

.method public ReleaseSystem()V
    .locals 2

    .line 155
    const-string v0, "STTWebClient"

    const-string v1, "RELEASE SYSTEM"

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 156
    const-wide/16 v0, 0x0

    iput-wide v0, p0, Lme/embodied/perception/audio/STTWebClient;->native_:J

    .line 157
    return-void
.end method

.method public native STTCallback(JLjava/lang/String;J)V
.end method

.method public native STTReady(JJ)V
.end method

.method public UpdatePhraseHints([Ljava/lang/String;)V
    .locals 6

    .line 161
    iput-object p1, p0, Lme/embodied/perception/audio/STTWebClient;->phraseHints_:[Ljava/lang/String;

    .line 162
    const-string v0, "STTWebClient"

    const-string v1, "UpdatePhraseHints"

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 163
    array-length v1, p1

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_0

    aget-object v3, p1, v2

    .line 164
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "UpdatePhraseHints: +"

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-static {v0, v3}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 163
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 165
    :cond_0
    return-void
.end method

.method protected checkWorkers()V
    .locals 1

    .line 172
    iget-object v0, p0, Lme/embodied/perception/audio/STTWebClient;->client_:Lme/embodied/perception/audio/STTWorker;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lme/embodied/perception/audio/STTWorker;->isDead()Z

    move-result v0

    if-eqz v0, :cond_1

    iget-object v0, p0, Lme/embodied/perception/audio/STTWebClient;->client_maker_:Ljava/lang/Thread;

    if-nez v0, :cond_1

    .line 173
    :cond_0
    new-instance v0, Lme/embodied/perception/audio/STTWebClient$1;

    invoke-direct {v0, p0}, Lme/embodied/perception/audio/STTWebClient$1;-><init>(Lme/embodied/perception/audio/STTWebClient;)V

    iput-object v0, p0, Lme/embodied/perception/audio/STTWebClient;->client_maker_:Ljava/lang/Thread;

    .line 189
    invoke-virtual {v0}, Ljava/lang/Thread;->start()V

    .line 191
    :cond_1
    return-void
.end method

.method createClient(Lme/embodied/perception/audio/STTWebClient$AudioBuffer;)Lme/embodied/perception/audio/STTWorker;
    .locals 7
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/net/URISyntaxException;,
            Ljava/io/UnsupportedEncodingException;
        }
    .end annotation

    .line 230
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    .line 231
    const-string v1, "Authorization"

    const-string v2, "bearer NEeChGAmS8b3QZnZMfln13XHWlBOU"

    invoke-interface {v0, v1, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 234
    new-instance v1, Lme/embodied/perception/audio/STTWebClient$URIMaker;

    invoke-direct {v1}, Lme/embodied/perception/audio/STTWebClient$URIMaker;-><init>()V

    .line 235
    const-string v2, "model"

    const-string v3, "general-dQw4w9WgXcQ"

    invoke-virtual {v1, v2, v3}, Lme/embodied/perception/audio/STTWebClient$URIMaker;->addParameter(Ljava/lang/String;Ljava/lang/String;)V

    .line 236
    const-string v2, "version"

    const-string v3, "2021-08-18"

    invoke-virtual {v1, v2, v3}, Lme/embodied/perception/audio/STTWebClient$URIMaker;->addParameter(Ljava/lang/String;Ljava/lang/String;)V

    .line 237
    const-string v2, "encoding"

    const-string v3, "linear16"

    invoke-virtual {v1, v2, v3}, Lme/embodied/perception/audio/STTWebClient$URIMaker;->addParameter(Ljava/lang/String;Ljava/lang/String;)V

    .line 238
    const-string v2, "sample_rate"

    const-string v3, "16000"

    invoke-virtual {v1, v2, v3}, Lme/embodied/perception/audio/STTWebClient$URIMaker;->addParameter(Ljava/lang/String;Ljava/lang/String;)V

    .line 239
    const-string v2, "keyword_boost"

    const-string v3, "standard"

    invoke-virtual {v1, v2, v3}, Lme/embodied/perception/audio/STTWebClient$URIMaker;->addParameter(Ljava/lang/String;Ljava/lang/String;)V

    .line 240
    iget-object v2, p0, Lme/embodied/perception/audio/STTWebClient;->phraseHints_:[Ljava/lang/String;

    array-length v3, v2

    const/4 v4, 0x0

    :goto_0
    if-ge v4, v3, :cond_0

    aget-object v5, v2, v4

    .line 241
    const-string v6, "keywords"

    invoke-virtual {v1, v6, v5}, Lme/embodied/perception/audio/STTWebClient$URIMaker;->addParameter(Ljava/lang/String;Ljava/lang/String;)V

    .line 240
    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    .line 244
    :cond_0
    new-instance v2, Ljava/net/URI;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "wss://deepgram-test.embodied.com/v2/listen/stream?"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Lme/embodied/perception/audio/STTWebClient$URIMaker;->build()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v2, v1}, Ljava/net/URI;-><init>(Ljava/lang/String;)V

    .line 245
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Using Client URI: "

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const-string v3, "STTWebClient"

    invoke-static {v3, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 248
    new-instance v1, Lme/embodied/perception/audio/STTWorker;

    invoke-direct {v1, v2, v0, p0, p1}, Lme/embodied/perception/audio/STTWorker;-><init>(Ljava/net/URI;Ljava/util/Map;Lme/embodied/perception/audio/STTController;Lme/embodied/perception/audio/AudioQueue;)V

    return-object v1
.end method

.method public onAudioResult(Ljava/lang/String;J)V
    .locals 6

    .line 253
    iget-wide v1, p0, Lme/embodied/perception/audio/STTWebClient;->native_:J

    const-wide/16 v3, 0x0

    cmp-long v0, v1, v3

    if-eqz v0, :cond_0

    .line 254
    move-object v0, p0

    move-object v3, p1

    move-wide v4, p2

    invoke-virtual/range {v0 .. v5}, Lme/embodied/perception/audio/STTWebClient;->STTCallback(JLjava/lang/String;J)V

    .line 256
    :cond_0
    return-void
.end method

.method public onWorkerReady(J)V
    .locals 4

    .line 260
    iget-wide v0, p0, Lme/embodied/perception/audio/STTWebClient;->native_:J

    const-wide/16 v2, 0x0

    cmp-long v2, v0, v2

    if-eqz v2, :cond_0

    .line 261
    invoke-virtual {p0, v0, v1, p1, p2}, Lme/embodied/perception/audio/STTWebClient;->STTReady(JJ)V

    .line 263
    :cond_0
    return-void
.end method

.method protected spawnWorker(Lme/embodied/perception/audio/STTWebClient$AudioBuffer;)V
    .locals 2

    .line 196
    iget-object v0, p0, Lme/embodied/perception/audio/STTWebClient;->client_creator_:Ljava/util/concurrent/ExecutorService;

    new-instance v1, Lme/embodied/perception/audio/STTWebClient$2;

    invoke-direct {v1, p0, p1}, Lme/embodied/perception/audio/STTWebClient$2;-><init>(Lme/embodied/perception/audio/STTWebClient;Lme/embodied/perception/audio/STTWebClient$AudioBuffer;)V

    invoke-interface {v0, v1}, Ljava/util/concurrent/ExecutorService;->execute(Ljava/lang/Runnable;)V

    .line 211
    return-void
.end method
