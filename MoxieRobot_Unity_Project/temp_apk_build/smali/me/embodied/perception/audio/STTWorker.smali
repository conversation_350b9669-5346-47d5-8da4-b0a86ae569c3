.class public Lme/embodied/perception/audio/STTWorker;
.super Lorg/java_websocket/client/WebSocketClient;
.source "STTWorker.java"


# static fields
.field public static final INPUT_COMPLETE:[B

.field private static final TAG:Ljava/lang/String; = "STTWorker"


# instance fields
.field active_:Z

.field audioFrames_:I

.field controller_:Lme/embodied/perception/audio/STTController;

.field dead_:Z

.field delay_counter_:I

.field delays_:[J

.field inputQueue_:Lme/embodied/perception/audio/AudioQueue;

.field realTimeStart_:J

.field startTime_:J

.field worker_:Lja<PERSON>/lang/Thread;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 15
    const/4 v0, 0x0

    new-array v0, v0, [B

    sput-object v0, Lme/embodied/perception/audio/STTWorker;->INPUT_COMPLETE:[B

    return-void
.end method

.method public constructor <init>(Ljava/net/URI;Ljava/util/Map;Lme/embodied/perception/audio/STTController;Lme/embodied/perception/audio/AudioQueue;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/net/URI;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;",
            "Lme/embodied/perception/audio/STTController;",
            "Lme/embodied/perception/audio/AudioQueue;",
            ")V"
        }
    .end annotation

    .line 27
    invoke-direct {p0, p1, p2}, Lorg/java_websocket/client/WebSocketClient;-><init>(Ljava/net/URI;Ljava/util/Map;)V

    .line 67
    const/16 p1, 0x14

    new-array p1, p1, [J

    iput-object p1, p0, Lme/embodied/perception/audio/STTWorker;->delays_:[J

    .line 68
    const/4 p1, 0x0

    iput p1, p0, Lme/embodied/perception/audio/STTWorker;->delay_counter_:I

    .line 28
    iput-object p3, p0, Lme/embodied/perception/audio/STTWorker;->controller_:Lme/embodied/perception/audio/STTController;

    .line 29
    iput-object p4, p0, Lme/embodied/perception/audio/STTWorker;->inputQueue_:Lme/embodied/perception/audio/AudioQueue;

    .line 30
    iput-boolean p1, p0, Lme/embodied/perception/audio/STTWorker;->active_:Z

    .line 31
    return-void
.end method


# virtual methods
.method countDelay()V
    .locals 8

    .line 71
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    iget-wide v2, p0, Lme/embodied/perception/audio/STTWorker;->startTime_:J

    iget v4, p0, Lme/embodied/perception/audio/STTWorker;->audioFrames_:I

    int-to-long v4, v4

    const-wide/16 v6, 0x78

    mul-long/2addr v4, v6

    add-long/2addr v2, v4

    sub-long/2addr v0, v2

    .line 72
    iget-object v2, p0, Lme/embodied/perception/audio/STTWorker;->delays_:[J

    iget v3, p0, Lme/embodied/perception/audio/STTWorker;->delay_counter_:I

    add-int/lit8 v4, v3, 0x1

    iput v4, p0, Lme/embodied/perception/audio/STTWorker;->delay_counter_:I

    aput-wide v0, v2, v3

    .line 73
    array-length v0, v2

    if-lt v4, v0, :cond_1

    .line 75
    const-wide/16 v0, 0x0

    .line 76
    array-length v3, v2

    const/4 v4, 0x0

    move v5, v4

    :goto_0
    if-ge v5, v3, :cond_0

    aget-wide v6, v2, v5

    .line 78
    add-long/2addr v0, v6

    .line 76
    add-int/lit8 v5, v5, 0x1

    goto :goto_0

    .line 80
    :cond_0
    iput v4, p0, Lme/embodied/perception/audio/STTWorker;->delay_counter_:I

    .line 81
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Average Internal STT Delay: "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v3, p0, Lme/embodied/perception/audio/STTWorker;->delays_:[J

    array-length v3, v3

    int-to-long v3, v3

    div-long/2addr v0, v3

    invoke-virtual {v2, v0, v1}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "STTWorker"

    invoke-static {v1, v0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 83
    :cond_1
    return-void
.end method

.method public endMe()V
    .locals 8

    .line 38
    const/4 v0, 0x1

    iput-boolean v0, p0, Lme/embodied/perception/audio/STTWorker;->dead_:Z

    .line 39
    const/4 v1, 0x0

    iput-boolean v1, p0, Lme/embodied/perception/audio/STTWorker;->active_:Z

    .line 40
    iget-object v2, p0, Lme/embodied/perception/audio/STTWorker;->worker_:Ljava/lang/Thread;

    if-eqz v2, :cond_0

    .line 41
    invoke-virtual {v2}, Ljava/lang/Thread;->interrupt()V

    .line 44
    :cond_0
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v2

    iget-wide v4, p0, Lme/embodied/perception/audio/STTWorker;->realTimeStart_:J

    sub-long/2addr v2, v4

    .line 45
    const-wide/16 v4, 0x78

    iget v6, p0, Lme/embodied/perception/audio/STTWorker;->audioFrames_:I

    int-to-long v6, v6

    mul-long/2addr v6, v4

    .line 46
    const/4 v4, 0x2

    new-array v4, v4, [Ljava/lang/Object;

    invoke-static {v2, v3}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v2

    aput-object v2, v4, v1

    invoke-static {v6, v7}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v1

    aput-object v1, v4, v0

    const-string v0, "Connected for %d ms and sent %d ms worth of audio."

    invoke-static {v0, v4}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    const-string v1, "STTWorker"

    invoke-static {v1, v0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 47
    return-void
.end method

.method getBufferOrStart()[B
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    .line 86
    iget-wide v0, p0, Lme/embodied/perception/audio/STTWorker;->startTime_:J

    const-wide/16 v2, 0x0

    cmp-long v0, v0, v2

    const-wide/16 v1, 0x3e8

    const/4 v3, 0x1

    if-nez v0, :cond_1

    .line 88
    iget-object v0, p0, Lme/embodied/perception/audio/STTWorker;->inputQueue_:Lme/embodied/perception/audio/AudioQueue;

    invoke-interface {v0, v1, v2}, Lme/embodied/perception/audio/AudioQueue;->firstBuffer(J)[Ljava/lang/Object;

    move-result-object v0

    .line 89
    if-eqz v0, :cond_0

    .line 91
    const/4 v1, 0x0

    aget-object v1, v0, v1

    check-cast v1, Ljava/lang/Long;

    invoke-virtual {v1}, Ljava/lang/Long;->longValue()J

    move-result-wide v1

    iput-wide v1, p0, Lme/embodied/perception/audio/STTWorker;->startTime_:J

    .line 92
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v1

    iput-wide v1, p0, Lme/embodied/perception/audio/STTWorker;->realTimeStart_:J

    .line 93
    iget v1, p0, Lme/embodied/perception/audio/STTWorker;->audioFrames_:I

    add-int/2addr v1, v3

    iput v1, p0, Lme/embodied/perception/audio/STTWorker;->audioFrames_:I

    .line 94
    aget-object v0, v0, v3

    check-cast v0, [B

    check-cast v0, [B

    return-object v0

    .line 96
    :cond_0
    const/4 v0, 0x0

    return-object v0

    .line 100
    :cond_1
    iget-object v0, p0, Lme/embodied/perception/audio/STTWorker;->inputQueue_:Lme/embodied/perception/audio/AudioQueue;

    invoke-interface {v0, v1, v2}, Lme/embodied/perception/audio/AudioQueue;->nextBuffer(J)[B

    move-result-object v0

    .line 101
    if-eqz v0, :cond_2

    .line 102
    iget v1, p0, Lme/embodied/perception/audio/STTWorker;->audioFrames_:I

    add-int/2addr v1, v3

    iput v1, p0, Lme/embodied/perception/audio/STTWorker;->audioFrames_:I

    .line 103
    invoke-virtual {p0}, Lme/embodied/perception/audio/STTWorker;->countDelay()V

    .line 105
    :cond_2
    return-object v0
.end method

.method public isDead()Z
    .locals 1

    .line 34
    iget-boolean v0, p0, Lme/embodied/perception/audio/STTWorker;->dead_:Z

    return v0
.end method

.method public onClose(ILjava/lang/String;Z)V
    .locals 2

    .line 51
    const/4 v0, 0x3

    new-array v0, v0, [Ljava/lang/Object;

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    const/4 v1, 0x0

    aput-object p1, v0, v1

    const/4 p1, 0x1

    aput-object p2, v0, p1

    invoke-static {p3}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p1

    const/4 p2, 0x2

    aput-object p1, v0, p2

    const-string p1, "Connection onClose(%d,%s,%s)"

    invoke-static {p1, v0}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    const-string p2, "STTWorker"

    invoke-static {p2, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 52
    invoke-virtual {p0}, Lme/embodied/perception/audio/STTWorker;->endMe()V

    .line 53
    return-void
.end method

.method public onError(Ljava/lang/Exception;)V
    .locals 2

    .line 57
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Connection onError."

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const-string v0, "STTWorker"

    invoke-static {v0, p1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 58
    invoke-virtual {p0}, Lme/embodied/perception/audio/STTWorker;->endMe()V

    .line 59
    return-void
.end method

.method public onMessage(Ljava/lang/String;)V
    .locals 3

    .line 63
    const-string v0, "STTWorker"

    const-string v1, "RX message"

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 64
    iget-object v0, p0, Lme/embodied/perception/audio/STTWorker;->controller_:Lme/embodied/perception/audio/STTController;

    iget-wide v1, p0, Lme/embodied/perception/audio/STTWorker;->startTime_:J

    invoke-interface {v0, p1, v1, v2}, Lme/embodied/perception/audio/STTController;->onAudioResult(Ljava/lang/String;J)V

    .line 65
    return-void
.end method

.method public onOpen(Lorg/java_websocket/handshake/ServerHandshake;)V
    .locals 2

    .line 111
    const-string p1, "STTWorker"

    const-string v0, "Connection opened."

    invoke-static {p1, v0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 112
    iget-object p1, p0, Lme/embodied/perception/audio/STTWorker;->controller_:Lme/embodied/perception/audio/STTController;

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    invoke-interface {p1, v0, v1}, Lme/embodied/perception/audio/STTController;->onWorkerReady(J)V

    .line 113
    const/4 p1, 0x1

    iput-boolean p1, p0, Lme/embodied/perception/audio/STTWorker;->active_:Z

    .line 114
    new-instance p1, Lme/embodied/perception/audio/STTWorker$1;

    invoke-direct {p1, p0}, Lme/embodied/perception/audio/STTWorker$1;-><init>(Lme/embodied/perception/audio/STTWorker;)V

    iput-object p1, p0, Lme/embodied/perception/audio/STTWorker;->worker_:Ljava/lang/Thread;

    .line 138
    invoke-virtual {p1}, Ljava/lang/Thread;->start()V

    .line 139
    return-void
.end method
