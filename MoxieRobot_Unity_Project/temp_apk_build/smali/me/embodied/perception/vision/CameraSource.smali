.class public Lme/embodied/perception/vision/CameraSource;
.super Ljava/lang/Object;
.source "CameraSource.java"

# interfaces
.implements Landroid/hardware/Camera$PreviewCallback;


# static fields
.field private static final DUMMY_TEXTURE_ID:I = 0x64

.field private static final NUM_BUFFERS:I = 0x5

.field private static final TAG:Ljava/lang/String; = "EBCamera"


# instance fields
.field buffers_:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "[B>;"
        }
    .end annotation
.end field

.field camera_:Landroid/hardware/Camera;

.field context_:Landroid/content/Context;

.field frame_count_:I

.field height_:I

.field ptr_:J

.field resolution_:I

.field rotation_:I

.field texture_:Landroid/graphics/SurfaceTexture;

.field timestamp_:J

.field width_:I


# direct methods
.method public constructor <init>(IJLandroid/content/Context;)V
    .locals 0

    .line 52
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 53
    iput-object p4, p0, Lme/embodied/perception/vision/CameraSource;->context_:Landroid/content/Context;

    .line 54
    iput p1, p0, Lme/embodied/perception/vision/CameraSource;->resolution_:I

    .line 55
    iput-wide p2, p0, Lme/embodied/perception/vision/CameraSource;->ptr_:J

    .line 56
    new-instance p1, Landroid/graphics/SurfaceTexture;

    const/16 p2, 0x64

    invoke-direct {p1, p2}, Landroid/graphics/SurfaceTexture;-><init>(I)V

    iput-object p1, p0, Lme/embodied/perception/vision/CameraSource;->texture_:Landroid/graphics/SurfaceTexture;

    .line 57
    const/4 p1, 0x0

    iput-object p1, p0, Lme/embodied/perception/vision/CameraSource;->camera_:Landroid/hardware/Camera;

    .line 58
    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    iput-object p1, p0, Lme/embodied/perception/vision/CameraSource;->buffers_:Ljava/util/List;

    .line 59
    const-wide/16 p1, -0x1

    iput-wide p1, p0, Lme/embodied/perception/vision/CameraSource;->timestamp_:J

    .line 61
    return-void
.end method

.method private AddCallbackBuffers(Landroid/hardware/Camera;)V
    .locals 4

    .line 155
    const/16 v0, 0x11

    invoke-static {v0}, Landroid/graphics/ImageFormat;->getBitsPerPixel(I)I

    move-result v0

    .line 156
    iget v1, p0, Lme/embodied/perception/vision/CameraSource;->width_:I

    iget v2, p0, Lme/embodied/perception/vision/CameraSource;->height_:I

    mul-int/2addr v1, v2

    mul-int/2addr v1, v0

    int-to-long v0, v1

    .line 157
    long-to-double v0, v0

    const-wide/high16 v2, 0x4020000000000000L    # 8.0

    div-double/2addr v0, v2

    invoke-static {v0, v1}, Ljava/lang/Math;->ceil(D)D

    move-result-wide v0

    double-to-int v0, v0

    add-int/lit8 v0, v0, 0x1

    .line 158
    const-string v1, "EBCamera"

    const-string v2, "creating the buffers"

    invoke-static {v1, v2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 159
    :goto_0
    iget-object v2, p0, Lme/embodied/perception/vision/CameraSource;->buffers_:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v2

    const/4 v3, 0x5

    if-ge v2, v3, :cond_0

    .line 160
    iget-object v2, p0, Lme/embodied/perception/vision/CameraSource;->buffers_:Ljava/util/List;

    new-array v3, v0, [B

    invoke-interface {v2, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 162
    :cond_0
    const-string v0, "adding the buffers to the camera"

    invoke-static {v1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 163
    iget-object v0, p0, Lme/embodied/perception/vision/CameraSource;->buffers_:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, [B

    .line 164
    invoke-virtual {p1, v1}, Landroid/hardware/Camera;->addCallbackBuffer([B)V

    goto :goto_1

    .line 165
    :cond_1
    return-void
.end method

.method private native FrameCallback(J[BIIF)V
.end method

.method private GetCameraInstance()Landroid/hardware/Camera;
    .locals 8

    .line 97
    const-string v0, "EBCamera"

    const-string v1, "Getting the camera instance"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 98
    invoke-direct {p0}, Lme/embodied/perception/vision/CameraSource;->GetFrontFacingCamera()I

    move-result v1

    .line 99
    const/4 v2, 0x1

    new-array v3, v2, [Ljava/lang/Object;

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    const/4 v5, 0x0

    aput-object v4, v3, v5

    const-string v4, "Camera id: %d"

    invoke-static {v4, v3}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v3

    invoke-static {v0, v3}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 100
    if-ltz v1, :cond_3

    .line 105
    const-string v3, "opening the camera"

    invoke-static {v0, v3}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 106
    invoke-static {v1}, Landroid/hardware/Camera;->open(I)Landroid/hardware/Camera;

    move-result-object v3

    .line 107
    const-string v4, "opened the camera"

    invoke-static {v0, v4}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 108
    invoke-virtual {v3}, Landroid/hardware/Camera;->getParameters()Landroid/hardware/Camera$Parameters;

    move-result-object v4

    .line 110
    invoke-virtual {v4}, Landroid/hardware/Camera$Parameters;->getSupportedPreviewFormats()Ljava/util/List;

    .line 111
    invoke-virtual {v4}, Landroid/hardware/Camera$Parameters;->getSupportedPreviewSizes()Ljava/util/List;

    .line 112
    invoke-virtual {v4}, Landroid/hardware/Camera$Parameters;->getSupportedPictureSizes()Ljava/util/List;

    .line 113
    invoke-virtual {v4}, Landroid/hardware/Camera$Parameters;->getSupportedPreviewFpsRange()Ljava/util/List;

    .line 115
    iget v6, p0, Lme/embodied/perception/vision/CameraSource;->resolution_:I

    const/16 v7, 0x500

    if-ne v6, v7, :cond_0

    .line 117
    iput v7, p0, Lme/embodied/perception/vision/CameraSource;->width_:I

    .line 118
    const/16 v2, 0x2d0

    iput v2, p0, Lme/embodied/perception/vision/CameraSource;->height_:I

    goto :goto_0

    .line 120
    :cond_0
    const/16 v7, 0x780

    if-ne v6, v7, :cond_2

    .line 122
    iput v7, p0, Lme/embodied/perception/vision/CameraSource;->width_:I

    .line 123
    const/16 v2, 0x438

    iput v2, p0, Lme/embodied/perception/vision/CameraSource;->height_:I

    .line 132
    :goto_0
    iget v2, p0, Lme/embodied/perception/vision/CameraSource;->width_:I

    iget v5, p0, Lme/embodied/perception/vision/CameraSource;->height_:I

    invoke-virtual {v4, v2, v5}, Landroid/hardware/Camera$Parameters;->setPreviewSize(II)V

    .line 133
    sget v2, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v5, 0x1b

    if-lt v2, v5, :cond_1

    .line 134
    const/16 v2, 0x7530

    invoke-virtual {v4, v2, v2}, Landroid/hardware/Camera$Parameters;->setPreviewFpsRange(II)V

    goto :goto_1

    .line 136
    :cond_1
    const/16 v2, 0x1e

    invoke-virtual {v4, v2, v2}, Landroid/hardware/Camera$Parameters;->setPreviewFpsRange(II)V

    .line 137
    :goto_1
    const/16 v2, 0x11

    invoke-virtual {v4, v2}, Landroid/hardware/Camera$Parameters;->setPreviewFormat(I)V

    .line 139
    const-string v2, "created the parameters"

    invoke-static {v0, v2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 141
    invoke-direct {p0, v3, v4, v1}, Lme/embodied/perception/vision/CameraSource;->setRotation(Landroid/hardware/Camera;Landroid/hardware/Camera$Parameters;I)V

    .line 143
    const-string v1, "set the rotation"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 144
    invoke-virtual {v3, v4}, Landroid/hardware/Camera;->setParameters(Landroid/hardware/Camera$Parameters;)V

    .line 145
    const-string v1, "set the parameters"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 146
    invoke-virtual {v3, p0}, Landroid/hardware/Camera;->setPreviewCallbackWithBuffer(Landroid/hardware/Camera$PreviewCallback;)V

    .line 147
    const-string v1, "set the preview callback"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 148
    invoke-direct {p0, v3}, Lme/embodied/perception/vision/CameraSource;->AddCallbackBuffers(Landroid/hardware/Camera;)V

    .line 149
    const-string v1, "added the callback buffers"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 150
    return-object v3

    .line 127
    :cond_2
    new-array v1, v2, [Ljava/lang/Object;

    invoke-static {v6}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    aput-object v2, v1, v5

    const-string v2, "unsupported resolution %d"

    invoke-static {v2, v1}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 128
    new-instance v0, Ljava/lang/RuntimeException;

    const-string v1, "Only supports 1920x1280 and 1280x720"

    invoke-direct {v0, v1}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 102
    :cond_3
    const-string v1, "Couldn\'t get the camera?"

    invoke-static {v0, v1}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 103
    new-instance v0, Ljava/lang/RuntimeException;

    const-string v1, "No camera!"

    invoke-direct {v0, v1}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method private GetFrontFacingCamera()I
    .locals 6

    .line 211
    invoke-static {}, Landroid/hardware/Camera;->getNumberOfCameras()I

    move-result v0

    .line 212
    new-instance v1, Landroid/hardware/Camera$CameraInfo;

    invoke-direct {v1}, Landroid/hardware/Camera$CameraInfo;-><init>()V

    .line 213
    const/4 v2, 0x0

    move v3, v2

    :goto_0
    if-ge v3, v0, :cond_1

    .line 215
    invoke-static {v3, v1}, Landroid/hardware/Camera;->getCameraInfo(ILandroid/hardware/Camera$CameraInfo;)V

    .line 216
    iget v4, v1, Landroid/hardware/Camera$CameraInfo;->facing:I

    const/4 v5, 0x1

    if-ne v4, v5, :cond_0

    .line 217
    return v3

    .line 213
    :cond_0
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 219
    :cond_1
    if-lez v0, :cond_2

    goto :goto_1

    :cond_2
    const/4 v2, -0x1

    :goto_1
    return v2
.end method

.method private setRotation(Landroid/hardware/Camera;Landroid/hardware/Camera$Parameters;I)V
    .locals 5

    .line 169
    iget-object v0, p0, Lme/embodied/perception/vision/CameraSource;->context_:Landroid/content/Context;

    const-string v1, "window"

    invoke-virtual {v0, v1}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/view/WindowManager;

    .line 170
    nop

    .line 171
    invoke-interface {v0}, Landroid/view/WindowManager;->getDefaultDisplay()Landroid/view/Display;

    move-result-object v0

    invoke-virtual {v0}, Landroid/view/Display;->getRotation()I

    move-result v0

    .line 172
    const/4 v1, 0x1

    const/4 v2, 0x0

    if-eqz v0, :cond_3

    if-eq v0, v1, :cond_2

    const/4 v3, 0x2

    if-eq v0, v3, :cond_1

    const/4 v3, 0x3

    if-eq v0, v3, :cond_0

    .line 186
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "Bad rotation value: "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v3, "EBCamera"

    invoke-static {v3, v0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_0

    .line 183
    :cond_0
    const/16 v2, 0x10e

    .line 184
    goto :goto_0

    .line 180
    :cond_1
    const/16 v2, 0xb4

    .line 181
    goto :goto_0

    .line 177
    :cond_2
    nop

    .line 178
    const/16 v2, 0x5a

    goto :goto_0

    .line 174
    :cond_3
    nop

    .line 175
    nop

    .line 189
    :goto_0
    new-instance v0, Landroid/hardware/Camera$CameraInfo;

    invoke-direct {v0}, Landroid/hardware/Camera$CameraInfo;-><init>()V

    .line 190
    invoke-static {p3, v0}, Landroid/hardware/Camera;->getCameraInfo(ILandroid/hardware/Camera$CameraInfo;)V

    .line 194
    iget p3, v0, Landroid/hardware/Camera$CameraInfo;->facing:I

    if-ne p3, v1, :cond_4

    .line 195
    iget p3, v0, Landroid/hardware/Camera$CameraInfo;->orientation:I

    add-int/2addr p3, v2

    rem-int/lit16 p3, p3, 0x168

    .line 196
    rsub-int v0, p3, 0x168

    rem-int/lit16 v0, v0, 0x168

    goto :goto_1

    .line 198
    :cond_4
    iget p3, v0, Landroid/hardware/Camera$CameraInfo;->orientation:I

    sub-int/2addr p3, v2

    add-int/lit16 p3, p3, 0x168

    rem-int/lit16 p3, p3, 0x168

    .line 199
    move v0, p3

    .line 203
    :goto_1
    div-int/lit8 v1, p3, 0x5a

    iput v1, p0, Lme/embodied/perception/vision/CameraSource;->rotation_:I

    .line 205
    invoke-virtual {p1, v0}, Landroid/hardware/Camera;->setDisplayOrientation(I)V

    .line 206
    invoke-virtual {p2, p3}, Landroid/hardware/Camera$Parameters;->setRotation(I)V

    .line 207
    return-void
.end method


# virtual methods
.method public Start()V
    .locals 4

    .line 65
    const-string v0, "EBCamera"

    const-string v1, "Start called"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 66
    const/4 v1, 0x1

    new-array v1, v1, [Ljava/lang/Object;

    iget-object v2, p0, Lme/embodied/perception/vision/CameraSource;->camera_:Landroid/hardware/Camera;

    if-nez v2, :cond_0

    const-string v2, "yes"

    goto :goto_0

    :cond_0
    const-string v2, "no"

    :goto_0
    const/4 v3, 0x0

    aput-object v2, v1, v3

    const-string v2, "camera is null? %s"

    invoke-static {v2, v1}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 67
    iget-object v1, p0, Lme/embodied/perception/vision/CameraSource;->camera_:Landroid/hardware/Camera;

    if-eqz v1, :cond_1

    .line 68
    return-void

    .line 69
    :cond_1
    invoke-direct {p0}, Lme/embodied/perception/vision/CameraSource;->GetCameraInstance()Landroid/hardware/Camera;

    move-result-object v1

    iput-object v1, p0, Lme/embodied/perception/vision/CameraSource;->camera_:Landroid/hardware/Camera;

    .line 70
    const-string v1, "got camera instance"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 73
    :try_start_0
    iget-object v1, p0, Lme/embodied/perception/vision/CameraSource;->camera_:Landroid/hardware/Camera;

    iget-object v2, p0, Lme/embodied/perception/vision/CameraSource;->texture_:Landroid/graphics/SurfaceTexture;

    invoke-virtual {v1, v2}, Landroid/hardware/Camera;->setPreviewTexture(Landroid/graphics/SurfaceTexture;)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    .line 79
    nop

    .line 81
    const-string v1, "STARTING THE PREVIEW"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 83
    iget-object v0, p0, Lme/embodied/perception/vision/CameraSource;->camera_:Landroid/hardware/Camera;

    invoke-virtual {v0}, Landroid/hardware/Camera;->startPreview()V

    .line 84
    return-void

    .line 76
    :catch_0
    move-exception v0

    .line 78
    new-instance v1, Ljava/lang/RuntimeException;

    invoke-direct {v1, v0}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/Throwable;)V

    throw v1
.end method

.method public Stop()V
    .locals 2

    .line 88
    const-string v0, "EBCamera"

    const-string v1, "STOP CAMERA CALLED"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 89
    iget-object v0, p0, Lme/embodied/perception/vision/CameraSource;->camera_:Landroid/hardware/Camera;

    if-nez v0, :cond_0

    .line 90
    return-void

    .line 91
    :cond_0
    invoke-virtual {v0}, Landroid/hardware/Camera;->stopPreview()V

    .line 92
    const/4 v0, 0x0

    iput-object v0, p0, Lme/embodied/perception/vision/CameraSource;->camera_:Landroid/hardware/Camera;

    .line 93
    return-void
.end method

.method public onPreviewFrame([BLandroid/hardware/Camera;)V
    .locals 11

    .line 224
    const-string v0, "EBCamera"

    const-string v1, "got preview frame"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 225
    iget-wide v1, p0, Lme/embodied/perception/vision/CameraSource;->timestamp_:J

    const-wide/16 v3, 0x0

    cmp-long v1, v1, v3

    if-gez v1, :cond_0

    .line 226
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v1

    iput-wide v1, p0, Lme/embodied/perception/vision/CameraSource;->timestamp_:J

    .line 227
    :cond_0
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v1

    iget-wide v3, p0, Lme/embodied/perception/vision/CameraSource;->timestamp_:J

    sub-long/2addr v1, v3

    long-to-float v1, v1

    const/high16 v2, 0x447a0000    # 1000.0f

    div-float/2addr v1, v2

    .line 228
    iget v2, p0, Lme/embodied/perception/vision/CameraSource;->frame_count_:I

    const/4 v10, 0x1

    add-int/2addr v2, v10

    iput v2, p0, Lme/embodied/perception/vision/CameraSource;->frame_count_:I

    .line 229
    iget-wide v4, p0, Lme/embodied/perception/vision/CameraSource;->ptr_:J

    iget v7, p0, Lme/embodied/perception/vision/CameraSource;->width_:I

    iget v8, p0, Lme/embodied/perception/vision/CameraSource;->height_:I

    move-object v3, p0

    move-object v6, p1

    move v9, v1

    invoke-direct/range {v3 .. v9}, Lme/embodied/perception/vision/CameraSource;->FrameCallback(J[BIIF)V

    .line 230
    const/4 v2, 0x2

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    iget v4, p0, Lme/embodied/perception/vision/CameraSource;->frame_count_:I

    int-to-float v4, v4

    div-float/2addr v4, v1

    invoke-static {v4}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v1

    aput-object v1, v2, v3

    iget v1, p0, Lme/embodied/perception/vision/CameraSource;->frame_count_:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    aput-object v1, v2, v10

    const-string v1, "FPS: %f\tframes: %d"

    invoke-static {v1, v2}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 231
    invoke-virtual {p2, p1}, Landroid/hardware/Camera;->addCallbackBuffer([B)V

    .line 232
    return-void
.end method
