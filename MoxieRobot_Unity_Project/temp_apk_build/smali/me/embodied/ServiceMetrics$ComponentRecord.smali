.class Lme/embodied/ServiceMetrics$ComponentRecord;
.super Ljava/lang/Object;
.source "ServiceMetrics.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lme/embodied/ServiceMetrics;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x8
    name = "ComponentRecord"
.end annotation


# instance fields
.field public component_:Landroid/content/ComponentName;

.field public error_uid_:I

.field public failure_count_:I

.field public first_started_ts_:J

.field public flag_internal_start_:Z

.field public last_started_callback_ts_:J

.field public last_started_ts_:J

.field public start_callbacks_:I

.field public start_count_:I


# direct methods
.method public constructor <init>(Landroid/content/ComponentName;I)V
    .locals 2

    .line 33
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 34
    iput-object p1, p0, Lme/embodied/ServiceMetrics$ComponentRecord;->component_:Landroid/content/ComponentName;

    .line 35
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    iput-wide v0, p0, Lme/embodied/ServiceMetrics$ComponentRecord;->first_started_ts_:J

    .line 36
    iput-wide v0, p0, Lme/embodied/ServiceMetrics$ComponentRecord;->last_started_ts_:J

    .line 37
    iput p2, p0, Lme/embodied/ServiceMetrics$ComponentRecord;->error_uid_:I

    .line 38
    const/4 p1, 0x1

    iput-boolean p1, p0, Lme/embodied/ServiceMetrics$ComponentRecord;->flag_internal_start_:Z

    .line 39
    return-void
.end method


# virtual methods
.method public handleRecover(Z)Z
    .locals 10

    .line 59
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "BO#8001 Restarting dead service: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lme/embodied/ServiceMetrics$ComponentRecord;->component_:Landroid/content/ComponentName;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    if-eqz p1, :cond_0

    const-string p1, " (system restart)"

    goto :goto_0

    :cond_0
    const-string p1, " (launcher restart)"

    :goto_0
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const-string v0, "ServiceMetrics"

    invoke-static {v0, p1}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 60
    iget p1, p0, Lme/embodied/ServiceMetrics$ComponentRecord;->error_uid_:I

    if-lez p1, :cond_1

    .line 62
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "BO#"

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v1, p0, Lme/embodied/ServiceMetrics$ComponentRecord;->error_uid_:I

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v1, " Service had to be restarted: "

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lme/embodied/ServiceMetrics$ComponentRecord;->component_:Landroid/content/ComponentName;

    invoke-virtual {v1}, Landroid/content/ComponentName;->getShortClassName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v0, p1}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 64
    :cond_1
    nop

    .line 65
    iget p1, p0, Lme/embodied/ServiceMetrics$ComponentRecord;->start_count_:I

    const/4 v1, 0x1

    add-int/2addr p1, v1

    iput p1, p0, Lme/embodied/ServiceMetrics$ComponentRecord;->start_count_:I

    .line 66
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v2

    .line 67
    iget-wide v4, p0, Lme/embodied/ServiceMetrics$ComponentRecord;->last_started_ts_:J

    sub-long v4, v2, v4

    .line 68
    const-wide/16 v6, 0x4e20

    cmp-long p1, v4, v6

    const/4 v6, 0x0

    const-string v7, "ms"

    const-string v8, "Component "

    if-gez p1, :cond_3

    .line 69
    iget p1, p0, Lme/embodied/ServiceMetrics$ComponentRecord;->failure_count_:I

    add-int/2addr p1, v1

    iput p1, p0, Lme/embodied/ServiceMetrics$ComponentRecord;->failure_count_:I

    const/16 v9, 0x14

    if-lt p1, v9, :cond_2

    .line 70
    goto :goto_1

    .line 69
    :cond_2
    move v6, v1

    .line 72
    :goto_1
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p1, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v8, p0, Lme/embodied/ServiceMetrics$ComponentRecord;->component_:Landroid/content/ComponentName;

    invoke-virtual {v8}, Landroid/content/ComponentName;->getShortClassName()Ljava/lang/String;

    move-result-object v8

    invoke-virtual {p1, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v8, " restarted after a failed ("

    invoke-virtual {p1, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v8, p0, Lme/embodied/ServiceMetrics$ComponentRecord;->failure_count_:I

    invoke-virtual {p1, v8}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v8, ") run of "

    invoke-virtual {p1, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1, v4, v5}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {p1, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v0, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_2

    .line 74
    :cond_3
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p1, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v8, p0, Lme/embodied/ServiceMetrics$ComponentRecord;->component_:Landroid/content/ComponentName;

    invoke-virtual {v8}, Landroid/content/ComponentName;->getShortClassName()Ljava/lang/String;

    move-result-object v8

    invoke-virtual {p1, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v8, " restarted after a successful run of "

    invoke-virtual {p1, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1, v4, v5}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {p1, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v0, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 75
    iput v6, p0, Lme/embodied/ServiceMetrics$ComponentRecord;->failure_count_:I

    move v6, v1

    .line 77
    :goto_2
    iput-wide v2, p0, Lme/embodied/ServiceMetrics$ComponentRecord;->last_started_ts_:J

    .line 78
    iput-boolean v1, p0, Lme/embodied/ServiceMetrics$ComponentRecord;->flag_internal_start_:Z

    .line 79
    return v6
.end method

.method public handleStartupDetect()Z
    .locals 5

    .line 43
    nop

    .line 44
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    iput-wide v0, p0, Lme/embodied/ServiceMetrics$ComponentRecord;->last_started_callback_ts_:J

    .line 45
    iget v0, p0, Lme/embodied/ServiceMetrics$ComponentRecord;->start_callbacks_:I

    const/4 v1, 0x1

    add-int/2addr v0, v1

    iput v0, p0, Lme/embodied/ServiceMetrics$ComponentRecord;->start_callbacks_:I

    .line 46
    iget-boolean v0, p0, Lme/embodied/ServiceMetrics$ComponentRecord;->flag_internal_start_:Z

    const-string v2, " times"

    const-string v3, "ServiceMetrics"

    if-eqz v0, :cond_0

    .line 47
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "Component "

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v4, p0, Lme/embodied/ServiceMetrics$ComponentRecord;->component_:Landroid/content/ComponentName;

    invoke-virtual {v4}, Landroid/content/ComponentName;->getShortClassName()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v4, " has signaled startup "

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v4, p0, Lme/embodied/ServiceMetrics$ComponentRecord;->start_callbacks_:I

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v3, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 48
    const/4 v0, 0x0

    iput-boolean v0, p0, Lme/embodied/ServiceMetrics$ComponentRecord;->flag_internal_start_:Z

    goto :goto_0

    .line 51
    :cond_0
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "System restart of component "

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v4, p0, Lme/embodied/ServiceMetrics$ComponentRecord;->component_:Landroid/content/ComponentName;

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v4, " detected.  Component has signaled startup "

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget v4, p0, Lme/embodied/ServiceMetrics$ComponentRecord;->start_callbacks_:I

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v3, v0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 52
    invoke-virtual {p0, v1}, Lme/embodied/ServiceMetrics$ComponentRecord;->handleRecover(Z)Z

    move-result v1

    .line 54
    :goto_0
    return v1
.end method
