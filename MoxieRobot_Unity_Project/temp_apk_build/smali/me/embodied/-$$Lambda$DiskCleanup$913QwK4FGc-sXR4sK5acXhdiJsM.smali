.class public final synthetic Lme/embodied/-$$Lambda$DiskCleanup$913QwK4FGc-sXR4sK5acXhdiJsM;
.super Ljava/lang/Object;
.source "lambda"

# interfaces
.implements Ljava/io/FileFilter;


# instance fields
.field public final synthetic f$0:Lme/embodied/DiskCleanup;

.field public final synthetic f$1:Ljava/util/regex/Pattern;


# direct methods
.method public synthetic constructor <init>(Lme/embodied/DiskCleanup;Ljava/util/regex/Pattern;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lme/embodied/-$$Lambda$DiskCleanup$913QwK4FGc-sXR4sK5acXhdiJsM;->f$0:Lme/embodied/DiskCleanup;

    iput-object p2, p0, Lme/embodied/-$$Lambda$DiskCleanup$913QwK4FGc-sXR4sK5acXhdiJsM;->f$1:Ljava/util/regex/Pattern;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/io/File;)Z
    .locals 2

    iget-object v0, p0, Lme/embodied/-$$Lambda$DiskCleanup$913QwK4FGc-sXR4sK5acXhdiJsM;->f$0:Lme/embodied/DiskCleanup;

    iget-object v1, p0, Lme/embodied/-$$Lambda$DiskCleanup$913QwK4FGc-sXR4sK5acXhdiJsM;->f$1:Ljava/util/regex/Pattern;

    invoke-virtual {v0, v1, p1}, Lme/embodied/DiskCleanup;->lambda$ensureFreeSpace$2$DiskCleanup(Ljava/util/regex/Pattern;Ljava/io/File;)Z

    move-result p1

    return p1
.end method
