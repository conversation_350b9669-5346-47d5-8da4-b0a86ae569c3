.class Lme/embodied/TransitionAnimation$1;
.super Landroid/animation/AnimatorListenerAdapter;
.source "TransitionAnimation.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lme/embodied/TransitionAnimation;->setVisible(Z)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lme/embodied/TransitionAnimation;


# direct methods
.method constructor <init>(Lme/embodied/TransitionAnimation;)V
    .locals 0

    .line 101
    iput-object p1, p0, Lme/embodied/TransitionAnimation$1;->this$0:Lme/embodied/TransitionAnimation;

    invoke-direct {p0}, Landroid/animation/AnimatorListenerAdapter;-><init>()V

    return-void
.end method


# virtual methods
.method public onAnimationEnd(Landroid/animation/Animator;)V
    .locals 1

    .line 104
    iget-object p1, p0, Lme/embodied/TransitionAnimation$1;->this$0:Lme/embodied/TransitionAnimation;

    iget-object p1, p1, Lme/embodied/TransitionAnimation;->animWindow_:Landroid/widget/FrameLayout;

    const/4 v0, 0x4

    invoke-virtual {p1, v0}, Landroid/widget/FrameLayout;->setVisibility(I)V

    .line 105
    iget-object p1, p0, Lme/embodied/TransitionAnimation$1;->this$0:Lme/embodied/TransitionAnimation;

    iget-object p1, p1, Lme/embodied/TransitionAnimation;->videoView_:Lme/embodied/TransitionAnimation$LoopingVideoView;

    invoke-virtual {p1}, Lme/embodied/TransitionAnimation$LoopingVideoView;->pausePlayback()V

    .line 106
    return-void
.end method
