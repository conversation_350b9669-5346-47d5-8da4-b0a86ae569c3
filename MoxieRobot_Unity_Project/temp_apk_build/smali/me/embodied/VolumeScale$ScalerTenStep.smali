.class Lme/embodied/VolumeScale$ScalerTenStep;
.super Lme/embodied/VolumeScale$ScalerBasic;
.source "VolumeScale.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lme/embodied/VolumeScale;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "ScalerTenStep"
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 1

    .line 102
    const/4 v0, 0x0

    invoke-direct {p0, v0}, Lme/embodied/VolumeScale$ScalerBasic;-><init>(Lme/embodied/VolumeScale$1;)V

    return-void
.end method

.method synthetic constructor <init>(Lme/embodied/VolumeScale$1;)V
    .locals 0

    .line 102
    invoke-direct {p0}, Lme/embodied/VolumeScale$ScalerTenStep;-><init>()V

    return-void
.end method


# virtual methods
.method public scaleToPercent(I)I
    .locals 1

    .line 111
    sget v0, Lme/embodied/VolumeScale;->VOLUME_FLOOR:I

    sub-int/2addr p1, v0

    add-int/lit8 p1, p1, 0x1

    mul-int/lit8 p1, p1, 0xa

    return p1
.end method

.method public scaleToSystem(I)I
    .locals 1

    .line 106
    sget v0, Lme/embodied/VolumeScale;->VOLUME_FLOOR:I

    add-int/lit8 p1, p1, -0x1

    div-int/lit8 p1, p1, 0xa

    add-int/2addr v0, p1

    return v0
.end method
