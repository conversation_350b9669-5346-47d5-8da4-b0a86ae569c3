.class public Lorg/slf4j/impl/SimpleLoggerConfiguration;
.super Ljava/lang/Object;
.source "SimpleLoggerConfiguration.java"


# static fields
.field private static final CACHE_OUTPUT_STREAM_DEFAULT:Z = false

.field private static final CONFIGURATION_FILE:Ljava/lang/String; = "simplelogger.properties"

.field private static final DATE_TIME_FORMAT_STR_DEFAULT:Ljava/lang/String;

.field static DEFAULT_LOG_LEVEL_DEFAULT:I = 0x0

.field private static final LEVEL_IN_BRACKETS_DEFAULT:Z = false

.field private static LOG_FILE_DEFAULT:Ljava/lang/String; = null

.field private static final SHOW_DATE_TIME_DEFAULT:Z = false

.field static final SHOW_LOG_NAME_DEFAULT:Z = true

.field private static final SHOW_SHORT_LOG_NAME_DEFAULT:Z = false

.field private static final SHOW_THREAD_NAME_DEFAULT:Z = true

.field private static final WARN_LEVELS_STRING_DEFAULT:Ljava/lang/String; = "WARN"

.field private static dateTimeFormatStr:Ljava/lang/String;


# instance fields
.field private cacheOutputStream:Z

.field dateFormatter:Ljava/text/DateFormat;

.field defaultLogLevel:I

.field levelInBrackets:Z

.field private logFile:Ljava/lang/String;

.field outputChoice:Lorg/slf4j/impl/OutputChoice;

.field private final properties:Ljava/util/Properties;

.field showDateTime:Z

.field showLogName:Z

.field showShortLogName:Z

.field showThreadName:Z

.field warnLevelString:Ljava/lang/String;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 34
    const/16 v0, 0x14

    sput v0, Lorg/slf4j/impl/SimpleLoggerConfiguration;->DEFAULT_LOG_LEVEL_DEFAULT:I

    .line 40
    const/4 v0, 0x0

    sput-object v0, Lorg/slf4j/impl/SimpleLoggerConfiguration;->DATE_TIME_FORMAT_STR_DEFAULT:Ljava/lang/String;

    .line 41
    sput-object v0, Lorg/slf4j/impl/SimpleLoggerConfiguration;->dateTimeFormatStr:Ljava/lang/String;

    .line 57
    const-string v0, "System.err"

    sput-object v0, Lorg/slf4j/impl/SimpleLoggerConfiguration;->LOG_FILE_DEFAULT:Ljava/lang/String;

    return-void
.end method

.method public constructor <init>()V
    .locals 3

    .line 30
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 35
    sget v0, Lorg/slf4j/impl/SimpleLoggerConfiguration;->DEFAULT_LOG_LEVEL_DEFAULT:I

    iput v0, p0, Lorg/slf4j/impl/SimpleLoggerConfiguration;->defaultLogLevel:I

    .line 38
    const/4 v0, 0x0

    iput-boolean v0, p0, Lorg/slf4j/impl/SimpleLoggerConfiguration;->showDateTime:Z

    .line 43
    const/4 v1, 0x0

    iput-object v1, p0, Lorg/slf4j/impl/SimpleLoggerConfiguration;->dateFormatter:Ljava/text/DateFormat;

    .line 46
    const/4 v2, 0x1

    iput-boolean v2, p0, Lorg/slf4j/impl/SimpleLoggerConfiguration;->showThreadName:Z

    .line 49
    iput-boolean v2, p0, Lorg/slf4j/impl/SimpleLoggerConfiguration;->showLogName:Z

    .line 52
    iput-boolean v0, p0, Lorg/slf4j/impl/SimpleLoggerConfiguration;->showShortLogName:Z

    .line 55
    iput-boolean v0, p0, Lorg/slf4j/impl/SimpleLoggerConfiguration;->levelInBrackets:Z

    .line 58
    sget-object v2, Lorg/slf4j/impl/SimpleLoggerConfiguration;->LOG_FILE_DEFAULT:Ljava/lang/String;

    iput-object v2, p0, Lorg/slf4j/impl/SimpleLoggerConfiguration;->logFile:Ljava/lang/String;

    .line 59
    iput-object v1, p0, Lorg/slf4j/impl/SimpleLoggerConfiguration;->outputChoice:Lorg/slf4j/impl/OutputChoice;

    .line 62
    iput-boolean v0, p0, Lorg/slf4j/impl/SimpleLoggerConfiguration;->cacheOutputStream:Z

    .line 65
    const-string v0, "WARN"

    iput-object v0, p0, Lorg/slf4j/impl/SimpleLoggerConfiguration;->warnLevelString:Ljava/lang/String;

    .line 67
    new-instance v0, Ljava/util/Properties;

    invoke-direct {v0}, Ljava/util/Properties;-><init>()V

    iput-object v0, p0, Lorg/slf4j/impl/SimpleLoggerConfiguration;->properties:Ljava/util/Properties;

    return-void
.end method

.method private static computeOutputChoice(Ljava/lang/String;Z)Lorg/slf4j/impl/OutputChoice;
    .locals 3
    .param p0, "logFile"    # Ljava/lang/String;
    .param p1, "cacheOutputStream"    # Z

    .line 164
    const-string v0, "System.err"

    invoke-virtual {v0, p0}, Ljava/lang/String;->equalsIgnoreCase(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 165
    if-eqz p1, :cond_0

    .line 166
    new-instance v0, Lorg/slf4j/impl/OutputChoice;

    sget-object v1, Lorg/slf4j/impl/OutputChoice$OutputChoiceType;->CACHED_SYS_ERR:Lorg/slf4j/impl/OutputChoice$OutputChoiceType;

    invoke-direct {v0, v1}, Lorg/slf4j/impl/OutputChoice;-><init>(Lorg/slf4j/impl/OutputChoice$OutputChoiceType;)V

    return-object v0

    .line 168
    :cond_0
    new-instance v0, Lorg/slf4j/impl/OutputChoice;

    sget-object v1, Lorg/slf4j/impl/OutputChoice$OutputChoiceType;->SYS_ERR:Lorg/slf4j/impl/OutputChoice$OutputChoiceType;

    invoke-direct {v0, v1}, Lorg/slf4j/impl/OutputChoice;-><init>(Lorg/slf4j/impl/OutputChoice$OutputChoiceType;)V

    return-object v0

    .line 169
    :cond_1
    const-string v0, "System.out"

    invoke-virtual {v0, p0}, Ljava/lang/String;->equalsIgnoreCase(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_3

    .line 170
    if-eqz p1, :cond_2

    .line 171
    new-instance v0, Lorg/slf4j/impl/OutputChoice;

    sget-object v1, Lorg/slf4j/impl/OutputChoice$OutputChoiceType;->CACHED_SYS_OUT:Lorg/slf4j/impl/OutputChoice$OutputChoiceType;

    invoke-direct {v0, v1}, Lorg/slf4j/impl/OutputChoice;-><init>(Lorg/slf4j/impl/OutputChoice$OutputChoiceType;)V

    return-object v0

    .line 173
    :cond_2
    new-instance v0, Lorg/slf4j/impl/OutputChoice;

    sget-object v1, Lorg/slf4j/impl/OutputChoice$OutputChoiceType;->SYS_OUT:Lorg/slf4j/impl/OutputChoice$OutputChoiceType;

    invoke-direct {v0, v1}, Lorg/slf4j/impl/OutputChoice;-><init>(Lorg/slf4j/impl/OutputChoice$OutputChoiceType;)V

    return-object v0

    .line 176
    :cond_3
    :try_start_0
    new-instance v0, Ljava/io/FileOutputStream;

    invoke-direct {v0, p0}, Ljava/io/FileOutputStream;-><init>(Ljava/lang/String;)V

    .line 177
    .local v0, "fos":Ljava/io/FileOutputStream;
    new-instance v1, Ljava/io/PrintStream;

    invoke-direct {v1, v0}, Ljava/io/PrintStream;-><init>(Ljava/io/OutputStream;)V

    .line 178
    .local v1, "printStream":Ljava/io/PrintStream;
    new-instance v2, Lorg/slf4j/impl/OutputChoice;

    invoke-direct {v2, v1}, Lorg/slf4j/impl/OutputChoice;-><init>(Ljava/io/PrintStream;)V
    :try_end_0
    .catch Ljava/io/FileNotFoundException; {:try_start_0 .. :try_end_0} :catch_0

    return-object v2

    .line 179
    .end local v0    # "fos":Ljava/io/FileOutputStream;
    .end local v1    # "printStream":Ljava/io/PrintStream;
    :catch_0
    move-exception v0

    .line 180
    .local v0, "e":Ljava/io/FileNotFoundException;
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Could not open ["

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v2, "]. Defaulting to System.err"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1, v0}, Lorg/slf4j/helpers/Util;->report(Ljava/lang/String;Ljava/lang/Throwable;)V

    .line 181
    new-instance v1, Lorg/slf4j/impl/OutputChoice;

    sget-object v2, Lorg/slf4j/impl/OutputChoice$OutputChoiceType;->SYS_ERR:Lorg/slf4j/impl/OutputChoice$OutputChoiceType;

    invoke-direct {v1, v2}, Lorg/slf4j/impl/OutputChoice;-><init>(Lorg/slf4j/impl/OutputChoice$OutputChoiceType;)V

    return-object v1
.end method

.method private loadProperties()V
    .locals 3

    .line 100
    new-instance v0, Lorg/slf4j/impl/SimpleLoggerConfiguration$1;

    invoke-direct {v0, p0}, Lorg/slf4j/impl/SimpleLoggerConfiguration$1;-><init>(Lorg/slf4j/impl/SimpleLoggerConfiguration;)V

    invoke-static {v0}, Ljava/security/AccessController;->doPrivileged(Ljava/security/PrivilegedAction;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/io/InputStream;

    .line 110
    .local v0, "in":Ljava/io/InputStream;
    if-eqz v0, :cond_0

    .line 112
    :try_start_0
    iget-object v1, p0, Lorg/slf4j/impl/SimpleLoggerConfiguration;->properties:Ljava/util/Properties;

    invoke-virtual {v1, v0}, Ljava/util/Properties;->load(Ljava/io/InputStream;)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_2
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 117
    :try_start_1
    invoke-virtual {v0}, Ljava/io/InputStream;->close()V
    :try_end_1
    .catch Ljava/io/IOException; {:try_start_1 .. :try_end_1} :catch_0

    .line 120
    :goto_0
    goto :goto_2

    .line 118
    :catch_0
    move-exception v1

    .line 121
    goto :goto_2

    .line 116
    :catchall_0
    move-exception v1

    .line 117
    :try_start_2
    invoke-virtual {v0}, Ljava/io/InputStream;->close()V
    :try_end_2
    .catch Ljava/io/IOException; {:try_start_2 .. :try_end_2} :catch_1

    .line 120
    goto :goto_1

    .line 118
    :catch_1
    move-exception v2

    .line 120
    :goto_1
    throw v1

    .line 113
    :catch_2
    move-exception v1

    .line 117
    :try_start_3
    invoke-virtual {v0}, Ljava/io/InputStream;->close()V
    :try_end_3
    .catch Ljava/io/IOException; {:try_start_3 .. :try_end_3} :catch_0

    goto :goto_0

    .line 123
    :cond_0
    :goto_2
    return-void
.end method

.method static stringToLevel(Ljava/lang/String;)I
    .locals 2
    .param p0, "levelStr"    # Ljava/lang/String;

    .line 146
    const-string v0, "trace"

    invoke-virtual {v0, p0}, Ljava/lang/String;->equalsIgnoreCase(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 147
    const/4 v0, 0x0

    return v0

    .line 148
    :cond_0
    const-string v0, "debug"

    invoke-virtual {v0, p0}, Ljava/lang/String;->equalsIgnoreCase(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 149
    const/16 v0, 0xa

    return v0

    .line 150
    :cond_1
    const-string v0, "info"

    invoke-virtual {v0, p0}, Ljava/lang/String;->equalsIgnoreCase(Ljava/lang/String;)Z

    move-result v0

    const/16 v1, 0x14

    if-eqz v0, :cond_2

    .line 151
    return v1

    .line 152
    :cond_2
    const-string v0, "warn"

    invoke-virtual {v0, p0}, Ljava/lang/String;->equalsIgnoreCase(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_3

    .line 153
    const/16 v0, 0x1e

    return v0

    .line 154
    :cond_3
    const-string v0, "error"

    invoke-virtual {v0, p0}, Ljava/lang/String;->equalsIgnoreCase(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_4

    .line 155
    const/16 v0, 0x28

    return v0

    .line 156
    :cond_4
    const-string v0, "off"

    invoke-virtual {v0, p0}, Ljava/lang/String;->equalsIgnoreCase(Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_5

    .line 157
    const/16 v0, 0x32

    return v0

    .line 160
    :cond_5
    return v1
.end method


# virtual methods
.method getBooleanProperty(Ljava/lang/String;Z)Z
    .locals 2
    .param p1, "name"    # Ljava/lang/String;
    .param p2, "defaultValue"    # Z

    .line 131
    invoke-virtual {p0, p1}, Lorg/slf4j/impl/SimpleLoggerConfiguration;->getStringProperty(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    .line 132
    .local v0, "prop":Ljava/lang/String;
    if-nez v0, :cond_0

    move v1, p2

    goto :goto_0

    :cond_0
    const-string v1, "true"

    invoke-virtual {v1, v0}, Ljava/lang/String;->equalsIgnoreCase(Ljava/lang/String;)Z

    move-result v1

    :goto_0
    return v1
.end method

.method getStringProperty(Ljava/lang/String;)Ljava/lang/String;
    .locals 2
    .param p1, "name"    # Ljava/lang/String;

    .line 136
    const/4 v0, 0x0

    .line 138
    .local v0, "prop":Ljava/lang/String;
    :try_start_0
    invoke-static {p1}, Ljava/lang/System;->getProperty(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1
    :try_end_0
    .catch Ljava/lang/SecurityException; {:try_start_0 .. :try_end_0} :catch_0

    move-object v0, v1

    .line 141
    goto :goto_0

    .line 139
    :catch_0
    move-exception v1

    .line 142
    :goto_0
    if-nez v0, :cond_0

    iget-object v1, p0, Lorg/slf4j/impl/SimpleLoggerConfiguration;->properties:Ljava/util/Properties;

    invoke-virtual {v1, p1}, Ljava/util/Properties;->getProperty(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    goto :goto_1

    :cond_0
    move-object v1, v0

    :goto_1
    return-object v1
.end method

.method getStringProperty(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
    .locals 2
    .param p1, "name"    # Ljava/lang/String;
    .param p2, "defaultValue"    # Ljava/lang/String;

    .line 126
    invoke-virtual {p0, p1}, Lorg/slf4j/impl/SimpleLoggerConfiguration;->getStringProperty(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    .line 127
    .local v0, "prop":Ljava/lang/String;
    if-nez v0, :cond_0

    move-object v1, p2

    goto :goto_0

    :cond_0
    move-object v1, v0

    :goto_0
    return-object v1
.end method

.method init()V
    .locals 4

    .line 70
    invoke-direct {p0}, Lorg/slf4j/impl/SimpleLoggerConfiguration;->loadProperties()V

    .line 72
    const-string v0, "org.slf4j.simpleLogger.defaultLogLevel"

    const/4 v1, 0x0

    invoke-virtual {p0, v0, v1}, Lorg/slf4j/impl/SimpleLoggerConfiguration;->getStringProperty(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    .line 73
    .local v0, "defaultLogLevelString":Ljava/lang/String;
    if-eqz v0, :cond_0

    .line 74
    invoke-static {v0}, Lorg/slf4j/impl/SimpleLoggerConfiguration;->stringToLevel(Ljava/lang/String;)I

    move-result v1

    iput v1, p0, Lorg/slf4j/impl/SimpleLoggerConfiguration;->defaultLogLevel:I

    .line 76
    :cond_0
    const-string v1, "org.slf4j.simpleLogger.showLogName"

    const/4 v2, 0x1

    invoke-virtual {p0, v1, v2}, Lorg/slf4j/impl/SimpleLoggerConfiguration;->getBooleanProperty(Ljava/lang/String;Z)Z

    move-result v1

    iput-boolean v1, p0, Lorg/slf4j/impl/SimpleLoggerConfiguration;->showLogName:Z

    .line 77
    const-string v1, "org.slf4j.simpleLogger.showShortLogName"

    const/4 v3, 0x0

    invoke-virtual {p0, v1, v3}, Lorg/slf4j/impl/SimpleLoggerConfiguration;->getBooleanProperty(Ljava/lang/String;Z)Z

    move-result v1

    iput-boolean v1, p0, Lorg/slf4j/impl/SimpleLoggerConfiguration;->showShortLogName:Z

    .line 78
    const-string v1, "org.slf4j.simpleLogger.showDateTime"

    invoke-virtual {p0, v1, v3}, Lorg/slf4j/impl/SimpleLoggerConfiguration;->getBooleanProperty(Ljava/lang/String;Z)Z

    move-result v1

    iput-boolean v1, p0, Lorg/slf4j/impl/SimpleLoggerConfiguration;->showDateTime:Z

    .line 79
    const-string v1, "org.slf4j.simpleLogger.showThreadName"

    invoke-virtual {p0, v1, v2}, Lorg/slf4j/impl/SimpleLoggerConfiguration;->getBooleanProperty(Ljava/lang/String;Z)Z

    move-result v1

    iput-boolean v1, p0, Lorg/slf4j/impl/SimpleLoggerConfiguration;->showThreadName:Z

    .line 80
    sget-object v1, Lorg/slf4j/impl/SimpleLoggerConfiguration;->DATE_TIME_FORMAT_STR_DEFAULT:Ljava/lang/String;

    const-string v2, "org.slf4j.simpleLogger.dateTimeFormat"

    invoke-virtual {p0, v2, v1}, Lorg/slf4j/impl/SimpleLoggerConfiguration;->getStringProperty(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    sput-object v1, Lorg/slf4j/impl/SimpleLoggerConfiguration;->dateTimeFormatStr:Ljava/lang/String;

    .line 81
    const-string v1, "org.slf4j.simpleLogger.levelInBrackets"

    invoke-virtual {p0, v1, v3}, Lorg/slf4j/impl/SimpleLoggerConfiguration;->getBooleanProperty(Ljava/lang/String;Z)Z

    move-result v1

    iput-boolean v1, p0, Lorg/slf4j/impl/SimpleLoggerConfiguration;->levelInBrackets:Z

    .line 82
    const-string v1, "org.slf4j.simpleLogger.warnLevelString"

    const-string v2, "WARN"

    invoke-virtual {p0, v1, v2}, Lorg/slf4j/impl/SimpleLoggerConfiguration;->getStringProperty(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    iput-object v1, p0, Lorg/slf4j/impl/SimpleLoggerConfiguration;->warnLevelString:Ljava/lang/String;

    .line 84
    iget-object v1, p0, Lorg/slf4j/impl/SimpleLoggerConfiguration;->logFile:Ljava/lang/String;

    const-string v2, "org.slf4j.simpleLogger.logFile"

    invoke-virtual {p0, v2, v1}, Lorg/slf4j/impl/SimpleLoggerConfiguration;->getStringProperty(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    iput-object v1, p0, Lorg/slf4j/impl/SimpleLoggerConfiguration;->logFile:Ljava/lang/String;

    .line 86
    const-string v1, "org.slf4j.simpleLogger.cacheOutputStream"

    invoke-virtual {p0, v1, v3}, Lorg/slf4j/impl/SimpleLoggerConfiguration;->getBooleanProperty(Ljava/lang/String;Z)Z

    move-result v1

    iput-boolean v1, p0, Lorg/slf4j/impl/SimpleLoggerConfiguration;->cacheOutputStream:Z

    .line 87
    iget-object v2, p0, Lorg/slf4j/impl/SimpleLoggerConfiguration;->logFile:Ljava/lang/String;

    invoke-static {v2, v1}, Lorg/slf4j/impl/SimpleLoggerConfiguration;->computeOutputChoice(Ljava/lang/String;Z)Lorg/slf4j/impl/OutputChoice;

    move-result-object v1

    iput-object v1, p0, Lorg/slf4j/impl/SimpleLoggerConfiguration;->outputChoice:Lorg/slf4j/impl/OutputChoice;

    .line 89
    sget-object v1, Lorg/slf4j/impl/SimpleLoggerConfiguration;->dateTimeFormatStr:Ljava/lang/String;

    if-eqz v1, :cond_1

    .line 91
    :try_start_0
    new-instance v1, Ljava/text/SimpleDateFormat;

    sget-object v2, Lorg/slf4j/impl/SimpleLoggerConfiguration;->dateTimeFormatStr:Ljava/lang/String;

    invoke-direct {v1, v2}, Ljava/text/SimpleDateFormat;-><init>(Ljava/lang/String;)V

    iput-object v1, p0, Lorg/slf4j/impl/SimpleLoggerConfiguration;->dateFormatter:Ljava/text/DateFormat;
    :try_end_0
    .catch Ljava/lang/IllegalArgumentException; {:try_start_0 .. :try_end_0} :catch_0

    .line 94
    goto :goto_0

    .line 92
    :catch_0
    move-exception v1

    .line 93
    .local v1, "e":Ljava/lang/IllegalArgumentException;
    const-string v2, "Bad date format in simplelogger.properties; will output relative time"

    invoke-static {v2, v1}, Lorg/slf4j/helpers/Util;->report(Ljava/lang/String;Ljava/lang/Throwable;)V

    .line 96
    .end local v1    # "e":Ljava/lang/IllegalArgumentException;
    :cond_1
    :goto_0
    return-void
.end method
