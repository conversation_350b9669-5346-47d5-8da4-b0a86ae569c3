.class public Lorg/slf4j/impl/SimpleLogger;
.super Lorg/slf4j/helpers/MarkerIgnoringBase;
.source "SimpleLogger.java"


# static fields
.field public static final CACHE_OUTPUT_STREAM_STRING_KEY:Ljava/lang/String; = "org.slf4j.simpleLogger.cacheOutputStream"

.field private static final CONFIG_PARAMS:Lorg/slf4j/impl/SimpleLoggerConfiguration;

.field public static final DATE_TIME_FORMAT_KEY:Ljava/lang/String; = "org.slf4j.simpleLogger.dateTimeFormat"

.field public static final DEFAULT_LOG_LEVEL_KEY:Ljava/lang/String; = "org.slf4j.simpleLogger.defaultLogLevel"

.field private static INITIALIZED:Z = false

.field public static final LEVEL_IN_BRACKETS_KEY:Ljava/lang/String; = "org.slf4j.simpleLogger.levelInBrackets"

.field public static final LOG_FILE_KEY:Ljava/lang/String; = "org.slf4j.simpleLogger.logFile"

.field public static final LOG_KEY_PREFIX:Ljava/lang/String; = "org.slf4j.simpleLogger.log."

.field protected static final LOG_LEVEL_DEBUG:I = 0xa

.field protected static final LOG_LEVEL_ERROR:I = 0x28

.field protected static final LOG_LEVEL_INFO:I = 0x14

.field protected static final LOG_LEVEL_OFF:I = 0x32

.field protected static final LOG_LEVEL_TRACE:I = 0x0

.field protected static final LOG_LEVEL_WARN:I = 0x1e

.field public static final SHOW_DATE_TIME_KEY:Ljava/lang/String; = "org.slf4j.simpleLogger.showDateTime"

.field public static final SHOW_LOG_NAME_KEY:Ljava/lang/String; = "org.slf4j.simpleLogger.showLogName"

.field public static final SHOW_SHORT_LOG_NAME_KEY:Ljava/lang/String; = "org.slf4j.simpleLogger.showShortLogName"

.field public static final SHOW_THREAD_NAME_KEY:Ljava/lang/String; = "org.slf4j.simpleLogger.showThreadName"

.field private static START_TIME:J = 0x0L

.field public static final SYSTEM_PREFIX:Ljava/lang/String; = "org.slf4j.simpleLogger."

.field public static final WARN_LEVEL_STRING_KEY:Ljava/lang/String; = "org.slf4j.simpleLogger.warnLevelString"

.field private static final serialVersionUID:J = -0x8c81e24d6789094L


# instance fields
.field protected currentLogLevel:I

.field private transient shortLogName:Ljava/lang/String;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 147
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    sput-wide v0, Lorg/slf4j/impl/SimpleLogger;->START_TIME:J

    .line 159
    const/4 v0, 0x0

    sput-boolean v0, Lorg/slf4j/impl/SimpleLogger;->INITIALIZED:Z

    .line 160
    new-instance v0, Lorg/slf4j/impl/SimpleLoggerConfiguration;

    invoke-direct {v0}, Lorg/slf4j/impl/SimpleLoggerConfiguration;-><init>()V

    sput-object v0, Lorg/slf4j/impl/SimpleLogger;->CONFIG_PARAMS:Lorg/slf4j/impl/SimpleLoggerConfiguration;

    return-void
.end method

.method constructor <init>(Ljava/lang/String;)V
    .locals 2
    .param p1, "name"    # Ljava/lang/String;

    .line 213
    invoke-direct {p0}, Lorg/slf4j/helpers/MarkerIgnoringBase;-><init>()V

    .line 177
    const/16 v0, 0x14

    iput v0, p0, Lorg/slf4j/impl/SimpleLogger;->currentLogLevel:I

    .line 179
    const/4 v0, 0x0

    iput-object v0, p0, Lorg/slf4j/impl/SimpleLogger;->shortLogName:Ljava/lang/String;

    .line 214
    iput-object p1, p0, Lorg/slf4j/impl/SimpleLogger;->name:Ljava/lang/String;

    .line 216
    invoke-virtual {p0}, Lorg/slf4j/impl/SimpleLogger;->recursivelyComputeLevelString()Ljava/lang/String;

    move-result-object v0

    .line 217
    .local v0, "levelString":Ljava/lang/String;
    if-eqz v0, :cond_0

    .line 218
    invoke-static {v0}, Lorg/slf4j/impl/SimpleLoggerConfiguration;->stringToLevel(Ljava/lang/String;)I

    move-result v1

    iput v1, p0, Lorg/slf4j/impl/SimpleLogger;->currentLogLevel:I

    goto :goto_0

    .line 220
    :cond_0
    sget-object v1, Lorg/slf4j/impl/SimpleLogger;->CONFIG_PARAMS:Lorg/slf4j/impl/SimpleLoggerConfiguration;

    iget v1, v1, Lorg/slf4j/impl/SimpleLoggerConfiguration;->defaultLogLevel:I

    iput v1, p0, Lorg/slf4j/impl/SimpleLogger;->currentLogLevel:I

    .line 222
    :goto_0
    return-void
.end method

.method private computeShortName()Ljava/lang/String;
    .locals 3

    .line 348
    iget-object v0, p0, Lorg/slf4j/impl/SimpleLogger;->name:Ljava/lang/String;

    iget-object v1, p0, Lorg/slf4j/impl/SimpleLogger;->name:Ljava/lang/String;

    const-string v2, "."

    invoke-virtual {v1, v2}, Ljava/lang/String;->lastIndexOf(Ljava/lang/String;)I

    move-result v1

    add-int/lit8 v1, v1, 0x1

    invoke-virtual {v0, v1}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method private formatAndLog(ILjava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 3
    .param p1, "level"    # I
    .param p2, "format"    # Ljava/lang/String;
    .param p3, "arg1"    # Ljava/lang/Object;
    .param p4, "arg2"    # Ljava/lang/Object;

    .line 360
    invoke-virtual {p0, p1}, Lorg/slf4j/impl/SimpleLogger;->isLevelEnabled(I)Z

    move-result v0

    if-nez v0, :cond_0

    .line 361
    return-void

    .line 363
    :cond_0
    invoke-static {p2, p3, p4}, Lorg/slf4j/helpers/MessageFormatter;->format(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)Lorg/slf4j/helpers/FormattingTuple;

    move-result-object v0

    .line 364
    .local v0, "tp":Lorg/slf4j/helpers/FormattingTuple;
    invoke-virtual {v0}, Lorg/slf4j/helpers/FormattingTuple;->getMessage()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0}, Lorg/slf4j/helpers/FormattingTuple;->getThrowable()Ljava/lang/Throwable;

    move-result-object v2

    invoke-direct {p0, p1, v1, v2}, Lorg/slf4j/impl/SimpleLogger;->log(ILjava/lang/String;Ljava/lang/Throwable;)V

    .line 365
    return-void
.end method

.method private varargs formatAndLog(ILjava/lang/String;[Ljava/lang/Object;)V
    .locals 3
    .param p1, "level"    # I
    .param p2, "format"    # Ljava/lang/String;
    .param p3, "arguments"    # [Ljava/lang/Object;

    .line 376
    invoke-virtual {p0, p1}, Lorg/slf4j/impl/SimpleLogger;->isLevelEnabled(I)Z

    move-result v0

    if-nez v0, :cond_0

    .line 377
    return-void

    .line 379
    :cond_0
    invoke-static {p2, p3}, Lorg/slf4j/helpers/MessageFormatter;->arrayFormat(Ljava/lang/String;[Ljava/lang/Object;)Lorg/slf4j/helpers/FormattingTuple;

    move-result-object v0

    .line 380
    .local v0, "tp":Lorg/slf4j/helpers/FormattingTuple;
    invoke-virtual {v0}, Lorg/slf4j/helpers/FormattingTuple;->getMessage()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0}, Lorg/slf4j/helpers/FormattingTuple;->getThrowable()Ljava/lang/Throwable;

    move-result-object v2

    invoke-direct {p0, p1, v1, v2}, Lorg/slf4j/impl/SimpleLogger;->log(ILjava/lang/String;Ljava/lang/Throwable;)V

    .line 381
    return-void
.end method

.method private getFormattedDate()Ljava/lang/String;
    .locals 3

    .line 339
    new-instance v0, Ljava/util/Date;

    invoke-direct {v0}, Ljava/util/Date;-><init>()V

    .line 341
    .local v0, "now":Ljava/util/Date;
    sget-object v1, Lorg/slf4j/impl/SimpleLogger;->CONFIG_PARAMS:Lorg/slf4j/impl/SimpleLoggerConfiguration;

    iget-object v1, v1, Lorg/slf4j/impl/SimpleLoggerConfiguration;->dateFormatter:Ljava/text/DateFormat;

    monitor-enter v1

    .line 342
    :try_start_0
    sget-object v2, Lorg/slf4j/impl/SimpleLogger;->CONFIG_PARAMS:Lorg/slf4j/impl/SimpleLoggerConfiguration;

    iget-object v2, v2, Lorg/slf4j/impl/SimpleLoggerConfiguration;->dateFormatter:Ljava/text/DateFormat;

    invoke-virtual {v2, v0}, Ljava/text/DateFormat;->format(Ljava/util/Date;)Ljava/lang/String;

    move-result-object v2

    .line 343
    .local v2, "dateText":Ljava/lang/String;
    monitor-exit v1

    .line 344
    return-object v2

    .line 343
    .end local v2    # "dateText":Ljava/lang/String;
    :catchall_0
    move-exception v2

    monitor-exit v1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw v2
.end method

.method static init()V
    .locals 1

    .line 173
    sget-object v0, Lorg/slf4j/impl/SimpleLogger;->CONFIG_PARAMS:Lorg/slf4j/impl/SimpleLoggerConfiguration;

    invoke-virtual {v0}, Lorg/slf4j/impl/SimpleLoggerConfiguration;->init()V

    .line 174
    return-void
.end method

.method static lazyInit()V
    .locals 1

    .line 163
    sget-boolean v0, Lorg/slf4j/impl/SimpleLogger;->INITIALIZED:Z

    if-eqz v0, :cond_0

    .line 164
    return-void

    .line 166
    :cond_0
    const/4 v0, 0x1

    sput-boolean v0, Lorg/slf4j/impl/SimpleLogger;->INITIALIZED:Z

    .line 167
    invoke-static {}, Lorg/slf4j/impl/SimpleLogger;->init()V

    .line 168
    return-void
.end method

.method private log(ILjava/lang/String;Ljava/lang/Throwable;)V
    .locals 6
    .param p1, "level"    # I
    .param p2, "message"    # Ljava/lang/String;
    .param p3, "t"    # Ljava/lang/Throwable;

    .line 248
    invoke-virtual {p0, p1}, Lorg/slf4j/impl/SimpleLogger;->isLevelEnabled(I)Z

    move-result v0

    if-nez v0, :cond_0

    .line 249
    return-void

    .line 252
    :cond_0
    new-instance v0, Ljava/lang/StringBuilder;

    const/16 v1, 0x20

    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(I)V

    .line 255
    .local v0, "buf":Ljava/lang/StringBuilder;
    sget-object v2, Lorg/slf4j/impl/SimpleLogger;->CONFIG_PARAMS:Lorg/slf4j/impl/SimpleLoggerConfiguration;

    iget-boolean v2, v2, Lorg/slf4j/impl/SimpleLoggerConfiguration;->showDateTime:Z

    if-eqz v2, :cond_2

    .line 256
    sget-object v2, Lorg/slf4j/impl/SimpleLogger;->CONFIG_PARAMS:Lorg/slf4j/impl/SimpleLoggerConfiguration;

    iget-object v2, v2, Lorg/slf4j/impl/SimpleLoggerConfiguration;->dateFormatter:Ljava/text/DateFormat;

    if-eqz v2, :cond_1

    .line 257
    invoke-direct {p0}, Lorg/slf4j/impl/SimpleLogger;->getFormattedDate()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 258
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    goto :goto_0

    .line 260
    :cond_1
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v2

    sget-wide v4, Lorg/slf4j/impl/SimpleLogger;->START_TIME:J

    sub-long/2addr v2, v4

    invoke-virtual {v0, v2, v3}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    .line 261
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 266
    :cond_2
    :goto_0
    sget-object v2, Lorg/slf4j/impl/SimpleLogger;->CONFIG_PARAMS:Lorg/slf4j/impl/SimpleLoggerConfiguration;

    iget-boolean v2, v2, Lorg/slf4j/impl/SimpleLoggerConfiguration;->showThreadName:Z

    const/16 v3, 0x5b

    if-eqz v2, :cond_3

    .line 267
    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 268
    invoke-static {}, Ljava/lang/Thread;->currentThread()Ljava/lang/Thread;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/Thread;->getName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 269
    const-string v2, "] "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 272
    :cond_3
    sget-object v2, Lorg/slf4j/impl/SimpleLogger;->CONFIG_PARAMS:Lorg/slf4j/impl/SimpleLoggerConfiguration;

    iget-boolean v2, v2, Lorg/slf4j/impl/SimpleLoggerConfiguration;->levelInBrackets:Z

    if-eqz v2, :cond_4

    .line 273
    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 276
    :cond_4
    invoke-virtual {p0, p1}, Lorg/slf4j/impl/SimpleLogger;->renderLevel(I)Ljava/lang/String;

    move-result-object v2

    .line 277
    .local v2, "levelStr":Ljava/lang/String;
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 278
    sget-object v3, Lorg/slf4j/impl/SimpleLogger;->CONFIG_PARAMS:Lorg/slf4j/impl/SimpleLoggerConfiguration;

    iget-boolean v3, v3, Lorg/slf4j/impl/SimpleLoggerConfiguration;->levelInBrackets:Z

    if-eqz v3, :cond_5

    .line 279
    const/16 v3, 0x5d

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 280
    :cond_5
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 283
    sget-object v1, Lorg/slf4j/impl/SimpleLogger;->CONFIG_PARAMS:Lorg/slf4j/impl/SimpleLoggerConfiguration;

    iget-boolean v1, v1, Lorg/slf4j/impl/SimpleLoggerConfiguration;->showShortLogName:Z

    const-string v3, " - "

    if-eqz v1, :cond_7

    .line 284
    iget-object v1, p0, Lorg/slf4j/impl/SimpleLogger;->shortLogName:Ljava/lang/String;

    if-nez v1, :cond_6

    .line 285
    invoke-direct {p0}, Lorg/slf4j/impl/SimpleLogger;->computeShortName()Ljava/lang/String;

    move-result-object v1

    iput-object v1, p0, Lorg/slf4j/impl/SimpleLogger;->shortLogName:Ljava/lang/String;

    .line 286
    :cond_6
    iget-object v1, p0, Lorg/slf4j/impl/SimpleLogger;->shortLogName:Ljava/lang/String;

    invoke-static {v1}, Ljava/lang/String;->valueOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_1

    .line 287
    :cond_7
    sget-object v1, Lorg/slf4j/impl/SimpleLogger;->CONFIG_PARAMS:Lorg/slf4j/impl/SimpleLoggerConfiguration;

    iget-boolean v1, v1, Lorg/slf4j/impl/SimpleLoggerConfiguration;->showLogName:Z

    if-eqz v1, :cond_8

    .line 288
    iget-object v1, p0, Lorg/slf4j/impl/SimpleLogger;->name:Ljava/lang/String;

    invoke-static {v1}, Ljava/lang/String;->valueOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 292
    :cond_8
    :goto_1
    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 294
    invoke-virtual {p0, v0, p3}, Lorg/slf4j/impl/SimpleLogger;->write(Ljava/lang/StringBuilder;Ljava/lang/Throwable;)V

    .line 296
    return-void
.end method


# virtual methods
.method public debug(Ljava/lang/String;)V
    .locals 2
    .param p1, "msg"    # Ljava/lang/String;

    .line 447
    const/16 v0, 0xa

    const/4 v1, 0x0

    invoke-direct {p0, v0, p1, v1}, Lorg/slf4j/impl/SimpleLogger;->log(ILjava/lang/String;Ljava/lang/Throwable;)V

    .line 448
    return-void
.end method

.method public debug(Ljava/lang/String;Ljava/lang/Object;)V
    .locals 2
    .param p1, "format"    # Ljava/lang/String;
    .param p2, "param1"    # Ljava/lang/Object;

    .line 455
    const/16 v0, 0xa

    const/4 v1, 0x0

    invoke-direct {p0, v0, p1, p2, v1}, Lorg/slf4j/impl/SimpleLogger;->formatAndLog(ILjava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V

    .line 456
    return-void
.end method

.method public debug(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 1
    .param p1, "format"    # Ljava/lang/String;
    .param p2, "param1"    # Ljava/lang/Object;
    .param p3, "param2"    # Ljava/lang/Object;

    .line 463
    const/16 v0, 0xa

    invoke-direct {p0, v0, p1, p2, p3}, Lorg/slf4j/impl/SimpleLogger;->formatAndLog(ILjava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V

    .line 464
    return-void
.end method

.method public debug(Ljava/lang/String;Ljava/lang/Throwable;)V
    .locals 1
    .param p1, "msg"    # Ljava/lang/String;
    .param p2, "t"    # Ljava/lang/Throwable;

    .line 476
    const/16 v0, 0xa

    invoke-direct {p0, v0, p1, p2}, Lorg/slf4j/impl/SimpleLogger;->log(ILjava/lang/String;Ljava/lang/Throwable;)V

    .line 477
    return-void
.end method

.method public varargs debug(Ljava/lang/String;[Ljava/lang/Object;)V
    .locals 1
    .param p1, "format"    # Ljava/lang/String;
    .param p2, "argArray"    # [Ljava/lang/Object;

    .line 471
    const/16 v0, 0xa

    invoke-direct {p0, v0, p1, p2}, Lorg/slf4j/impl/SimpleLogger;->formatAndLog(ILjava/lang/String;[Ljava/lang/Object;)V

    .line 472
    return-void
.end method

.method public error(Ljava/lang/String;)V
    .locals 2
    .param p1, "msg"    # Ljava/lang/String;

    .line 573
    const/16 v0, 0x28

    const/4 v1, 0x0

    invoke-direct {p0, v0, p1, v1}, Lorg/slf4j/impl/SimpleLogger;->log(ILjava/lang/String;Ljava/lang/Throwable;)V

    .line 574
    return-void
.end method

.method public error(Ljava/lang/String;Ljava/lang/Object;)V
    .locals 2
    .param p1, "format"    # Ljava/lang/String;
    .param p2, "arg"    # Ljava/lang/Object;

    .line 581
    const/16 v0, 0x28

    const/4 v1, 0x0

    invoke-direct {p0, v0, p1, p2, v1}, Lorg/slf4j/impl/SimpleLogger;->formatAndLog(ILjava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V

    .line 582
    return-void
.end method

.method public error(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 1
    .param p1, "format"    # Ljava/lang/String;
    .param p2, "arg1"    # Ljava/lang/Object;
    .param p3, "arg2"    # Ljava/lang/Object;

    .line 589
    const/16 v0, 0x28

    invoke-direct {p0, v0, p1, p2, p3}, Lorg/slf4j/impl/SimpleLogger;->formatAndLog(ILjava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V

    .line 590
    return-void
.end method

.method public error(Ljava/lang/String;Ljava/lang/Throwable;)V
    .locals 1
    .param p1, "msg"    # Ljava/lang/String;
    .param p2, "t"    # Ljava/lang/Throwable;

    .line 602
    const/16 v0, 0x28

    invoke-direct {p0, v0, p1, p2}, Lorg/slf4j/impl/SimpleLogger;->log(ILjava/lang/String;Ljava/lang/Throwable;)V

    .line 603
    return-void
.end method

.method public varargs error(Ljava/lang/String;[Ljava/lang/Object;)V
    .locals 1
    .param p1, "format"    # Ljava/lang/String;
    .param p2, "argArray"    # [Ljava/lang/Object;

    .line 597
    const/16 v0, 0x28

    invoke-direct {p0, v0, p1, p2}, Lorg/slf4j/impl/SimpleLogger;->formatAndLog(ILjava/lang/String;[Ljava/lang/Object;)V

    .line 598
    return-void
.end method

.method public info(Ljava/lang/String;)V
    .locals 2
    .param p1, "msg"    # Ljava/lang/String;

    .line 489
    const/16 v0, 0x14

    const/4 v1, 0x0

    invoke-direct {p0, v0, p1, v1}, Lorg/slf4j/impl/SimpleLogger;->log(ILjava/lang/String;Ljava/lang/Throwable;)V

    .line 490
    return-void
.end method

.method public info(Ljava/lang/String;Ljava/lang/Object;)V
    .locals 2
    .param p1, "format"    # Ljava/lang/String;
    .param p2, "arg"    # Ljava/lang/Object;

    .line 497
    const/16 v0, 0x14

    const/4 v1, 0x0

    invoke-direct {p0, v0, p1, p2, v1}, Lorg/slf4j/impl/SimpleLogger;->formatAndLog(ILjava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V

    .line 498
    return-void
.end method

.method public info(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 1
    .param p1, "format"    # Ljava/lang/String;
    .param p2, "arg1"    # Ljava/lang/Object;
    .param p3, "arg2"    # Ljava/lang/Object;

    .line 505
    const/16 v0, 0x14

    invoke-direct {p0, v0, p1, p2, p3}, Lorg/slf4j/impl/SimpleLogger;->formatAndLog(ILjava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V

    .line 506
    return-void
.end method

.method public info(Ljava/lang/String;Ljava/lang/Throwable;)V
    .locals 1
    .param p1, "msg"    # Ljava/lang/String;
    .param p2, "t"    # Ljava/lang/Throwable;

    .line 518
    const/16 v0, 0x14

    invoke-direct {p0, v0, p1, p2}, Lorg/slf4j/impl/SimpleLogger;->log(ILjava/lang/String;Ljava/lang/Throwable;)V

    .line 519
    return-void
.end method

.method public varargs info(Ljava/lang/String;[Ljava/lang/Object;)V
    .locals 1
    .param p1, "format"    # Ljava/lang/String;
    .param p2, "argArray"    # [Ljava/lang/Object;

    .line 513
    const/16 v0, 0x14

    invoke-direct {p0, v0, p1, p2}, Lorg/slf4j/impl/SimpleLogger;->formatAndLog(ILjava/lang/String;[Ljava/lang/Object;)V

    .line 514
    return-void
.end method

.method public isDebugEnabled()Z
    .locals 1

    .line 439
    const/16 v0, 0xa

    invoke-virtual {p0, v0}, Lorg/slf4j/impl/SimpleLogger;->isLevelEnabled(I)Z

    move-result v0

    return v0
.end method

.method public isErrorEnabled()Z
    .locals 1

    .line 565
    const/16 v0, 0x28

    invoke-virtual {p0, v0}, Lorg/slf4j/impl/SimpleLogger;->isLevelEnabled(I)Z

    move-result v0

    return v0
.end method

.method public isInfoEnabled()Z
    .locals 1

    .line 481
    const/16 v0, 0x14

    invoke-virtual {p0, v0}, Lorg/slf4j/impl/SimpleLogger;->isLevelEnabled(I)Z

    move-result v0

    return v0
.end method

.method protected isLevelEnabled(I)Z
    .locals 1
    .param p1, "logLevel"    # I

    .line 392
    iget v0, p0, Lorg/slf4j/impl/SimpleLogger;->currentLogLevel:I

    if-lt p1, v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public isTraceEnabled()Z
    .locals 1

    .line 397
    const/4 v0, 0x0

    invoke-virtual {p0, v0}, Lorg/slf4j/impl/SimpleLogger;->isLevelEnabled(I)Z

    move-result v0

    return v0
.end method

.method public isWarnEnabled()Z
    .locals 1

    .line 523
    const/16 v0, 0x1e

    invoke-virtual {p0, v0}, Lorg/slf4j/impl/SimpleLogger;->isLevelEnabled(I)Z

    move-result v0

    return v0
.end method

.method public log(Lorg/slf4j/event/LoggingEvent;)V
    .locals 4
    .param p1, "event"    # Lorg/slf4j/event/LoggingEvent;

    .line 606
    invoke-interface {p1}, Lorg/slf4j/event/LoggingEvent;->getLevel()Lorg/slf4j/event/Level;

    move-result-object v0

    invoke-virtual {v0}, Lorg/slf4j/event/Level;->toInt()I

    move-result v0

    .line 608
    .local v0, "levelInt":I
    invoke-virtual {p0, v0}, Lorg/slf4j/impl/SimpleLogger;->isLevelEnabled(I)Z

    move-result v1

    if-nez v1, :cond_0

    .line 609
    return-void

    .line 611
    :cond_0
    invoke-interface {p1}, Lorg/slf4j/event/LoggingEvent;->getMessage()Ljava/lang/String;

    move-result-object v1

    invoke-interface {p1}, Lorg/slf4j/event/LoggingEvent;->getArgumentArray()[Ljava/lang/Object;

    move-result-object v2

    invoke-interface {p1}, Lorg/slf4j/event/LoggingEvent;->getThrowable()Ljava/lang/Throwable;

    move-result-object v3

    invoke-static {v1, v2, v3}, Lorg/slf4j/helpers/MessageFormatter;->arrayFormat(Ljava/lang/String;[Ljava/lang/Object;Ljava/lang/Throwable;)Lorg/slf4j/helpers/FormattingTuple;

    move-result-object v1

    .line 612
    .local v1, "tp":Lorg/slf4j/helpers/FormattingTuple;
    invoke-virtual {v1}, Lorg/slf4j/helpers/FormattingTuple;->getMessage()Ljava/lang/String;

    move-result-object v2

    invoke-interface {p1}, Lorg/slf4j/event/LoggingEvent;->getThrowable()Ljava/lang/Throwable;

    move-result-object v3

    invoke-direct {p0, v0, v2, v3}, Lorg/slf4j/impl/SimpleLogger;->log(ILjava/lang/String;Ljava/lang/Throwable;)V

    .line 613
    return-void
.end method

.method recursivelyComputeLevelString()Ljava/lang/String;
    .locals 6

    .line 225
    iget-object v0, p0, Lorg/slf4j/impl/SimpleLogger;->name:Ljava/lang/String;

    .line 226
    .local v0, "tempName":Ljava/lang/String;
    const/4 v1, 0x0

    .line 227
    .local v1, "levelString":Ljava/lang/String;
    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v2

    .line 228
    .local v2, "indexOfLastDot":I
    :goto_0
    if-nez v1, :cond_0

    const/4 v3, -0x1

    if-le v2, v3, :cond_0

    .line 229
    const/4 v3, 0x0

    invoke-virtual {v0, v3, v2}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v0

    .line 230
    sget-object v3, Lorg/slf4j/impl/SimpleLogger;->CONFIG_PARAMS:Lorg/slf4j/impl/SimpleLoggerConfiguration;

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "org.slf4j.simpleLogger.log."

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    const/4 v5, 0x0

    invoke-virtual {v3, v4, v5}, Lorg/slf4j/impl/SimpleLoggerConfiguration;->getStringProperty(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    .line 231
    invoke-static {v0}, Ljava/lang/String;->valueOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v3

    const-string v4, "."

    invoke-virtual {v3, v4}, Ljava/lang/String;->lastIndexOf(Ljava/lang/String;)I

    move-result v2

    goto :goto_0

    .line 233
    :cond_0
    return-object v1
.end method

.method protected renderLevel(I)Ljava/lang/String;
    .locals 3
    .param p1, "level"    # I

    .line 299
    if-eqz p1, :cond_4

    const/16 v0, 0xa

    if-eq p1, v0, :cond_3

    const/16 v0, 0x14

    if-eq p1, v0, :cond_2

    const/16 v0, 0x1e

    if-eq p1, v0, :cond_1

    const/16 v0, 0x28

    if-ne p1, v0, :cond_0

    .line 309
    const-string v0, "ERROR"

    return-object v0

    .line 311
    :cond_0
    new-instance v0, Ljava/lang/IllegalStateException;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Unrecognized level ["

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v2, "]"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 307
    :cond_1
    sget-object v0, Lorg/slf4j/impl/SimpleLogger;->CONFIG_PARAMS:Lorg/slf4j/impl/SimpleLoggerConfiguration;

    iget-object v0, v0, Lorg/slf4j/impl/SimpleLoggerConfiguration;->warnLevelString:Ljava/lang/String;

    return-object v0

    .line 305
    :cond_2
    const-string v0, "INFO"

    return-object v0

    .line 303
    :cond_3
    const-string v0, "DEBUG"

    return-object v0

    .line 301
    :cond_4
    const-string v0, "TRACE"

    return-object v0
.end method

.method public trace(Ljava/lang/String;)V
    .locals 2
    .param p1, "msg"    # Ljava/lang/String;

    .line 405
    const/4 v0, 0x0

    const/4 v1, 0x0

    invoke-direct {p0, v0, p1, v1}, Lorg/slf4j/impl/SimpleLogger;->log(ILjava/lang/String;Ljava/lang/Throwable;)V

    .line 406
    return-void
.end method

.method public trace(Ljava/lang/String;Ljava/lang/Object;)V
    .locals 2
    .param p1, "format"    # Ljava/lang/String;
    .param p2, "param1"    # Ljava/lang/Object;

    .line 413
    const/4 v0, 0x0

    const/4 v1, 0x0

    invoke-direct {p0, v0, p1, p2, v1}, Lorg/slf4j/impl/SimpleLogger;->formatAndLog(ILjava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V

    .line 414
    return-void
.end method

.method public trace(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 1
    .param p1, "format"    # Ljava/lang/String;
    .param p2, "param1"    # Ljava/lang/Object;
    .param p3, "param2"    # Ljava/lang/Object;

    .line 421
    const/4 v0, 0x0

    invoke-direct {p0, v0, p1, p2, p3}, Lorg/slf4j/impl/SimpleLogger;->formatAndLog(ILjava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V

    .line 422
    return-void
.end method

.method public trace(Ljava/lang/String;Ljava/lang/Throwable;)V
    .locals 1
    .param p1, "msg"    # Ljava/lang/String;
    .param p2, "t"    # Ljava/lang/Throwable;

    .line 434
    const/4 v0, 0x0

    invoke-direct {p0, v0, p1, p2}, Lorg/slf4j/impl/SimpleLogger;->log(ILjava/lang/String;Ljava/lang/Throwable;)V

    .line 435
    return-void
.end method

.method public varargs trace(Ljava/lang/String;[Ljava/lang/Object;)V
    .locals 1
    .param p1, "format"    # Ljava/lang/String;
    .param p2, "argArray"    # [Ljava/lang/Object;

    .line 429
    const/4 v0, 0x0

    invoke-direct {p0, v0, p1, p2}, Lorg/slf4j/impl/SimpleLogger;->formatAndLog(ILjava/lang/String;[Ljava/lang/Object;)V

    .line 430
    return-void
.end method

.method public warn(Ljava/lang/String;)V
    .locals 2
    .param p1, "msg"    # Ljava/lang/String;

    .line 531
    const/16 v0, 0x1e

    const/4 v1, 0x0

    invoke-direct {p0, v0, p1, v1}, Lorg/slf4j/impl/SimpleLogger;->log(ILjava/lang/String;Ljava/lang/Throwable;)V

    .line 532
    return-void
.end method

.method public warn(Ljava/lang/String;Ljava/lang/Object;)V
    .locals 2
    .param p1, "format"    # Ljava/lang/String;
    .param p2, "arg"    # Ljava/lang/Object;

    .line 539
    const/16 v0, 0x1e

    const/4 v1, 0x0

    invoke-direct {p0, v0, p1, p2, v1}, Lorg/slf4j/impl/SimpleLogger;->formatAndLog(ILjava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V

    .line 540
    return-void
.end method

.method public warn(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 1
    .param p1, "format"    # Ljava/lang/String;
    .param p2, "arg1"    # Ljava/lang/Object;
    .param p3, "arg2"    # Ljava/lang/Object;

    .line 547
    const/16 v0, 0x1e

    invoke-direct {p0, v0, p1, p2, p3}, Lorg/slf4j/impl/SimpleLogger;->formatAndLog(ILjava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V

    .line 548
    return-void
.end method

.method public warn(Ljava/lang/String;Ljava/lang/Throwable;)V
    .locals 1
    .param p1, "msg"    # Ljava/lang/String;
    .param p2, "t"    # Ljava/lang/Throwable;

    .line 560
    const/16 v0, 0x1e

    invoke-direct {p0, v0, p1, p2}, Lorg/slf4j/impl/SimpleLogger;->log(ILjava/lang/String;Ljava/lang/Throwable;)V

    .line 561
    return-void
.end method

.method public varargs warn(Ljava/lang/String;[Ljava/lang/Object;)V
    .locals 1
    .param p1, "format"    # Ljava/lang/String;
    .param p2, "argArray"    # [Ljava/lang/Object;

    .line 555
    const/16 v0, 0x1e

    invoke-direct {p0, v0, p1, p2}, Lorg/slf4j/impl/SimpleLogger;->formatAndLog(ILjava/lang/String;[Ljava/lang/Object;)V

    .line 556
    return-void
.end method

.method write(Ljava/lang/StringBuilder;Ljava/lang/Throwable;)V
    .locals 3
    .param p1, "buf"    # Ljava/lang/StringBuilder;
    .param p2, "t"    # Ljava/lang/Throwable;

    .line 322
    sget-object v0, Lorg/slf4j/impl/SimpleLogger;->CONFIG_PARAMS:Lorg/slf4j/impl/SimpleLoggerConfiguration;

    iget-object v0, v0, Lorg/slf4j/impl/SimpleLoggerConfiguration;->outputChoice:Lorg/slf4j/impl/OutputChoice;

    invoke-virtual {v0}, Lorg/slf4j/impl/OutputChoice;->getTargetPrintStream()Ljava/io/PrintStream;

    move-result-object v0

    .line 324
    .local v0, "targetStream":Ljava/io/PrintStream;
    sget-object v1, Lorg/slf4j/impl/SimpleLogger;->CONFIG_PARAMS:Lorg/slf4j/impl/SimpleLoggerConfiguration;

    monitor-enter v1

    .line 325
    :try_start_0
    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 326
    invoke-virtual {p0, p2, v0}, Lorg/slf4j/impl/SimpleLogger;->writeThrowable(Ljava/lang/Throwable;Ljava/io/PrintStream;)V

    .line 327
    invoke-virtual {v0}, Ljava/io/PrintStream;->flush()V

    .line 328
    monitor-exit v1

    .line 330
    return-void

    .line 328
    :catchall_0
    move-exception v2

    monitor-exit v1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw v2
.end method

.method protected writeThrowable(Ljava/lang/Throwable;Ljava/io/PrintStream;)V
    .locals 0
    .param p1, "t"    # Ljava/lang/Throwable;
    .param p2, "targetStream"    # Ljava/io/PrintStream;

    .line 333
    if-eqz p1, :cond_0

    .line 334
    invoke-virtual {p1, p2}, Ljava/lang/Throwable;->printStackTrace(Ljava/io/PrintStream;)V

    .line 336
    :cond_0
    return-void
.end method
