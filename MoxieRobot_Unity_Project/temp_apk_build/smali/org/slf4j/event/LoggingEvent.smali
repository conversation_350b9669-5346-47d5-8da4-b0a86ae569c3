.class public interface abstract Lorg/slf4j/event/LoggingEvent;
.super Ljava/lang/Object;
.source "LoggingEvent.java"


# virtual methods
.method public abstract getArgumentArray()[Ljava/lang/Object;
.end method

.method public abstract getLevel()Lorg/slf4j/event/Level;
.end method

.method public abstract getLoggerName()Ljava/lang/String;
.end method

.method public abstract getMarker()Lorg/slf4j/Marker;
.end method

.method public abstract getMessage()Ljava/lang/String;
.end method

.method public abstract getThreadName()Ljava/lang/String;
.end method

.method public abstract getThrowable()Ljava/lang/Throwable;
.end method

.method public abstract getTimeStamp()J
.end method
