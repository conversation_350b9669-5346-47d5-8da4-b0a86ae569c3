.class public interface abstract Lorg/java_websocket/protocols/IProtocol;
.super Ljava/lang/Object;
.source "IProtocol.java"


# virtual methods
.method public abstract acceptProvidedProtocol(Ljava/lang/String;)Z
.end method

.method public abstract copyInstance()Lorg/java_websocket/protocols/IProtocol;
.end method

.method public abstract getProvidedProtocol()Ljava/lang/String;
.end method

.method public abstract toString()Ljava/lang/String;
.end method
