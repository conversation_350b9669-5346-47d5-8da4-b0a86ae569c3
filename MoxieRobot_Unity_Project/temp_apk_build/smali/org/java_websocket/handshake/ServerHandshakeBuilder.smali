.class public interface abstract Lorg/java_websocket/handshake/ServerHandshakeBuilder;
.super Ljava/lang/Object;
.source "ServerHandshakeBuilder.java"

# interfaces
.implements Lorg/java_websocket/handshake/HandshakeBuilder;
.implements Lorg/java_websocket/handshake/ServerHandshake;


# virtual methods
.method public abstract setHttpStatus(S)V
.end method

.method public abstract setHttpStatusMessage(Ljava/lang/String;)V
.end method
