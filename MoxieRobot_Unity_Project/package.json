{"name": "com.embodied.moxierobot.elevenlabs-tts", "version": "1.0.0", "displayName": "MoxieRobot ElevenLabs TTS", "description": "Advanced Text-to-Speech system for MoxieRobot using ElevenLabs API with multi-language support, intelligent caching, and enhanced viseme generation.", "unity": "2019.4", "unityRelease": "0f1", "documentationUrl": "https://github.com/embodied-inc/moxierobot-elevenlabs-tts/blob/main/README.md", "changelogUrl": "https://github.com/embodied-inc/moxierobot-elevenlabs-tts/blob/main/CHANGELOG.md", "licensesUrl": "https://github.com/embodied-inc/moxierobot-elevenlabs-tts/blob/main/LICENSE.md", "keywords": ["tts", "text-to-speech", "elevenlabs", "moxierobot", "voice", "audio", "ai", "speech-synthesis", "visemes", "multilingual"], "author": {"name": "Embodied Inc.", "email": "<EMAIL>", "url": "https://embodied.com"}, "type": "library", "hideInEditor": false, "testables": ["com.embodied.moxierobot.elevenlabs-tts"], "dependencies": {"com.unity.nuget.newtonsoft-json": "3.0.2", "com.unity.modules.audio": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.unitywebrequestmultimedia": "1.0.0"}, "samples": [{"displayName": "Basic TTS Example", "description": "Simple example showing basic TTS functionality", "path": "Samples~/BasicTTSExample"}, {"displayName": "Multi-Language Demo", "description": "Demonstrates multi-language support and voice selection", "path": "Samples~/MultiLanguageDemo"}, {"displayName": "Advanced Visemes", "description": "Shows advanced viseme generation and facial animation", "path": "Samples~/AdvancedVisemes"}, {"displayName": "Migration Example", "description": "Example of migrating from CereVoice to ElevenLabs", "path": "Samples~/MigrationExample"}], "repository": {"type": "git", "url": "https://github.com/embodied-inc/moxierobot-elevenlabs-tts.git"}, "bugs": {"url": "https://github.com/embodied-inc/moxierobot-elevenlabs-tts/issues"}, "homepage": "https://github.com/embodied-inc/moxierobot-elevenlabs-tts#readme", "license": "MIT"}