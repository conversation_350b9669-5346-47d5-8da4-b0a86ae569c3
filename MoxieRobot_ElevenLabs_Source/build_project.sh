#!/bin/bash

# MoxieRobot ElevenLabs TTS - Build Script
# Este script automatiza la construcción y empaquetado del proyecto

set -e  # Exit on any error

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuración
PROJECT_NAME="MoxieRobot_ElevenLabs"
BUILD_DIR="Build"
PACKAGE_DIR="Package"
UNITY_PATH="/Applications/Unity/Hub/Editor/2019.4.40f1/Unity.app/Contents/MacOS/Unity"
LOG_FILE="build.log"

# Función para logging
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a $LOG_FILE
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a $LOG_FILE
    exit 1
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a $LOG_FILE
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a $LOG_FILE
}

# Verificar prerrequisitos
check_prerequisites() {
    log "Verificando prerrequisitos..."
    
    # Verificar Unity
    if [ ! -f "$UNITY_PATH" ]; then
        error "Unity no encontrado en $UNITY_PATH"
    fi
    
    # Verificar estructura del proyecto
    if [ ! -f "Assets/Scripts/TTS/ElevenLabs/ElevenLabsTTSManager.cs" ]; then
        error "Estructura del proyecto incorrecta. Ejecutar desde la raíz del proyecto."
    fi
    
    success "Prerrequisitos verificados"
}

# Limpiar builds anteriores
clean_build() {
    log "Limpiando builds anteriores..."
    
    if [ -d "$BUILD_DIR" ]; then
        rm -rf "$BUILD_DIR"
    fi
    
    if [ -d "$PACKAGE_DIR" ]; then
        rm -rf "$PACKAGE_DIR"
    fi
    
    mkdir -p "$BUILD_DIR"
    mkdir -p "$PACKAGE_DIR"
    
    success "Limpieza completada"
}

# Validar código
validate_code() {
    log "Validando código..."
    
    # Verificar que no hay errores de compilación
    "$UNITY_PATH" -batchmode -quit -projectPath "$(pwd)" -logFile "$LOG_FILE.unity" -executeMethod BuildValidator.ValidateProject
    
    if [ $? -ne 0 ]; then
        error "Errores de compilación encontrados. Ver $LOG_FILE.unity"
    fi
    
    success "Código validado"
}

# Construir para Android
build_android() {
    log "Construyendo para Android..."
    
    "$UNITY_PATH" \
        -batchmode \
        -quit \
        -projectPath "$(pwd)" \
        -buildTarget Android \
        -logFile "$LOG_FILE.android" \
        -executeMethod BuildScript.BuildAndroid \
        -buildPath "$BUILD_DIR/Android/${PROJECT_NAME}.apk"
    
    if [ $? -eq 0 ]; then
        success "Build Android completado: $BUILD_DIR/Android/${PROJECT_NAME}.apk"
    else
        error "Build Android falló. Ver $LOG_FILE.android"
    fi
}

# Construir para iOS
build_ios() {
    log "Construyendo para iOS..."
    
    "$UNITY_PATH" \
        -batchmode \
        -quit \
        -projectPath "$(pwd)" \
        -buildTarget iOS \
        -logFile "$LOG_FILE.ios" \
        -executeMethod BuildScript.BuildiOS \
        -buildPath "$BUILD_DIR/iOS"
    
    if [ $? -eq 0 ]; then
        success "Build iOS completado: $BUILD_DIR/iOS"
    else
        error "Build iOS falló. Ver $LOG_FILE.ios"
    fi
}

# Construir para Windows
build_windows() {
    log "Construyendo para Windows..."
    
    "$UNITY_PATH" \
        -batchmode \
        -quit \
        -projectPath "$(pwd)" \
        -buildTarget StandaloneWindows64 \
        -logFile "$LOG_FILE.windows" \
        -executeMethod BuildScript.BuildWindows \
        -buildPath "$BUILD_DIR/Windows/${PROJECT_NAME}.exe"
    
    if [ $? -eq 0 ]; then
        success "Build Windows completado: $BUILD_DIR/Windows"
    else
        error "Build Windows falló. Ver $LOG_FILE.windows"
    fi
}

# Crear paquete de código fuente
package_source() {
    log "Creando paquete de código fuente..."
    
    # Crear estructura del paquete
    mkdir -p "$PACKAGE_DIR/Source"
    
    # Copiar archivos esenciales
    cp -r Assets/Scripts/TTS "$PACKAGE_DIR/Source/"
    cp -r ProjectSettings "$PACKAGE_DIR/Source/"
    cp README.md "$PACKAGE_DIR/"
    cp MIGRATION_GUIDE.md "$PACKAGE_DIR/"
    cp ANALISIS_CEREVOICE_INTEGRACION.md "$PACKAGE_DIR/"
    cp ARQUITECTURA_ELEVENLABS.md "$PACKAGE_DIR/"
    
    # Crear archivo de instalación
    cat > "$PACKAGE_DIR/INSTALL.md" << EOF
# Instalación MoxieRobot ElevenLabs TTS

## Pasos de Instalación

1. **Copiar Scripts**
   \`\`\`bash
   cp -r Source/TTS/ Assets/Scripts/
   \`\`\`

2. **Configurar Proyecto**
   - Importar configuraciones de ProjectSettings/
   - Configurar API Key de ElevenLabs
   - Añadir componentes a la escena

3. **Verificar Instalación**
   - Ejecutar tests incluidos
   - Verificar funcionalidad básica

Ver README.md y MIGRATION_GUIDE.md para detalles completos.
EOF
    
    # Crear archivo comprimido
    cd "$PACKAGE_DIR"
    tar -czf "../${PROJECT_NAME}_Source_$(date +%Y%m%d).tar.gz" .
    cd ..
    
    success "Paquete de código fuente creado: ${PROJECT_NAME}_Source_$(date +%Y%m%d).tar.gz"
}

# Ejecutar tests
run_tests() {
    log "Ejecutando tests..."
    
    "$UNITY_PATH" \
        -batchmode \
        -quit \
        -projectPath "$(pwd)" \
        -logFile "$LOG_FILE.tests" \
        -runTests \
        -testPlatform PlayMode \
        -testResults "$BUILD_DIR/test_results.xml"
    
    if [ $? -eq 0 ]; then
        success "Tests completados. Ver $BUILD_DIR/test_results.xml"
    else
        warning "Algunos tests fallaron. Ver $LOG_FILE.tests"
    fi
}

# Generar documentación
generate_docs() {
    log "Generando documentación..."
    
    # Crear directorio de documentación
    mkdir -p "$BUILD_DIR/Documentation"
    
    # Copiar documentación existente
    cp README.md "$BUILD_DIR/Documentation/"
    cp MIGRATION_GUIDE.md "$BUILD_DIR/Documentation/"
    cp ANALISIS_CEREVOICE_INTEGRACION.md "$BUILD_DIR/Documentation/"
    cp ARQUITECTURA_ELEVENLABS.md "$BUILD_DIR/Documentation/"
    
    # Generar documentación de API (si xmldoc2md está disponible)
    if command -v xmldoc2md &> /dev/null; then
        log "Generando documentación de API..."
        # xmldoc2md Assets/Scripts/TTS/ "$BUILD_DIR/Documentation/API/"
    fi
    
    success "Documentación generada en $BUILD_DIR/Documentation/"
}

# Función principal
main() {
    log "=== Iniciando build de $PROJECT_NAME ==="
    
    # Verificar argumentos
    BUILD_TARGET=${1:-"all"}
    
    case $BUILD_TARGET in
        "android")
            check_prerequisites
            clean_build
            validate_code
            build_android
            ;;
        "ios")
            check_prerequisites
            clean_build
            validate_code
            build_ios
            ;;
        "windows")
            check_prerequisites
            clean_build
            validate_code
            build_windows
            ;;
        "source")
            check_prerequisites
            package_source
            ;;
        "test")
            check_prerequisites
            run_tests
            ;;
        "docs")
            generate_docs
            ;;
        "all")
            check_prerequisites
            clean_build
            validate_code
            run_tests
            build_android
            # build_ios  # Comentado - requiere macOS
            # build_windows  # Comentado - puede requerir Windows
            package_source
            generate_docs
            ;;
        *)
            echo "Uso: $0 [android|ios|windows|source|test|docs|all]"
            echo ""
            echo "Opciones:"
            echo "  android  - Construir APK para Android"
            echo "  ios      - Construir proyecto Xcode para iOS"
            echo "  windows  - Construir ejecutable para Windows"
            echo "  source   - Crear paquete de código fuente"
            echo "  test     - Ejecutar tests"
            echo "  docs     - Generar documentación"
            echo "  all      - Ejecutar todas las tareas (por defecto)"
            exit 1
            ;;
    esac
    
    success "=== Build completado exitosamente ==="
}

# Ejecutar función principal con argumentos
main "$@"
