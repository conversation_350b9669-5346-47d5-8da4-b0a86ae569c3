# Arquitectura ElevenLabs para MoxieRobot

## Visión General
Reem<PERSON><PERSON>zar el sistema TTS CereVoice con ElevenLabs API para obtener voces más naturales, soporte multiidioma y mejor escalabilidad.

## Componentes Principales

### 1. ElevenLabsTTSManager
**Propósito**: <PERSON>emplazar TTSRequestCloudHandler manteniendo compatibilidad
```csharp
public class ElevenLabsTTSManager : MonoBehaviour
{
    // Configuración
    public string ApiKey { get; set; }
    public string DefaultVoiceId { get; set; }
    public SupportedLanguage DefaultLanguage { get; set; }
    
    // Métodos principales
    public async Task<AudioClip> SynthesizeTextAsync(string text, string voiceId = null, string language = null)
    public async Task<Stream> SynthesizeTextStreamAsync(string text, string voiceId = null)
    public void PlayTTSWithVisemes(string text, Action<VisemeData> onViseme = null)
    
    // Eventos (mantener compatibilidad)
    public event Action<AudioClip> OnTTSCompleteEvent;
    public event Action<TTSStreamData> OnTTSStreamEvent;
}
```

### 2. ElevenLabsAPIClient
**Propósito**: Cliente HTTP para comunicación con ElevenLabs API
```csharp
public class ElevenLabsAPIClient
{
    private readonly HttpClient httpClient;
    private readonly string apiKey;
    private readonly string baseUrl = "https://api.elevenlabs.io/v1";
    
    // Métodos API
    public async Task<VoiceInfo[]> GetAvailableVoicesAsync()
    public async Task<byte[]> TextToSpeechAsync(string text, string voiceId, TTSSettings settings)
    public async Task<Stream> TextToSpeechStreamAsync(string text, string voiceId, TTSSettings settings)
    public async Task<VoiceSettings> GetVoiceSettingsAsync(string voiceId)
}
```

### 3. MultiLanguageSupport
**Propósito**: Gestión de idiomas y localización
```csharp
public class MultiLanguageSupport : MonoBehaviour
{
    public enum SupportedLanguage
    {
        English, Spanish, French, German, Italian, Portuguese, 
        Japanese, Korean, Chinese, Russian, Arabic, Hindi
    }
    
    public Dictionary<SupportedLanguage, VoiceProfile[]> LanguageVoices { get; set; }
    public SupportedLanguage CurrentLanguage { get; set; }
    
    public VoiceProfile GetBestVoiceForLanguage(SupportedLanguage language, VoiceGender gender = VoiceGender.Any)
    public string DetectLanguage(string text)
    public void SetLanguage(SupportedLanguage language)
}
```

### 4. VoiceSelectionManager
**Propósito**: Gestión y selección de voces
```csharp
public class VoiceSelectionManager : MonoBehaviour
{
    public enum VoiceGender { Male, Female, Any }
    public enum VoiceAge { Child, Young, Adult, Senior, Any }
    public enum VoiceStyle { Conversational, Narration, Expressive, Calm, Energetic }
    
    public VoiceProfile[] AvailableVoices { get; private set; }
    public VoiceProfile CurrentVoice { get; set; }
    
    public VoiceProfile SelectVoice(VoiceGender gender, VoiceAge age, VoiceStyle style, SupportedLanguage language)
    public void LoadCustomVoice(string voiceId, VoiceSettings settings)
    public VoiceProfile[] FilterVoices(VoiceFilter filter)
}
```

### 5. AudioCacheManager
**Propósito**: Cache local para optimizar rendimiento y reducir costos
```csharp
public class AudioCacheManager : MonoBehaviour
{
    private readonly Dictionary<string, CachedAudioData> audioCache;
    private readonly string cacheDirectory;
    
    public async Task<AudioClip> GetOrCreateAudioAsync(string text, string voiceId, TTSSettings settings)
    public void CacheAudio(string key, AudioClip audio, VisemeData[] visemes)
    public void ClearCache()
    public void SetCacheSize(long maxSizeBytes)
    public CacheStatistics GetCacheStatistics()
}
```

### 6. Enhanced VisemeGenerator
**Propósito**: Generar datos de visemas para animación facial
```csharp
public class EnhancedVisemeGenerator : MonoBehaviour
{
    // Mapeo de fonemas a visemas
    private readonly Dictionary<string, VisemeType> phonemeToViseme;
    
    public VisemeData[] GenerateVisemesFromText(string text, SupportedLanguage language)
    public VisemeData[] GenerateVisemesFromAudio(AudioClip audio, float[] timestamps)
    public void UpdateVisemeAnimation(VisemeData viseme, float intensity)
}
```

## Flujo de Datos

### 1. Inicialización del Sistema
```
Unity Start → ElevenLabsTTSManager.Initialize() → 
ElevenLabsAPIClient.Setup() → MultiLanguageSupport.LoadLanguages() → 
VoiceSelectionManager.LoadVoices() → AudioCacheManager.Initialize()
```

### 2. Síntesis de Voz
```
Text Input → Language Detection → Voice Selection → 
Cache Check → [Cache Hit: Return Cached] OR [Cache Miss: API Call] → 
Audio Processing → Viseme Generation → Audio Playback + Animation
```

### 3. Streaming en Tiempo Real
```
Text Input → ElevenLabsAPIClient.TextToSpeechStreamAsync() → 
Stream Processing → Real-time Audio Playback → 
Concurrent Viseme Generation → Live Animation Updates
```

## Configuración y Settings

### TTSSettings
```csharp
[Serializable]
public class TTSSettings
{
    public float Stability = 0.5f;        // 0.0 - 1.0
    public float SimilarityBoost = 0.5f;  // 0.0 - 1.0
    public float Style = 0.0f;            // 0.0 - 1.0
    public bool UseSpeakerBoost = true;
    public OutputFormat OutputFormat = OutputFormat.MP3_44100_128;
    public int OptimizeStreamingLatency = 0; // 0-4
}
```

### VoiceProfile
```csharp
[Serializable]
public class VoiceProfile
{
    public string VoiceId;
    public string Name;
    public VoiceGender Gender;
    public VoiceAge Age;
    public SupportedLanguage[] SupportedLanguages;
    public VoiceStyle[] SupportedStyles;
    public TTSSettings DefaultSettings;
    public bool IsCustomVoice;
    public string PreviewUrl;
}
```

## Integración con Sistema Existente

### 1. Mantener Compatibilidad
- Conservar interfaces públicas existentes
- Adaptar eventos y callbacks
- Mantener sistema de visemas

### 2. Mejoras Incrementales
- Implementar cache inteligente
- Añadir soporte streaming
- Optimizar para latencia baja

### 3. Configuración Flexible
- Permitir fallback a sistema anterior
- Configuración por usuario/sesión
- A/B testing capabilities

## Beneficios de la Nueva Arquitectura

### Técnicos
- **Reducción de tamaño**: -44MB (eliminación libcerevoice_eng.so)
- **Mejor mantenimiento**: Código C# puro, sin P/Invoke
- **Escalabilidad**: API cloud vs librería local
- **Flexibilidad**: Fácil actualización de voces y idiomas

### Funcionales
- **Calidad superior**: Voces más naturales y expresivas
- **Multiidioma nativo**: 12+ idiomas soportados
- **Personalización**: Voces custom y ajustes finos
- **Streaming**: Reproducción en tiempo real

### Operacionales
- **Monitoreo**: Métricas de uso y rendimiento
- **Costos controlados**: Cache y límites configurables
- **Actualizaciones**: Sin necesidad de actualizar APK para nuevas voces
