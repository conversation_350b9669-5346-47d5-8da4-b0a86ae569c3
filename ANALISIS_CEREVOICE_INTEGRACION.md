# Análisis de Integración CereVoice en MoxieRobot

## Resumen Ejecutivo
La aplicación MoxieRobot utiliza CereVoice como motor TTS a través de una librería nativa ARM (`libcerevoice_eng.so` - 44MB) integrada con Unity mediante P/Invoke.

## Componentes Identificados

### 1. Librería Nativa
- **Archivo**: `libcerevoice_eng.so` (44,446,420 bytes)
- **Arquitectura**: ARM EABI5 32-bit LSB shared object
- **Ubicación**: `lib/armeabi-v7a/libcerevoice_eng.so`

### 2. Funciones P/Invoke Identificadas
```csharp
// Funciones principales encontradas en Assembly-CSharp.dll
CSharp_CPRCEN_engine_load_voice
CSharp_CPRCEN_engine_unload_voice
CSharp_CPRCEN_engine_load_voice_licensestr
CSharp_CPRCEN_engine_load_config

// Callbacks SWIG
SWIGRegisterExceptionCallbacks_cerevoice_eng
SWIGRegisterExceptionArgumentCallbacks_cerevoice_eng
SWIGRegisterStringCallback_cerevoice_eng
```

### 3. Clases C# Relacionadas
```csharp
// Clases identificadas en el análisis de strings
cerevoice_engPINVOKE          // Clase principal P/Invoke
CereVoicePhoneSetResource     // Recursos de fonemas
CereVoicePhoneme             // Manejo de fonemas individuales
CereVoicePhoneSet            // Conjunto de fonemas
```

### 4. Sistema TTS Actual
```csharp
// Componentes del sistema TTS
TTSRequestCloudHandler        // Manejador principal de TTS
TTSSpeechMarkPlayback        // Reproducción con marcas de tiempo
RobotGameTaskAnimLayer_Viseme // Animación de visemas
OnTTSCompleteEvent           // Eventos de finalización
OnTTSStreamEvent             // Eventos de streaming
```

## Flujo de Funcionamiento Actual

### 1. Inicialización
1. Unity carga `libcerevoice_eng.so` automáticamente
2. Se registran callbacks SWIG para manejo de excepciones
3. Se cargan recursos de fonemas (`CereVoicePhoneSetResource`)
4. Se configura el motor con `CPRCEN_engine_load_config`

### 2. Síntesis de Voz
1. `TTSRequestCloudHandler` recibe texto para sintetizar
2. Se invoca `CPRCEN_engine_load_voice` con configuración
3. CereVoice procesa el texto y genera audio
4. Se extraen marcas de tiempo y fonemas para visemas
5. Audio se pasa al pipeline de `libbo-audio.so`

### 3. Reproducción y Animación
1. `TTSSpeechMarkPlayback` sincroniza audio con animaciones
2. `RobotGameTaskAnimLayer_Viseme` anima la boca del robot
3. `OnTTSStreamEvent` maneja eventos en tiempo real
4. `OnTTSCompleteEvent` finaliza la secuencia

## Dependencias Críticas

### Archivos que deben modificarse:
- `Assembly-CSharp.dll` - Lógica principal TTS
- `AndroidManifest.xml` - Permisos y configuración
- Recursos de fonemas en `assets/bin/Data/`

### Librerías que permanecen:
- `libbo-audio.so` - Pipeline de audio (184MB)
- `libbo-brain.so` - Lógica del cerebro IA (154MB)
- `libunity.so` - Motor Unity

## Puntos de Integración para ElevenLabs

### 1. Reemplazar P/Invoke
- Eliminar `cerevoice_engPINVOKE`
- Crear `ElevenLabsAPIClient` con HTTP/WebSocket

### 2. Mantener Interfaces
- Conservar `TTSRequestCloudHandler` como interfaz
- Adaptar `TTSSpeechMarkPlayback` para nuevos datos
- Actualizar `RobotGameTaskAnimLayer_Viseme`

### 3. Nuevas Capacidades
- Soporte multiidioma nativo
- Streaming en tiempo real
- Voces personalizables
- Mejor calidad de audio

## Beneficios de la Migración

### Técnicos
- Reducción de 44MB en tamaño APK
- Eliminación de dependencia nativa ARM
- Mejor escalabilidad y mantenimiento
- Soporte nativo para múltiples idiomas

### Funcionales
- Voces más naturales y expresivas
- Soporte para más idiomas
- Personalización de voces
- Mejor rendimiento en streaming

## Riesgos y Consideraciones

### Dependencia de Red
- Requiere conexión a internet estable
- Implementar cache local para frases comunes
- Manejo de errores de conectividad

### Latencia
- Posible aumento en latencia inicial
- Mitigar con streaming y pre-carga
- Optimizar con cache inteligente

### Costos
- API de ElevenLabs tiene costos por uso
- Implementar límites y monitoreo
- Considerar planes empresariales
